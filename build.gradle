plugins {
	id 'fabric-loom' version '1.10-SNAPSHOT'
}

repositories {
	maven { url = 'https://masa.dy.fi/maven' }
	maven { url = 'https://maven.fallenbreath.me/releases' }
	maven { url = 'https://maven.terraformersmc.com/releases/' }
	maven { url = 'https://jitpack.io' }
}

dependencies {
	minecraft "com.mojang:minecraft:${project.minecraft_version}"
	mappings "net.fabricmc:yarn:${project.mappings_version}:v2"
	modImplementation "net.fabricmc:fabric-loader:${project.fabric_loader_version}"
	implementation "com.google.code.findbugs:jsr305:3.0.2"

	//modImplementation "fi.dy.masa.malilib:malilib-fabric-${project.minecraft_version_out}:${project.malilib_version}"
	modImplementation "com.github.sakura-ryoko:malilib:${project.malilib_version}"

	// Fabric API. This is technically optional, but you probably want it anyway.
	//include(modApi(fabricApi.module("fabric-api-base", project.fabric_api_version)))

	modCompileOnly "com.terraformersmc:modmenu:${project.mod_menu_version}"
//	modRuntimeOnly 'me.fallenbreath:mixin-auditor:0.1.0'
}

base {
	group = project.group + "." + project.mod_id
	archivesName = project.mod_file_name + '-' + project.minecraft_version_out
	version = project.mod_version

	if (version.endsWith('-dev')) {
		version += "." + new Date().format('yyyyMMdd.HHmmss')
	}
}

loom {
//	def commonVmArgs = ['-Dmixin.debug.export=true', '-Dmixin.debug.countInjections=true']
//	// [FEATURE] MIXIN_AUDITOR
//	runs {
//		def auditVmArgs = [*commonVmArgs, '-DmixinAuditor.audit=true']
//		clientMixinAudit {
//			client()
//			vmArgs auditVmArgs
//			ideConfigGenerated false
//		}
//	}
}

processResources {
	// Exclude the GIMP image files
	exclude '**/*.xcf'
	exclude '**/xcf'

	// this will ensure that this task is redone when the versions change.
	//inputs.property "minecraft_version", project.project.minecraft_version

	inputs.property "mod_version", project.mod_version

	filesMatching("fabric.mod.json") {
		expand "mod_version": project.mod_version
	}
}

tasks.withType(JavaCompile).configureEach {
	// ensure that the encoding is set to UTF-8, no matter what the system default is
	// this fixes some edge cases with special characters not displaying correctly
	// see http://yodaconditions.net/blog/fix-for-java-file-encoding-problems-with-gradle.html
	// If Javadoc is generated, this must be specified in that task too.
	it.options.encoding = "UTF-8"

	// Minecraft 1.20.5/1.21 (24w14a) upwards uses Java 21.
	it.options.release = 21
}

tasks.withType(AbstractArchiveTask).configureEach {
	preserveFileTimestamps = true
	//reproducibleFileOrder = true
}

java {
	sourceCompatibility = JavaVersion.VERSION_21
	targetCompatibility = JavaVersion.VERSION_21
}

jar {
	from("LICENSE") {
		rename { "${it}_${base.archivesName}"}
	}
}
