# Automatically build the project and run any configured tests for every push
# and submitted pull request. This can help catch issues that only occur on
# certain platforms or Java versions, and provides a first line of defence
# against bad commits.

name: build
on: [ pull_request, push ]

jobs:
  build:
    strategy:
      matrix:
        # Use these Java versions
        java: [ 21 ]
        distro: [ temurin ]
        # and run on both Linux and Windows
        os: [ ubuntu-latest ]
    runs-on: ${{ matrix.os }}
    steps:
      - name: checkout repository
        uses: actions/checkout@v4
      - name: validate gradle wrapper
        uses: gradle/actions/wrapper-validation@v4
      - name: setup jdk ${{ matrix.java }}
        uses: actions/setup-java@v4
        with:
          distribution: ${{ matrix.distro }}
          java-version: ${{ matrix.java }}
      - name: make gradle wrapper executable
        if: ${{ runner.os != 'Windows' }}
        run: chmod +x ./gradlew
      - name: build
        run: ./gradlew build
      - name: capture build artifacts
        if: ${{ runner.os == 'Linux' }}
        uses: actions/upload-artifact@v4
        with:
          name: Artifacts
          path: build/libs/
