[![](https://jitpack.io/v/sakura-ryoko/tweakeroo.svg)](https://jitpack.io/#sakura-ryoko/tweakeroo)

Tweakeroo
==============
Tweakeroo is a client-side-only Minecraft mod using LiteLoader.
It adds a selection of miscellaneous, configurable, client-side tweaks to the game.
Some examples of these are the "flexible block placement" tweak and the "fast block placement" tweak.
For more information and the downloads (compiled builds), see http://minecraft.curseforge.com/projects/tweakeroo

Compiling
=========
* Clone the repository
* Open a command prompt/terminal to the repository directory
* run 'gradlew build'
* The built jar file will be in build/libs/
