{"modmenu.descriptionTranslation.tweakeroo": "Adds a bunch of configurable client-side tweaks", "tweakeroo.config.disable.comment.disableArmorStandRendering": "停用所有盔甲座的實體繪製", "tweakeroo.config.disable.comment.disableAtmosphericFog": "Disables all Atmospheric Fog that exists\nbetween the player and the Render Distance Fog.", "tweakeroo.config.disable.comment.disableAxeStripping": "停用斧頭對原木剝皮的操作", "tweakeroo.config.disable.comment.disableBatSpawning": "在單人模式下停用蝙蝠生成", "tweakeroo.config.disable.comment.disableBeaconBeamRendering": "關閉烽火台光柱繪製", "tweakeroo.config.disable.comment.disableBlockBreakCooldown": "停用方塊破壞冷卻，可以讓你挖的飛快", "tweakeroo.config.disable.comment.disableBlockBreakingParticles": "移除方塊破碎時產生的粒子效果（由 Nessie 製作）", "tweakeroo.config.disable.comment.disableBossBar": "停用 Boss 血條的顯示", "tweakeroo.config.disable.comment.disableBossFog": "移除 Boss 所產生的煙霧效果", "tweakeroo.config.disable.comment.disableChunkRendering": "停用區塊繪製，這會導致所有方塊不可見。\n復原繪製需要在停用此功能後使用 F3 + A 重新繪製世界。\n這可能會讓你在大量方塊改變時擁有不那麼低的幀數", "tweakeroo.config.disable.comment.disableClientEntityUpdates": "停用客戶端中除了玩家外所有實體的更新。\n當你需要去處理實體過多環境下的情況時，使用這個可以降低客戶端的繪製壓力", "tweakeroo.config.disable.comment.disableClientLightUpdates": "停用客戶端的所有光照更新", "tweakeroo.config.disable.comment.disableConstantChunkSaving": "除了正常的自動儲存外，禁止遊戲每 Tick 儲存 20 個區塊", "tweakeroo.config.disable.comment.disableCreativeMenuInfestedBlocks": "從創造搜尋清單中移除被蛀蝕的石頭方塊", "tweakeroo.config.disable.comment.disableDeadMobRendering": "不繪製死亡的怪物（生命值為 0 的實體），但這並不影響你與屍體互動", "tweakeroo.config.disable.comment.disableDeadMobTargeting": "防止鎖定生命值處於 0 的實體，開啟後你將不會打到已死亡的怪物身上。\n§4注意：該功能不屬於原版 + TAS 可實現或公平的功能§f\n§4使用此功能前請詢問伺服器管理是否允許此功能的使用§f", "tweakeroo.config.disable.comment.disableDoubleTapSprint": "停用後雙擊前進鍵時不會觸發疾走", "tweakeroo.config.disable.comment.disableEntityRendering": "停用客戶端中除了玩家外所有實體的繪製。\n當你需要去處理實體過多環境下的情況時，使用這個可以降低客戶端的繪製壓力", "tweakeroo.config.disable.comment.disableEntityTicking": "禁止除玩家以外的所有實體運算", "tweakeroo.config.disable.comment.disableFallingBlockEntityRendering": "如果啟用，則掉落的方塊實體將不會被繪製", "tweakeroo.config.disable.comment.disableFirstPersonEffectParticles": "移除第一人稱的藥水狀態粒子效果", "tweakeroo.config.disable.comment.disableInventoryEffectRendering": "移除了背包中狀態效果的繪製", "tweakeroo.config.disable.comment.disableItemSwitchRenderCooldown": "開啟後在你切換或使用物品時將不會有任何的冷卻/裝備動繪畫", "tweakeroo.config.disable.comment.disableMobSpawnerMobRendering": "停用生怪磚內實體模型繪製", "tweakeroo.config.disable.comment.disableNauseaEffect": "停用噁心所導致的視覺效果", "tweakeroo.config.disable.comment.disableNetherFog": "移除地獄的迷霧", "tweakeroo.config.disable.comment.disableNetherPortalSound": "停用地獄門聲音", "tweakeroo.config.disable.comment.disableObserver": "禁止偵測器被觸發", "tweakeroo.config.disable.comment.disableOffhandRendering": "停用非慣用手持有物品時對非慣用手的繪製", "tweakeroo.config.disable.comment.disableParticles": "停用所有的粒子效果", "tweakeroo.config.disable.comment.disablePortalGuiClosing": "如果啟用，你可以在傳送門中開啟各種介面\n\n§b譯註：如果你在傳送時將物品拖動到物品欄外面，傳送結束後物品會消失", "tweakeroo.config.disable.comment.disableRainEffects": "停用下雨時的視覺效果與聲音", "tweakeroo.config.disable.comment.disableRenderDistanceFog": "停用視距邊緣的迷霧", "tweakeroo.config.disable.comment.disableRenderingScaffolding": "停用鷹架的繪製", "tweakeroo.config.disable.comment.disableScoreboardRendering": "停用側面的計分板顯示", "tweakeroo.config.disable.comment.disableShovelPathing": "停用鏟子將草地等製作成土徑塊", "tweakeroo.config.disable.comment.disableShulkerBoxTooltip": "關閉界伏盒內容的原版文字顯示", "tweakeroo.config.disable.comment.disableSignGui": "停用告示牌開啟時的編輯介面", "tweakeroo.config.disable.comment.disableSkyDarkness": "停用 Y = 63 以下的天空黑暗\n（透過將天空黑暗的觸發 Y 值移動到世界底部以下 2 格實現）", "tweakeroo.config.disable.comment.disableSlimeBlockSlowdown": "消除了在黏液塊上移動時減速效果\n（此功能來自 Nessie 製作的 UsefulMod）", "tweakeroo.config.disable.comment.disableStatusEffectHud": "停用狀態效果的 HUD 繪製（通常在畫面的右上角）", "tweakeroo.config.disable.comment.disableTileEntityRendering": "停用所有的方塊實體的繪製", "tweakeroo.config.disable.comment.disableTileEntityTicking": "禁止所有的方塊實體被加入運算", "tweakeroo.config.disable.comment.disableVillagerTradeLocking": "透過在交易時增加最大次數限制來防止村民鎖定交易", "tweakeroo.config.disable.comment.disableWallUnsprint": "開啟後在疾走時撞上牆壁也不會停止疾走狀態", "tweakeroo.config.disable.comment.disableWorldViewBob": "停用世界的視角晃動效果，但不影響手的搖晃動繪畫\n如果安裝了 Iris，此功能將失效", "tweakeroo.config.disable.name.disableArmorStandRendering": "停用盔甲座繪製", "tweakeroo.config.disable.name.disableAtmosphericFog": "disableAtmosphericFog", "tweakeroo.config.disable.name.disableAxeStripping": "停用斧頭剝皮", "tweakeroo.config.disable.name.disableBatSpawning": "停用蝙蝠生成", "tweakeroo.config.disable.name.disableBeaconBeamRendering": "停用烽火台光柱繪製", "tweakeroo.config.disable.name.disableBlockBreakCooldown": "停用方塊挖掘冷卻", "tweakeroo.config.disable.name.disableBlockBreakingParticles": "停用方塊破碎粒子", "tweakeroo.config.disable.name.disableBossBar": "停用 Boss 血條", "tweakeroo.config.disable.name.disableBossFog": "停用 Boss 煙霧繪製", "tweakeroo.config.disable.name.disableChunkRendering": "停用區塊繪製", "tweakeroo.config.disable.name.disableClientEntityUpdates": "停用實體更新", "tweakeroo.config.disable.name.disableClientLightUpdates": "停用客戶端光照更新", "tweakeroo.config.disable.name.disableConstantChunkSaving": "停用區塊持續儲存", "tweakeroo.config.disable.name.disableCreativeMenuInfestedBlocks": "創造不可搜尋蛀蝕方塊", "tweakeroo.config.disable.name.disableDeadMobRendering": "停用怪物屍體繪製", "tweakeroo.config.disable.name.disableDeadMobTargeting": "停用怪物屍體互動", "tweakeroo.config.disable.name.disableDoubleTapSprint": "停用雙擊疾走", "tweakeroo.config.disable.name.disableEntityRendering": "停用實體繪製", "tweakeroo.config.disable.name.disableEntityTicking": "停用實體刻", "tweakeroo.config.disable.name.disableFallingBlockEntityRendering": "停用掉落方塊繪製", "tweakeroo.config.disable.name.disableFirstPersonEffectParticles": "停用第一人稱粒子", "tweakeroo.config.disable.name.disableInventoryEffectRendering": "停用背包介面效果", "tweakeroo.config.disable.name.disableItemSwitchRenderCooldown": "停用裝備動繪畫", "tweakeroo.config.disable.name.disableMobSpawnerMobRendering": "停用生怪磚繪製", "tweakeroo.config.disable.name.disableNauseaEffect": "停用噁心效果", "tweakeroo.config.disable.name.disableNetherFog": "停用地獄迷霧", "tweakeroo.config.disable.name.disableNetherPortalSound": "停用地獄門聲音", "tweakeroo.config.disable.name.disableObserver": "停用觀察者觸發", "tweakeroo.config.disable.name.disableOffhandRendering": "停用非慣用手繪製", "tweakeroo.config.disable.name.disableParticles": "停用所有粒子", "tweakeroo.config.disable.name.disablePortalGuiClosing": "禁止傳送門關閉 GUI", "tweakeroo.config.disable.name.disableRainEffects": "停用下雨效果", "tweakeroo.config.disable.name.disableRenderDistanceFog": "停用遠處迷霧", "tweakeroo.config.disable.name.disableRenderingScaffolding": "停用鷹架繪製", "tweakeroo.config.disable.name.disableScoreboardRendering": "停用計分板顯示", "tweakeroo.config.disable.name.disableShovelPathing": "停用製作土徑", "tweakeroo.config.disable.name.disableShulkerBoxTooltip": "停用界伏盒原版顯示", "tweakeroo.config.disable.name.disableSignGui": "停用告示牌 GUI", "tweakeroo.config.disable.name.disableSkyDarkness": "停用天空黑暗", "tweakeroo.config.disable.name.disableSlimeBlockSlowdown": "停用黏液塊減速", "tweakeroo.config.disable.name.disableStatusEffectHud": "停用效果顯示", "tweakeroo.config.disable.name.disableTileEntityRendering": "停用方塊實體繪製", "tweakeroo.config.disable.name.disableTileEntityTicking": "停用方塊實體刻", "tweakeroo.config.disable.name.disableVillagerTradeLocking": "停用村民交易鎖定", "tweakeroo.config.disable.name.disableWallUnsprint": "停用撞牆剎車", "tweakeroo.config.disable.name.disableWorldViewBob": "停用走路晃動", "tweakeroo.config.disable.prettyName.disableArmorStandRendering": "停用盔甲座繪製", "tweakeroo.config.disable.prettyName.disableAtmosphericFog": "Disable Atmospheric Fog", "tweakeroo.config.disable.prettyName.disableAxeStripping": "停用斧頭剝皮", "tweakeroo.config.disable.prettyName.disableBatSpawning": "停用蝙蝠生成", "tweakeroo.config.disable.prettyName.disableBeaconBeamRendering": "停用烽火台光柱繪製", "tweakeroo.config.disable.prettyName.disableBlockBreakCooldown": "停用方塊挖掘冷卻", "tweakeroo.config.disable.prettyName.disableBlockBreakingParticles": "停用方塊破碎粒子", "tweakeroo.config.disable.prettyName.disableBossBar": "停用 Boss 血條", "tweakeroo.config.disable.prettyName.disableBossFog": "停用 Boss 煙霧繪製", "tweakeroo.config.disable.prettyName.disableChunkRendering": "停用區塊繪製", "tweakeroo.config.disable.prettyName.disableClientEntityUpdates": "停用實體更新", "tweakeroo.config.disable.prettyName.disableClientLightUpdates": "停用客戶端光照更新", "tweakeroo.config.disable.prettyName.disableConstantChunkSaving": "停用區塊持續儲存", "tweakeroo.config.disable.prettyName.disableCreativeMenuInfestedBlocks": "創造不可搜尋蛀蝕方塊", "tweakeroo.config.disable.prettyName.disableDeadMobRendering": "停用怪物屍體繪製", "tweakeroo.config.disable.prettyName.disableDeadMobTargeting": "停用怪物屍體互動", "tweakeroo.config.disable.prettyName.disableDoubleTapSprint": "停用雙擊疾走", "tweakeroo.config.disable.prettyName.disableEntityRendering": "停用實體繪製", "tweakeroo.config.disable.prettyName.disableEntityTicking": "停用實體刻", "tweakeroo.config.disable.prettyName.disableFallingBlockEntityRendering": "停用掉落方塊繪製", "tweakeroo.config.disable.prettyName.disableFirstPersonEffectParticles": "停用第一人稱粒子", "tweakeroo.config.disable.prettyName.disableInventoryEffectRendering": "停用背包介面效果", "tweakeroo.config.disable.prettyName.disableItemSwitchRenderCooldown": "停用裝備動繪畫", "tweakeroo.config.disable.prettyName.disableMobSpawnerMobRendering": "停用生怪磚繪製", "tweakeroo.config.disable.prettyName.disableNauseaEffect": "停用噁心效果", "tweakeroo.config.disable.prettyName.disableNetherFog": "停用地獄迷霧", "tweakeroo.config.disable.prettyName.disableNetherPortalSound": "停用地獄門聲音", "tweakeroo.config.disable.prettyName.disableObserver": "停用觀察者觸發", "tweakeroo.config.disable.prettyName.disableOffhandRendering": "停用非慣用手繪製", "tweakeroo.config.disable.prettyName.disableParticles": "停用所有粒子", "tweakeroo.config.disable.prettyName.disablePortalGuiClosing": "禁止傳送門關閉 GUI", "tweakeroo.config.disable.prettyName.disableRainEffects": "停用下雨效果", "tweakeroo.config.disable.prettyName.disableRenderDistanceFog": "停用遠處迷霧", "tweakeroo.config.disable.prettyName.disableRenderingScaffolding": "停用鷹架繪製", "tweakeroo.config.disable.prettyName.disableScoreboardRendering": "停用計分板顯示", "tweakeroo.config.disable.prettyName.disableShovelPathing": "停用製作土徑", "tweakeroo.config.disable.prettyName.disableShulkerBoxTooltip": "停用界伏盒原版顯示", "tweakeroo.config.disable.prettyName.disableSignGui": "停用告示牌 GUI", "tweakeroo.config.disable.prettyName.disableSkyDarkness": "停用天空黑暗", "tweakeroo.config.disable.prettyName.disableSlimeBlockSlowdown": "停用黏液塊減速", "tweakeroo.config.disable.prettyName.disableStatusEffectHud": "停用效果顯示", "tweakeroo.config.disable.prettyName.disableTileEntityRendering": "停用方塊實體繪製", "tweakeroo.config.disable.prettyName.disableTileEntityTicking": "停用方塊實體刻", "tweakeroo.config.disable.prettyName.disableVillagerTradeLocking": "停用村民交易鎖定", "tweakeroo.config.disable.prettyName.disableWallUnsprint": "停用撞牆剎車", "tweakeroo.config.disable.prettyName.disableWorldViewBob": "停用走路晃動", "tweakeroo.config.feature_toggle.comment.tweakAccurateBlockPlacement": "啟用簡化版的 §6[方塊靈活放置]§r 功能，類似於 Carpet 模組，即朝向點擊的方塊面或背向該方塊面進行方塊放置", "tweakeroo.config.feature_toggle.comment.tweakAfterClicker": "啟用該功能後，它會對剛剛放置的方塊快速右鍵一定次數，\n這在放置四擋中繼器時非常有用。\n按住快捷鍵時捲動滾輪可以調整放置後右鍵的次數。", "tweakeroo.config.feature_toggle.comment.tweakAimLock": "啟用瞄準鎖定，將水平和俯仰旋轉鎖定到目前值。\n這與[瞄準輔助鎖定]不同，\n後者將它們鎖定到一個確定值。\n這項設定允許將它們「自由地」鎖定到目前值", "tweakeroo.config.feature_toggle.comment.tweakAngelBlock": "此功能將讓你能夠在空中放置方塊。\n§6該功能僅在創造模式生效§r\n由 Flotato 提供", "tweakeroo.config.feature_toggle.comment.tweakAreaSelector": "Enables the area selector\nWritten by Andrew54757 under TweakFork", "tweakeroo.config.feature_toggle.comment.tweakAutoSwitchElytra": "當玩家騰空時自動切換到鞘翅，\n並在著陸時切換回之前的胸甲", "tweakeroo.config.feature_toggle.comment.tweakBlockReachOverride": "覆寫方塊放置距離，\n需要在 §6[通用功能] -> [覆寫方塊放置距離]§r 中設定方塊放置距離", "tweakeroo.config.feature_toggle.comment.tweakBlockTypeBreakRestriction": "使用自訂的 §6[方塊破壞限制]§r\n在 §6[設定表單] -> [方塊破壞限制*]§r 變更您的設定", "tweakeroo.config.feature_toggle.comment.tweakBreakingGrid": "當啟用時，您只能等距的破壞方塊。\n按住快捷鍵時捲動滑輪可以快速調整設定", "tweakeroo.config.feature_toggle.comment.tweakBreakingRestriction": "啟用破壞範圍限制時，將你挖掘的範圍限制在\n（一個水平面、直線、平面、對角線、稜邊）上", "tweakeroo.config.feature_toggle.comment.tweakBundleDisplay": "啟用此功能後，當你按住 Shift 鍵並將滑鼠懸停在束口袋上時，可以預覽束口袋內的物品", "tweakeroo.config.feature_toggle.comment.tweakChatBackgroundColor": "將聊天欄的背景顏色設定為 §6[通用功能] -> [聊天背景顏色]§r 中所選擇的顏色", "tweakeroo.config.feature_toggle.comment.tweakChatPersistentText": "關閉聊天欄時不會將已輸入的內容清空，而是作為草稿儲存", "tweakeroo.config.feature_toggle.comment.tweakChatTimestamp": "在每一條資訊前顯示資訊傳送的時間", "tweakeroo.config.feature_toggle.comment.tweakCommandBlockExtraFields": "在指令方塊的 GUI 中加入額外的部分，用於設定指令方塊的名稱，並查看執行結果", "tweakeroo.config.feature_toggle.comment.tweakCreativeExtraItems": "將自訂物品加入到物品組中。\n請參閱 §6[設定表單] -> [創造額外物品]§r 以控制加入到組中的物品。\n注意：目前這些物品將被加入到 [交通運輸] 物品欄中（因為它的物品最少），\n但將來這些組將可以根據加入的物品進行設定", "tweakeroo.config.feature_toggle.comment.tweakCustomFlatPresets": "開啟此選項後會在 §6[生成世界] -> [超平坦] -> [預設]§r 中加入自訂的預設。\n自訂預設可在 §6[設定表單] -> [自訂世界方案]§r 中編輯加入", "tweakeroo.config.feature_toggle.comment.tweakCustomFlyDeceleration": "允許在創造模式或旁觀者模式下改變飛行減速程度，\n這主要用於加快減速。當鬆開移動鍵時，將減少滑翔的距離\n在 §6[通用] -> [飛行停止速度]§r 中設定停止速度", "tweakeroo.config.feature_toggle.comment.tweakCustomInventoryScreenScale": "允許自訂容器介面縮放比例。\n縮放值可以在這裡調整：§6[通用功能] -> [自訂容器比例]§r", "tweakeroo.config.feature_toggle.comment.tweakDarknessVisibility": "If enabled, this will increase visibility\nwhile effected by the Darkness Status Effect.", "tweakeroo.config.feature_toggle.comment.tweakElytraCamera": "啟用此功能後需要在 §6[通用功能快捷鍵] -> [環顧四周]§r 中設定啟動快捷鍵。\n按住此快捷鍵會鎖定玩家目前的視角方向，\n使玩家滑鼠輸入僅改變攝影機視角而不改變移動方向。\n這讓玩家能夠在飛行環顧四周，觀賞風景", "tweakeroo.config.feature_toggle.comment.tweakEmptyShulkerBoxesStack": "允許空界伏盒堆疊，數量最多為 64 個 \n注意：它們也會在背包中堆疊！\n在伺服器上，除非伺服器安裝了相同功能的模組，否則會導致資料不同步或出現故障。\n在單人遊戲中，這會改變界伏盒的系統行為", "tweakeroo.config.feature_toggle.comment.tweakEntityReachOverride": "覆寫實體互動距離，\n需要在 §6[通用功能] -> [覆寫實體互動距離]§r 中設定實體互動距離", "tweakeroo.config.feature_toggle.comment.tweakEntityTypeAttackRestriction": "手動限制可以攻擊的實體。\n請參閱 §6[通用功能] -> [實體攻擊規則限制類型]§r", "tweakeroo.config.feature_toggle.comment.tweakExplosionReducedParticles": "啟用後，所有爆炸的粒子效果將使用「中等」體積的粒子，\n而不產生「大型」和「巨型」體積的粒子。\n這能在大量 TNT 爆炸時提高你的幀數", "tweakeroo.config.feature_toggle.comment.tweakF3Cursor": "啟用後畫面準心為按下 F3 時的樣式", "tweakeroo.config.feature_toggle.comment.tweakFakeSneakPlacement": "該功能將你的點擊位置從可互動的方塊上調整到了他旁邊的空氣上，\n這讓你可以在可互動的方塊旁邊直接放置方塊而不開啟互動介面。\n注意：這不是真正的偽潛行，只是看起來效果一樣", "tweakeroo.config.feature_toggle.comment.tweakFakeSneaking": "啟用後，你將不會從方塊邊緣落下，類似潛行但不會降低移動速度", "tweakeroo.config.feature_toggle.comment.tweakFastBlockPlacement": "啟用 §6[方塊快速放置]§r 功能。\n該功能擁有多種限制器，可以在 §6[通用功能快捷鍵]§r 中查看快速鍵設定（預設為 Z + 1~6）", "tweakeroo.config.feature_toggle.comment.tweakFastLeftClick": "可以在一遊戲刻中點擊多次左鍵。\n點擊速度可在 §6[通用功能]§f 中進行設定", "tweakeroo.config.feature_toggle.comment.tweakFastRightClick": "可以在一遊戲刻中點擊多次右鍵。\n點擊速度可在 §6[通用功能]§r 中進行設定", "tweakeroo.config.feature_toggle.comment.tweakFillCloneLimit": "啟用在單人遊戲中覆寫 /fill 和 /clone 指令的方塊數量限制。\n新的限制可以在 §6[通用功能] -> [填充方塊上限]§f 中設定", "tweakeroo.config.feature_toggle.comment.tweakFlexibleBlockPlacement": "按住此功能快捷鍵時，可以以不同的方向或角度放置方塊。\n快捷鍵可在 §6[通用功能快捷鍵]§r 中進行設定，預設為 Ctrl&Alt", "tweakeroo.config.feature_toggle.comment.tweakFlySpeed": "開啟後可以調整飛行速度，包括創造模式、旁觀者、靈魂出竅時的飛行速度。\n此功能擁有 4 個預設欄，可在 §6[通用功能]§r 中設定。\n欄位切換快捷鍵需要在 §6[通用功能快捷鍵]§r 中進行設定，預設為空", "tweakeroo.config.feature_toggle.comment.tweakFreeCamera": "啟用靈魂出竅後，你可以在身邊隨意控制視角，就像旁觀者一樣。\n此功能在 §6[通用功能]§r 內有兩個附屬選項", "tweakeroo.config.feature_toggle.comment.tweakGammaOverride": "啟用後會將 Gamma 覆寫為設定值，當 Gamma 大於 10 時即可實現無限夜視的效果。\n覆寫值可於 §6[通用功能] -> [伽馬值設定]§r 設定。\n（Gamma 也叫灰度係數，是亮度和對比度的協助工具）", "tweakeroo.config.feature_toggle.comment.tweakHandRestock": "啟用後會在目前物品將使用完前自動補充一組新物品手上。\n此功能在 §6[通用功能]§r 內有兩個附屬設定", "tweakeroo.config.feature_toggle.comment.tweakHangableEntityBypass": "啟用此功能將改變玩家與懸掛類實體（展示框和繪畫）的互動方式。\n開啟後預設忽略與懸掛類實體的互動，\n可在 §6[通用功能] -> [懸掛實體互動方式]§r 設定與懸掛類實體的互動方法", "tweakeroo.config.feature_toggle.comment.tweakHoldAttack": "模擬長按攻擊鍵的效果", "tweakeroo.config.feature_toggle.comment.tweakHoldUse": "模擬長按使用鍵的效果", "tweakeroo.config.feature_toggle.comment.tweakHotbarScroll": "可以透過捲動功能調節物品欄", "tweakeroo.config.feature_toggle.comment.tweakHotbarSlotCycle": "此功能會在放置方塊後循環選中下一格物品欄。\n你可以在 §6[通用功能]§r 中設定循環範圍上限。\n要快速調整該值，請在按住該快捷鍵的同時捲動滑鼠", "tweakeroo.config.feature_toggle.comment.tweakHotbarSlotRandomizer": "此功能會在放置方塊後隨機選中一格物品欄。\n你可以在 §6[通用功能]§r 中設定隨機範圍上限。\n要快速調整該值，請在按住該快捷鍵的同時捲動滑鼠", "tweakeroo.config.feature_toggle.comment.tweakHotbarSwap": "啟用透過快捷鍵進行快捷欄欄位切換的功能\n在 §6[通用功能快捷鍵]§r 中設定該功能的快捷鍵", "tweakeroo.config.feature_toggle.comment.tweakInventoryPreview": "當游標懸停在具有物品欄的方塊或實體上並按住設定的快捷鍵時，啟用物品欄預覽。\n§c已棄用，在 1.21+ 版本的 MiniHUD 中已經實現了此功能§f", "tweakeroo.config.feature_toggle.comment.tweakItemUnstackingProtection": "啟用後在背包已滿時你將不會製造新的不可堆疊物品。\n例如：在你收集岩漿桶時，他可以避免背包滿時丟出岩漿桶。\n\n可在 §6[設定表單] -> [不可堆疊物品保護]§r 中設定不可堆疊物品列表", "tweakeroo.config.feature_toggle.comment.tweakLavaVisibility": "如果啟用該功能，\n在防火狀態下，根據水中呼吸和親水性附魔的等級，\n增加岩漿下的能見度", "tweakeroo.config.feature_toggle.comment.tweakMapPreview": "啟用後，在背包內按下 Shift 可以預覽地圖", "tweakeroo.config.feature_toggle.comment.tweakMovementKeysLast": "啟用此功能後，同時按下兩個反方向的移動鍵時，\n將不再相互抵消，而是按照最後輸入的方向移動", "tweakeroo.config.feature_toggle.comment.tweakPeriodicAttack": "啟用週期性點擊左鍵功能\n點擊左鍵的時間間隔可以在 §6[通用功能] -> [點擊左鍵間隔]§r 中調整", "tweakeroo.config.feature_toggle.comment.tweakPeriodicHoldAttack": "在一定的時間內保持按住左鍵狀態。\n在 §6[通用功能]->[左鍵按住間隔]§r 中設定時間間隔，\n在 §6[左鍵按住時間]§r 中設定持續時間。\n\n§6注意：請不要同時使用長按左鍵或左鍵連點§f", "tweakeroo.config.feature_toggle.comment.tweakPeriodicHoldUse": "在一定的時間內保持按住右鍵狀態。\n在 §6[通用功能]->[右鍵按住間隔]§r 中設定時間間隔，\n在 §6[右鍵按住時間]§r 中設定持續時間。\n\n§6注意：請不要同時使用長按右鍵或右鍵連點§f", "tweakeroo.config.feature_toggle.comment.tweakPeriodicUse": "啟用週期性點擊右鍵功能\n點擊右鍵的時間間隔可以在 §6[通用功能] -> [點擊右鍵間隔]§r 中調整", "tweakeroo.config.feature_toggle.comment.tweakPermanentSneak": "啟用後會一直保持潛行狀態 \n§6注意：此功能為貨真價實的潛行，會降低移動速度§f\n§e不降低移動速度的功能為 §e[偽潛行]§f", "tweakeroo.config.feature_toggle.comment.tweakPermanentSprint": "如果啟用，玩家將會一直保持奔跑狀態", "tweakeroo.config.feature_toggle.comment.tweakPickBeforePlace": "開啟後，在每次放置方塊之前，手中會自動切換為與目前右鍵點擊方塊相同的方塊", "tweakeroo.config.feature_toggle.comment.tweakPlacementGrid": "啟用後，您只能等距的破壞方塊。\n按住快捷鍵時捲動滑輪可以快速調整間距", "tweakeroo.config.feature_toggle.comment.tweakPlacementLimit": "啟用後，每次放置只可以放置一定數量的方塊。\n單次放置方塊的數量限制可以在 §6[通用功能] -> [放置數量限制]§r 進行修改，\n按住快捷鍵時捲動滑輪可以快速調整限制數量", "tweakeroo.config.feature_toggle.comment.tweakPlacementRestriction": "啟用放置範圍限制時，將你能放置方塊的範圍限制在\n（水平面、直線、平面、對角線、稜邊）上", "tweakeroo.config.feature_toggle.comment.tweakPlacementRestrictionFirst": "限制方塊的放置位置，你只能將方塊放置在第一次點擊的相同方塊上", "tweakeroo.config.feature_toggle.comment.tweakPlacementRestrictionHand": "限制方塊的放置位置，你只能將方塊放置在與手中持有的方塊類型相同的方塊上", "tweakeroo.config.feature_toggle.comment.tweakPlayerInventoryPeek": "開啟後你可以使用 §6[容器預覽]§r 快捷鍵預覽玩家背包。", "tweakeroo.config.feature_toggle.comment.tweakPlayerListAlwaysVisible": "如果啟用，玩家列表將會一直顯示而不需要按住按鍵（預設為 TAB）", "tweakeroo.config.feature_toggle.comment.tweakPotionWarning": "當非烽火台效果即將消失時，在物品欄上方顯示警報提醒", "tweakeroo.config.feature_toggle.comment.tweakPrintDeathCoordinates": "開啟後在你死亡時會在聊天欄傳送死亡座標，此座標僅自己可見。\n這個功能最初來源於 Nessie 的 UsefulMod", "tweakeroo.config.feature_toggle.comment.tweakRenderEdgeChunks": "允許繪製客戶端載入的最邊緣區塊\n原版 Minecraft 不允許繪製未載入所有相鄰區塊的區塊，這意味著客戶端載入的最邊緣區塊在原版中不會被繪製\n§l這在靈魂出竅模式下也非常有用！§r", "tweakeroo.config.feature_toggle.comment.tweakRenderInvisibleEntities": "啟用後不可見的實體會繪製為類似旁觀者的半透明狀態", "tweakeroo.config.feature_toggle.comment.tweakRenderLimitEntities": "啟用後可以限制每幀繪製的特定實體的數量。\n繪製數量可以在 §6[通用功能]§r 中分別進行設定。\n§6目前支援限制經驗球和掉落物實體§r", "tweakeroo.config.feature_toggle.comment.tweakRepairMode": "啟用此功能時，會把耐久不滿並帶有修補的裝備自動切換到手中", "tweakeroo.config.feature_toggle.comment.tweakSculkPulseLength": "允許修改伏聆感測器的脈衝長度\n在 §6[通用功能] -> [調整伏聆植物脈衝距離]§r 設定脈衝長度", "tweakeroo.config.feature_toggle.comment.tweakSelectiveBlocksRenderOutline": "Renders an outline over listed blocks\nWritten by Andrew54757 under TweakFork", "tweakeroo.config.feature_toggle.comment.tweakSelectiveBlocksRendering": "Enables selectively visible blocks rendering\nWritten by Andrew54757 under TweakFork", "tweakeroo.config.feature_toggle.comment.tweakServerDataSync": "對諸如界伏盒等實體使用伺服器資料同步器，可以使 §6[容器預覽] 在安裝 Servux 的伺服器上工作\n§6你必須是伺服器的管理員，或者安裝伺服器端的模組，才能使同步器工作，即使此選項已設定為 true", "tweakeroo.config.feature_toggle.comment.tweakServerDataSyncBackup": "伺服器資料同步器可以設定為在 Servux 不可用時使用原版的 NbtQueryRequest 資料包", "tweakeroo.config.feature_toggle.comment.tweakShulkerBoxDisplay": "滑鼠懸停在背包中的界伏盒上時，按下 Shift 將可以預覽界伏盒中的內容", "tweakeroo.config.feature_toggle.comment.tweakSignCopy": "開啟後放置告示牌時將自動填充上一個告示牌的文字本。\n該功能可以與 §6[停用類功能] -> [停用告示牌 GUI]§r 配合使用，\n在放置第一個告示牌後啟用該功能，可以快速放置第一個告示牌的副本", "tweakeroo.config.feature_toggle.comment.tweakSnapAim": "啟用瞄準輔助後，玩家的視角每次只能移動預先設定的角度（預設為 45 度）\n角度可以在 §6[通用功能]§r 中進行變更", "tweakeroo.config.feature_toggle.comment.tweakSnapAimLock": "啟用後會鎖定玩家視角的水準和垂直移動。\n\n§6該功能需要開啟 [瞄準輔助] 後才能生效§f", "tweakeroo.config.feature_toggle.comment.tweakSneak_1_15_2": "復原了 1.15.2 的潛行行為", "tweakeroo.config.feature_toggle.comment.tweakSpectatorTeleport": "啟用後你可以在旁觀者模式時傳送到其他旁觀者的位置上。\n該功能來源於 Nessie 的 UsefulMod 中", "tweakeroo.config.feature_toggle.comment.tweakStructureBlockLimit": "允許覆寫結構方塊範圍限制。\n新的限制在 §6[通用功能] -> [結構方塊範圍上限]§r 中設定", "tweakeroo.config.feature_toggle.comment.tweakSwapAlmostBrokenTools": "開啟此功能後，耐久將要耗盡的工具會被取代為更高耐久的工具", "tweakeroo.config.feature_toggle.comment.tweakTabCompleteCoordinate": "開啟此功能後，在座標補全時如果準心沒有指向方塊，\n則會使用玩家座標補全而不是相對座標（~ ~ ~）", "tweakeroo.config.feature_toggle.comment.tweakToolSwitch": "開啟後將根據挖掘的方塊自動切換工具", "tweakeroo.config.feature_toggle.comment.tweakWaterVisibility": "If enabled, then the level of Respiration and Aqua Affinity enchantments,\nwill greatly increase the visibility under water.", "tweakeroo.config.feature_toggle.comment.tweakWeaponSwitch": "開啟後將根據攻擊的實體自動切換武器", "tweakeroo.config.feature_toggle.comment.tweakYMirror": "啟用後按下快捷鍵時，方塊將放置為 Y 軸對稱的形式。\n這功能主要是為了放置上下相反的半磚和階梯。\n快捷鍵預設為空，需要在 §6[通用功能快捷鍵] -> [Y 軸鏡像放置]§r 中設定", "tweakeroo.config.feature_toggle.comment.tweakZoom": "啟用望遠鏡功能，需要在 §6[通用功能快捷鍵] -> [啟用望遠鏡]§r 中設定快捷鍵來使用望遠鏡", "tweakeroo.config.feature_toggle.name.tweakAccurateBlockPlacement": "精準放置模式", "tweakeroo.config.feature_toggle.name.tweakAfterClicker": "放置後點擊", "tweakeroo.config.feature_toggle.name.tweakAimLock": "視角鎖定", "tweakeroo.config.feature_toggle.name.tweakAngelBlock": "憑空放置", "tweakeroo.config.feature_toggle.name.tweakAreaSelector": "tweakAreaSelector", "tweakeroo.config.feature_toggle.name.tweakAutoSwitchElytra": "自動切換鞘翅", "tweakeroo.config.feature_toggle.name.tweakBlockReachOverride": "方塊放置距離覆寫§r", "tweakeroo.config.feature_toggle.name.tweakBlockTypeBreakRestriction": "方塊破壞限制", "tweakeroo.config.feature_toggle.name.tweakBreakingGrid": "破壞間距限制", "tweakeroo.config.feature_toggle.name.tweakBreakingRestriction": "破壞範圍限制", "tweakeroo.config.feature_toggle.name.tweakBundleDisplay": "束口袋預覽", "tweakeroo.config.feature_toggle.name.tweakChatBackgroundColor": "聊天背景顏色", "tweakeroo.config.feature_toggle.name.tweakChatPersistentText": "聊天草稿儲存", "tweakeroo.config.feature_toggle.name.tweakChatTimestamp": "聊天時間顯示", "tweakeroo.config.feature_toggle.name.tweakCommandBlockExtraFields": "指令方塊額外選項", "tweakeroo.config.feature_toggle.name.tweakCreativeExtraItems": "創造額外物品", "tweakeroo.config.feature_toggle.name.tweakCustomFlatPresets": "自訂世界方案", "tweakeroo.config.feature_toggle.name.tweakCustomFlyDeceleration": "飛行減速", "tweakeroo.config.feature_toggle.name.tweakCustomInventoryScreenScale": "自訂容器比例", "tweakeroo.config.feature_toggle.name.tweakDarknessVisibility": "tweakDarknessVisibility", "tweakeroo.config.feature_toggle.name.tweakElytraCamera": "環顧四周", "tweakeroo.config.feature_toggle.name.tweakEmptyShulkerBoxesStack": "空界伏盒堆疊§r", "tweakeroo.config.feature_toggle.name.tweakEntityReachOverride": "實體互動距離覆寫§r", "tweakeroo.config.feature_toggle.name.tweakEntityTypeAttackRestriction": "實體攻擊限制", "tweakeroo.config.feature_toggle.name.tweakExplosionReducedParticles": "爆炸粒子最佳化", "tweakeroo.config.feature_toggle.name.tweakF3Cursor": "使用 F3 準心", "tweakeroo.config.feature_toggle.name.tweakFakeSneakPlacement": "封鎖方塊互動", "tweakeroo.config.feature_toggle.name.tweakFakeSneaking": "偽潛行", "tweakeroo.config.feature_toggle.name.tweakFastBlockPlacement": "方塊快速放置", "tweakeroo.config.feature_toggle.name.tweakFastLeftClick": "左鍵快速點擊", "tweakeroo.config.feature_toggle.name.tweakFastRightClick": "右鍵快速點擊", "tweakeroo.config.feature_toggle.name.tweakFillCloneLimit": "填充方塊上限§r", "tweakeroo.config.feature_toggle.name.tweakFlexibleBlockPlacement": "方塊靈活放置", "tweakeroo.config.feature_toggle.name.tweakFlySpeed": "飛行速度控制", "tweakeroo.config.feature_toggle.name.tweakFreeCamera": "靈魂出竅", "tweakeroo.config.feature_toggle.name.tweakGammaOverride": "伽馬覆寫", "tweakeroo.config.feature_toggle.name.tweakHandRestock": "自動補貨", "tweakeroo.config.feature_toggle.name.tweakHangableEntityBypass": "忽略懸掛實體", "tweakeroo.config.feature_toggle.name.tweakHoldAttack": "長按左鍵", "tweakeroo.config.feature_toggle.name.tweakHoldUse": "長按右鍵", "tweakeroo.config.feature_toggle.name.tweakHotbarScroll": "物品欄捲動", "tweakeroo.config.feature_toggle.name.tweakHotbarSlotCycle": "物品欄循環捲動", "tweakeroo.config.feature_toggle.name.tweakHotbarSlotRandomizer": "物品欄隨機捲動", "tweakeroo.config.feature_toggle.name.tweakHotbarSwap": "切換物品欄", "tweakeroo.config.feature_toggle.name.tweakInventoryPreview": "容器預覽（已棄用）§r", "tweakeroo.config.feature_toggle.name.tweakItemUnstackingProtection": "不可堆疊物品保護", "tweakeroo.config.feature_toggle.name.tweakLavaVisibility": "岩漿夜視", "tweakeroo.config.feature_toggle.name.tweakMapPreview": "地圖預覽", "tweakeroo.config.feature_toggle.name.tweakMovementKeysLast": "移動方向輸入覆蓋", "tweakeroo.config.feature_toggle.name.tweakPeriodicAttack": "週期性點擊左鍵", "tweakeroo.config.feature_toggle.name.tweakPeriodicHoldAttack": "週期性長按左鍵", "tweakeroo.config.feature_toggle.name.tweakPeriodicHoldUse": "週期性長按右鍵", "tweakeroo.config.feature_toggle.name.tweakPeriodicUse": "週期性點擊右鍵", "tweakeroo.config.feature_toggle.name.tweakPermanentSneak": "一直潛行", "tweakeroo.config.feature_toggle.name.tweakPermanentSprint": "一直奔跑", "tweakeroo.config.feature_toggle.name.tweakPickBeforePlace": "自動匹配放置", "tweakeroo.config.feature_toggle.name.tweakPlacementGrid": "限制放置間距", "tweakeroo.config.feature_toggle.name.tweakPlacementLimit": "限制放置數量", "tweakeroo.config.feature_toggle.name.tweakPlacementRestriction": "限制放置範圍", "tweakeroo.config.feature_toggle.name.tweakPlacementRestrictionFirst": "限制放置位置", "tweakeroo.config.feature_toggle.name.tweakPlacementRestrictionHand": "限制放置方塊", "tweakeroo.config.feature_toggle.name.tweakPlayerInventoryPeek": "玩家背包預覽", "tweakeroo.config.feature_toggle.name.tweakPlayerListAlwaysVisible": "一直顯示玩家列表", "tweakeroo.config.feature_toggle.name.tweakPotionWarning": "效果不足警報", "tweakeroo.config.feature_toggle.name.tweakPrintDeathCoordinates": "傳送死亡座標", "tweakeroo.config.feature_toggle.name.tweakRenderEdgeChunks": "繪製邊界區塊", "tweakeroo.config.feature_toggle.name.tweakRenderInvisibleEntities": "不可見實體繪製", "tweakeroo.config.feature_toggle.name.tweakRenderLimitEntities": "限制實體繪製", "tweakeroo.config.feature_toggle.name.tweakRepairMode": "修補模式", "tweakeroo.config.feature_toggle.name.tweakSculkPulseLength": "調整伏聆植物脈衝距離§r", "tweakeroo.config.feature_toggle.name.tweakSelectiveBlocksRenderOutline": "tweakSelectiveBlocksRenderOutline", "tweakeroo.config.feature_toggle.name.tweakSelectiveBlocksRendering": "tweakSelectiveBlocksRendering", "tweakeroo.config.feature_toggle.name.tweakServerDataSync": "伺服器資料同步", "tweakeroo.config.feature_toggle.name.tweakServerDataSyncBackup": "伺服器資料同步備份", "tweakeroo.config.feature_toggle.name.tweakShulkerBoxDisplay": "界伏盒預覽", "tweakeroo.config.feature_toggle.name.tweakSignCopy": "告示牌複製", "tweakeroo.config.feature_toggle.name.tweakSnapAim": "瞄準輔助", "tweakeroo.config.feature_toggle.name.tweakSnapAimLock": "瞄準輔助鎖定", "tweakeroo.config.feature_toggle.name.tweakSneak_1_15_2": "1.15.2 潛行", "tweakeroo.config.feature_toggle.name.tweakSpectatorTeleport": "旁觀者傳送", "tweakeroo.config.feature_toggle.name.tweakStructureBlockLimit": "變更結構方塊範圍上限§r", "tweakeroo.config.feature_toggle.name.tweakSwapAlmostBrokenTools": "工具防損壞", "tweakeroo.config.feature_toggle.name.tweakTabCompleteCoordinate": "座標補全升級", "tweakeroo.config.feature_toggle.name.tweakToolSwitch": "工具自動選擇", "tweakeroo.config.feature_toggle.name.tweakWaterVisibility": "tweakWaterVisibility", "tweakeroo.config.feature_toggle.name.tweakWeaponSwitch": "武器自動選擇", "tweakeroo.config.feature_toggle.name.tweakYMirror": "Y 軸鏡像放置", "tweakeroo.config.feature_toggle.name.tweakZoom": "望遠鏡", "tweakeroo.config.feature_toggle.prettyName.tweakAccurateBlockPlacement": "精準放置模式", "tweakeroo.config.feature_toggle.prettyName.tweakAfterClicker": "放置後點擊", "tweakeroo.config.feature_toggle.prettyName.tweakAimLock": "視角鎖定", "tweakeroo.config.feature_toggle.prettyName.tweakAngelBlock": "憑空放置", "tweakeroo.config.feature_toggle.prettyName.tweakAreaSelector": "Area Selector", "tweakeroo.config.feature_toggle.prettyName.tweakAutoSwitchElytra": "自動切換鞘翅", "tweakeroo.config.feature_toggle.prettyName.tweakBlockReachOverride": "方塊放置距離覆寫", "tweakeroo.config.feature_toggle.prettyName.tweakBlockTypeBreakRestriction": "方塊破壞限制", "tweakeroo.config.feature_toggle.prettyName.tweakBreakingGrid": "破壞間距限制", "tweakeroo.config.feature_toggle.prettyName.tweakBreakingRestriction": "破壞範圍限制", "tweakeroo.config.feature_toggle.prettyName.tweakBundleDisplay": "束口袋預覽", "tweakeroo.config.feature_toggle.prettyName.tweakChatBackgroundColor": "聊天背景顏色", "tweakeroo.config.feature_toggle.prettyName.tweakChatPersistentText": "聊天草稿儲存", "tweakeroo.config.feature_toggle.prettyName.tweakChatTimestamp": "聊天時間顯示", "tweakeroo.config.feature_toggle.prettyName.tweakCommandBlockExtraFields": "指令方塊額外選項", "tweakeroo.config.feature_toggle.prettyName.tweakCreativeExtraItems": "創造額外物品", "tweakeroo.config.feature_toggle.prettyName.tweakCustomFlatPresets": "自訂世界方案", "tweakeroo.config.feature_toggle.prettyName.tweakCustomFlyDeceleration": "飛行減速", "tweakeroo.config.feature_toggle.prettyName.tweakCustomInventoryScreenScale": "自訂容器比例", "tweakeroo.config.feature_toggle.prettyName.tweakDarknessVisibility": "Darkness Visibility", "tweakeroo.config.feature_toggle.prettyName.tweakElytraCamera": "環顧四周", "tweakeroo.config.feature_toggle.prettyName.tweakEmptyShulkerBoxesStack": "空界伏盒堆疊", "tweakeroo.config.feature_toggle.prettyName.tweakEntityReachOverride": "實體互動距離覆寫", "tweakeroo.config.feature_toggle.prettyName.tweakEntityTypeAttackRestriction": "實體攻擊限制", "tweakeroo.config.feature_toggle.prettyName.tweakExplosionReducedParticles": "爆炸粒子最佳化", "tweakeroo.config.feature_toggle.prettyName.tweakF3Cursor": "使用 F3 準心", "tweakeroo.config.feature_toggle.prettyName.tweakFakeSneakPlacement": "封鎖方塊互動", "tweakeroo.config.feature_toggle.prettyName.tweakFakeSneaking": "偽潛行", "tweakeroo.config.feature_toggle.prettyName.tweakFastBlockPlacement": "方塊快速放置", "tweakeroo.config.feature_toggle.prettyName.tweakFastLeftClick": "左鍵快速點擊", "tweakeroo.config.feature_toggle.prettyName.tweakFastRightClick": "右鍵快速點擊", "tweakeroo.config.feature_toggle.prettyName.tweakFillCloneLimit": "填充方塊上限", "tweakeroo.config.feature_toggle.prettyName.tweakFlexibleBlockPlacement": "方塊靈活放置", "tweakeroo.config.feature_toggle.prettyName.tweakFlySpeed": "飛行速度控制", "tweakeroo.config.feature_toggle.prettyName.tweakFreeCamera": "靈魂出竅", "tweakeroo.config.feature_toggle.prettyName.tweakGammaOverride": "伽馬覆寫", "tweakeroo.config.feature_toggle.prettyName.tweakHandRestock": "自動補貨", "tweakeroo.config.feature_toggle.prettyName.tweakHangableEntityBypass": "忽略懸掛實體", "tweakeroo.config.feature_toggle.prettyName.tweakHoldAttack": "長按左鍵", "tweakeroo.config.feature_toggle.prettyName.tweakHoldUse": "長按右鍵", "tweakeroo.config.feature_toggle.prettyName.tweakHotbarScroll": "物品欄捲動", "tweakeroo.config.feature_toggle.prettyName.tweakHotbarSlotCycle": "物品欄循環捲動", "tweakeroo.config.feature_toggle.prettyName.tweakHotbarSlotRandomizer": "物品欄隨機捲動", "tweakeroo.config.feature_toggle.prettyName.tweakHotbarSwap": "切換物品欄", "tweakeroo.config.feature_toggle.prettyName.tweakInventoryPreview": "容器預覽", "tweakeroo.config.feature_toggle.prettyName.tweakItemUnstackingProtection": "不可堆疊物品保護", "tweakeroo.config.feature_toggle.prettyName.tweakLavaVisibility": "岩漿夜視", "tweakeroo.config.feature_toggle.prettyName.tweakMapPreview": "地圖預覽", "tweakeroo.config.feature_toggle.prettyName.tweakMovementKeysLast": "移動方向輸入覆蓋", "tweakeroo.config.feature_toggle.prettyName.tweakPeriodicAttack": "週期性點擊左鍵", "tweakeroo.config.feature_toggle.prettyName.tweakPeriodicHoldAttack": "週期性長按左鍵", "tweakeroo.config.feature_toggle.prettyName.tweakPeriodicHoldUse": "週期性長按右鍵", "tweakeroo.config.feature_toggle.prettyName.tweakPeriodicUse": "週期性點擊右鍵", "tweakeroo.config.feature_toggle.prettyName.tweakPermanentSneak": "一直潛行", "tweakeroo.config.feature_toggle.prettyName.tweakPermanentSprint": "一直奔跑", "tweakeroo.config.feature_toggle.prettyName.tweakPickBeforePlace": "自動匹配放置", "tweakeroo.config.feature_toggle.prettyName.tweakPlacementGrid": "限制放置間距", "tweakeroo.config.feature_toggle.prettyName.tweakPlacementLimit": "限制放置數量", "tweakeroo.config.feature_toggle.prettyName.tweakPlacementRestriction": "限制放置範圍", "tweakeroo.config.feature_toggle.prettyName.tweakPlacementRestrictionFirst": "限制放置位置", "tweakeroo.config.feature_toggle.prettyName.tweakPlacementRestrictionHand": "限制放置方塊", "tweakeroo.config.feature_toggle.prettyName.tweakPlayerInventoryPeek": "玩家背包預覽", "tweakeroo.config.feature_toggle.prettyName.tweakPlayerListAlwaysVisible": "一直顯示玩家列表", "tweakeroo.config.feature_toggle.prettyName.tweakPotionWarning": "效果不足警報", "tweakeroo.config.feature_toggle.prettyName.tweakPrintDeathCoordinates": "傳送死亡座標", "tweakeroo.config.feature_toggle.prettyName.tweakRenderEdgeChunks": "繪製邊界區塊", "tweakeroo.config.feature_toggle.prettyName.tweakRenderInvisibleEntities": "不可見實體繪製", "tweakeroo.config.feature_toggle.prettyName.tweakRenderLimitEntities": "限制實體繪製", "tweakeroo.config.feature_toggle.prettyName.tweakRepairMode": "修補模式", "tweakeroo.config.feature_toggle.prettyName.tweakSculkPulseLength": "調整伏聆植物脈衝距離", "tweakeroo.config.feature_toggle.prettyName.tweakSelectiveBlocksRenderOutline": "Selective Blocks Render Outline", "tweakeroo.config.feature_toggle.prettyName.tweakSelectiveBlocksRendering": "Selective Blocks Rendering", "tweakeroo.config.feature_toggle.prettyName.tweakServerDataSync": "伺服器資料同步", "tweakeroo.config.feature_toggle.prettyName.tweakServerDataSyncBackup": "伺服器資料同步備份", "tweakeroo.config.feature_toggle.prettyName.tweakShulkerBoxDisplay": "界伏盒預覽", "tweakeroo.config.feature_toggle.prettyName.tweakSignCopy": "告示牌複製", "tweakeroo.config.feature_toggle.prettyName.tweakSnapAim": "瞄準輔助", "tweakeroo.config.feature_toggle.prettyName.tweakSnapAimLock": "瞄準輔助鎖定", "tweakeroo.config.feature_toggle.prettyName.tweakSneak_1_15_2": "1.15.2 潛行", "tweakeroo.config.feature_toggle.prettyName.tweakSpectatorTeleport": "旁觀者傳送", "tweakeroo.config.feature_toggle.prettyName.tweakStructureBlockLimit": "變更結構方塊範圍上限", "tweakeroo.config.feature_toggle.prettyName.tweakSwapAlmostBrokenTools": "工具防損壞", "tweakeroo.config.feature_toggle.prettyName.tweakTabCompleteCoordinate": "座標補全升級", "tweakeroo.config.feature_toggle.prettyName.tweakToolSwitch": "工具自動選擇", "tweakeroo.config.feature_toggle.prettyName.tweakWaterVisibility": "Water Visibility", "tweakeroo.config.feature_toggle.prettyName.tweakWeaponSwitch": "武器自動選擇", "tweakeroo.config.feature_toggle.prettyName.tweakYMirror": "Y 軸鏡像放置", "tweakeroo.config.feature_toggle.prettyName.tweakZoom": "望遠鏡", "tweakeroo.config.fixes.comment.elytraFix": "鞘翅著陸問題被 Earthcomputer 和 N<PERSON><PERSON> 修復\n原版現在修復了起飛，所以該功能只影響了降落", "tweakeroo.config.fixes.comment.elytraSprintCancel": "Fixes the Vanilla bug (MC-279688) that removes\nyou from sprinting while using an Elytra.\nThis setting is only needed for 1.21.4, but for 1.21.5 this is fixed.", "tweakeroo.config.fixes.comment.macHorizontalScroll": "如果您使用的是 Mac 系統，這將套用與 [hscroll 模組] 相同的修復和變更，\n同時不會破壞基於 Malilib 的 Mod 中的所有捲動處理", "tweakeroo.config.fixes.comment.ravagerClientBlockBreakFix": "修復了客戶端在突襲後產生的幽靈方塊等奇怪的東西", "tweakeroo.config.fixes.name.elytraFix": "鞘翅修復", "tweakeroo.config.fixes.name.elytraSprintCancel": "elytraSprintCancel", "tweakeroo.config.fixes.name.macHorizontalScroll": "MAC 捲動修復", "tweakeroo.config.fixes.name.ravagerClientBlockBreakFix": "劫毀獸幽靈方塊修復", "tweakeroo.config.generic.comment.accuratePlacementProtocol": "如果啟用，那麼 §6[方塊靈活放置]§r 和 §6[精確放置]§r 將會使用 Carpet 模組實現。\n§6注意：任何方塊旋轉都需要這樣做，\n§6除了方向只依賴於你在側面點擊的方塊（漏斗、原木等）", "tweakeroo.config.generic.comment.accuratePlacementProtocolMode": "要使用的 [精確放置協定] 類型。\n- 自動：在單人遊戲中使用 [模式 3]，預設在多人遊戲中使用 [僅半磚]，\n除非伺服器有傳送「carpet：hello」資料包的 Carpet 模組，\n在這種情況下，該伺服器上將使用 [模式 2]\n- 模式 3：由 Tweakeroo 本身支援（在單人遊戲中）或與 Servux 一起使用。\n- 模式 2：與具有 Carpet 模組的伺服器相容\n（包括 Skyrising 和 DeadlyMC 的 QuickCarpet，\n或者在 FabricCarpet 基礎上增加 CarpetExtra。\n在這兩種情況下，伺服器都需要啟用「accurateBlockPlacement」Carpet 規則）。\n- 僅半磚：只修正頂部半磚，與 Paper 伺服器相容\n- 關閉：不進行修正", "tweakeroo.config.generic.comment.afterClickerClickCount": "在 §6[放置後點擊]§r 啟用時，放置方塊後右鍵點擊的次數", "tweakeroo.config.generic.comment.angelBlockPlacementDistance": "啟用 §6[憑空放置]§r 時，玩家可以在空中放置方塊的距離。\n伺服器允許的最大距離是 5", "tweakeroo.config.generic.comment.areaSelectionUseAll": "Whether or not to include air in selection\nWritten by Andrew54757 under TweakFork", "tweakeroo.config.generic.comment.blockReachDistance": "如果啟用，將會調整方塊放置距離。\n遊戲允許的最大值是 64。\n§6不要嘗試使用 [0.5 - 1.0] 範圍以上的值，這將超出伺服器規則定義\n§c在一些伺服器會被定義為作弊！！§f", "tweakeroo.config.generic.comment.blockTypeBreakRestrictionWarn": "當 §6[方塊類型破壞限制]§r 阻止破壞方塊時，\n將顯示哪種類型的警告訊息（如果有的話）", "tweakeroo.config.generic.comment.breakingGridSize": "破壞間隔距離設定，按住此功能快捷鍵時滑動滾輪可以快速調整", "tweakeroo.config.generic.comment.breakingRestrictionMode": "選擇要使用的破壞限制模式（也可使用快捷鍵選擇）", "tweakeroo.config.generic.comment.bundleDisplayBgColor": "將 §6[束口袋預覽]§f 的預覽介面背景顏色設定為束口袋的顏色", "tweakeroo.config.generic.comment.bundleDisplayRequireShift": "是否需要按住 Shift 鍵才能預覽束口袋內的物品\n§6注意：停用此功能會導致原版束口袋的物品提示完全失效", "tweakeroo.config.generic.comment.bundleDisplayRowWidth": "Adjust the Row Width size of the Bundle Preview display,\nAnything smaller or larger will cause texture display problems.", "tweakeroo.config.generic.comment.chatBackgroundColor": "設定聊天欄背景顏色，需要開啟 §6[聊天背景顏色]§r 功能使用", "tweakeroo.config.generic.comment.chatTimeFormat": "§6[聊天時間顯示]§r 功能所顯示的時間格式。\n請使用 Java 的簡易日期格式", "tweakeroo.config.generic.comment.clientPlacementRotation": "允許在單人遊戲中放置旋轉後的方塊。\n功能類似 §6[精確放置]§r 但這不需要 §eCarpet Mod§f", "tweakeroo.config.generic.comment.customInventoryGuiScale": "如果啟用了 §6[自訂容器比例]§r，則可以調整容器 GUI 比例", "tweakeroo.config.generic.comment.debugLogging": "在遊戲控制台中啟用一些除錯日誌訊息，\n用於除錯特定問題或崩潰", "tweakeroo.config.generic.comment.darknessScaleOverrideValue": "The override value used by 'tweakDarknessVisibility'\nto automatically weaken the 'darkening' screen effect.\nThis is a percentage value for the amount\nthat the screen darkens for each effect pulse.", "tweakeroo.config.generic.comment.elytraCameraIndicator": "當使用 §6[環顧四周]§r 時，顯示仰角指示器", "tweakeroo.config.generic.comment.entityReachDistance": "如果啟用，將會調整實體互動距離。\n遊戲允許的最大值是64。\n§6不要嘗試使用 [0.5 - 1.0] 範圍以上的值，這將超出伺服器規則定義\n§c在一些伺服器會被定義為作弊！！§f", "tweakeroo.config.generic.comment.entityTypeAttackRestrictionWarn": "當 §6[實體類型攻擊限制]§r 阻止攻擊實體時，\n將顯示哪種類型的警告訊息（如果有的話）", "tweakeroo.config.generic.comment.fastBlockPlacementCount": "§6[方塊快速放置]§r 功能在每遊戲刻能夠放置方塊的最大數量", "tweakeroo.config.generic.comment.fastLeftClickAllowTools": "在生存當中開啟左鍵快速點擊將相容工具\n§b金合歡：你們挖區塊都不開連點的嗎？", "tweakeroo.config.generic.comment.fastLeftClickCount": "設定 §6[左鍵快速點擊]§r 的點擊頻率，數字為每遊戲刻點擊的次數", "tweakeroo.config.generic.comment.fastPlacementRememberOrientation": "如果啟用，那麼 §6[方塊快速放置]§r 功能將始終記住你放置的第一個方塊的方向。\n如果停用，方向只會在啟用並啟動 §6[方塊靈活放置]§r 的情況下被記住", "tweakeroo.config.generic.comment.fastRightClickCount": "設定 §6[右鍵快速點擊]§r 點擊頻率，數字為每遊戲刻點擊的次數", "tweakeroo.config.generic.comment.fillCloneLimit": "如果啟用 §6[填充方塊上限]§r 功能，\n修改單人模式下 §6[填充方塊上限]§r 功能的填充/複製指令方塊上限", "tweakeroo.config.generic.comment.flexibleBlockPlacementOverlayColor": "設定 §6[方塊靈活放置]§r 功能啟用時方塊上覆蓋層的顏色", "tweakeroo.config.generic.comment.flyDecelerationFactor": "如果啟用 §6[飛行減速]§r 功能，\n將調整玩家停止飛行時的速度", "tweakeroo.config.generic.comment.flySpeedIncrement1": "How much the fly speed changes for increment 1", "tweakeroo.config.generic.comment.flySpeedIncrement2": "How much the fly speed changes for increment 2", "tweakeroo.config.generic.comment.flySpeedPreset1": "飛行速度的預設值 1", "tweakeroo.config.generic.comment.flySpeedPreset2": "飛行速度的預設值 2", "tweakeroo.config.generic.comment.flySpeedPreset3": "飛行速度的預設值 3", "tweakeroo.config.generic.comment.flySpeedPreset4": "飛行速度的預設值 4", "tweakeroo.config.generic.comment.freeCameraPlayerInputs": "啟用後，你可以在 §6[靈魂出竅]§r 狀態下控制自己身體的攻擊和使用動作", "tweakeroo.config.generic.comment.freeCameraPlayerMovement": "啟用時，你可以在 §6[靈魂出竅]§r 狀態移動自己的身體，而不是移動靈魂出竅的靈魂", "tweakeroo.config.generic.comment.gammaOverrideValue": "啟用 §6[伽馬覆寫]§r 後，所覆寫的伽馬值", "tweakeroo.config.generic.comment.handRestockPre": "開啟後會在手上的物品耗盡前提前補充新的物品", "tweakeroo.config.generic.comment.handRestockPreThreshold": "在 §6[自動補貨]§r 模式下的補貨閾值", "tweakeroo.config.generic.comment.hangableEntityBypassInverse": "此功能需要啟用 §6[忽略懸掛實體]§r\n那麼這個選項控制玩家是否必須蹲下才能選中可懸掛實體（物品展示框或繪畫）\n> §a開啟時§r 潛行=忽略實體\n> §c關閉時§r 潛行=互動實體", "tweakeroo.config.generic.comment.hotbarSlotCycleMax": "設定 §6[物品欄循環捲動]§r 可以捲動到的最後一個欄位。\n當超過設定的最大值時，會返回第一個欄位", "tweakeroo.config.generic.comment.hotbarSlotRandomizerMax": "設定 §6[物品欄隨機捲動]§r 可以捲動到的最後一個欄位。\n當超過設定的最大值時，會返回第一個欄位", "tweakeroo.config.generic.comment.hotbarSwapOverlayAlignment": "設定顯示背包內容物預覽的位置", "tweakeroo.config.generic.comment.hotbarSwapOverlayOffsetX": "設定顯示背包內容物預覽的水準偏移量", "tweakeroo.config.generic.comment.hotbarSwapOverlayOffsetY": "設定顯示背包內容物預覽的垂直偏移量", "tweakeroo.config.generic.comment.inventoryPreviewVillagerBGColor": "Enable/Disable the Background Color\ndisplay for the Villager Trades based upon their Profession.", "tweakeroo.config.generic.comment.itemSwapDurabilityThreshold": "啟用 §6[工具防損壞]§r 後，低於該耐久的工具會被自動取代\n注意，總耐久度較低的物品會降低該閾值，並在剩餘 5%% 耐久度時進行取代", "tweakeroo.config.generic.comment.itemUsePacketCheckBypass": "該功能繞過了 1.18.2 中新加入的距離/座標校驗。\n\n該校驗破壞了 §6[精確放置協定]§r 的功能\n並導致任何帶有旋轉（或其他屬性）\n請求放置的方塊變成了幽靈方塊\n基本上沒有必要停用這個校驗。\n因為在 1.18.2 之前，這個校驗甚至都不存在", "tweakeroo.config.generic.comment.mapPreviewRequireShift": "是否需要按住 Shift 鍵才能預覽地圖", "tweakeroo.config.generic.comment.mapPreviewSize": "使用 §6[地圖預覽]§r 時地圖的尺寸", "tweakeroo.config.generic.comment.periodicAttackInterval": "在 §6[週期性點擊左鍵]§r 功能中，相鄰兩次點擊的間隔時間（單位：遊戲刻）", "tweakeroo.config.generic.comment.periodicAttackResetIntervalOnActivate": "如果使用滑鼠滾輪調整了週期性左鍵間隔，\n則在停用時重設該間隔", "tweakeroo.config.generic.comment.periodicHoldAttackDuration": "玩家持續按住左鍵的時間（單位：遊戲刻）", "tweakeroo.config.generic.comment.periodicHoldAttackInterval": "玩家間隔多久按住一次左鍵（單位：遊戲刻）", "tweakeroo.config.generic.comment.periodicHoldAttackResetIntervalOnActivate": "如果使用滑鼠滾輪調整了週期性按住左鍵間隔，\n則在停用時重設該間隔", "tweakeroo.config.generic.comment.periodicHoldUseDuration": "玩家持續按住右鍵的時間（單位：遊戲刻）", "tweakeroo.config.generic.comment.periodicHoldUseInterval": "玩家間隔多久按住一次右鍵（單位：遊戲刻）", "tweakeroo.config.generic.comment.periodicHoldUseResetIntervalOnActivate": "如果使用滑鼠滾輪調整了週期性按住右鍵間隔，\n則在停用時重設該間隔", "tweakeroo.config.generic.comment.periodicUseInterval": "在 §6[週期性點擊右鍵]§r 功能中，相鄰兩次點擊的間隔時間（單位：遊戲刻）", "tweakeroo.config.generic.comment.periodicUseResetIntervalOnActivate": "如果使用滑鼠滾輪調整了週期性右鍵間隔，\n則在停用時重設該間隔", "tweakeroo.config.generic.comment.permanentSneakAllowInGUIs": "開啟此功能後即便你開啟了遊戲內的任何介面，你仍然會保持潛行的狀態", "tweakeroo.config.generic.comment.placementGridSize": "當啟用了 §6[放置間距限制]§r 功能時，設定放置間距的大小。\n按住該功能快捷鍵時滑動滾輪可以快速調整", "tweakeroo.config.generic.comment.placementLimit": "當啟用了 §6[放置數量限制]§r 功能時，設定每次放置的方塊數量。\n按住該功能快捷鍵時滑動滾輪可以快速調整", "tweakeroo.config.generic.comment.placementRestrictionMode": "使用的放置模式限制（可使用快速鍵調節）", "tweakeroo.config.generic.comment.placementRestrictionTiedToFast": "啟用此功能後，當開啟/關閉 §6[方塊快速放置]§r 時，§6[放置範圍限制]§r 將同步開啟/關閉", "tweakeroo.config.generic.comment.potionWarningBeneficialOnly": "開啟後僅顯示正面效果的 §6[效果不足警報]§r", "tweakeroo.config.generic.comment.potionWarningThreshold": "當開啟 §6[效果不足警報]§r 時，當身上的效果剩餘時間小於此值時會對玩家發出提示", "tweakeroo.config.generic.comment.rememberFlexibleFromClick": "如果啟用，在按住使用鍵（右鍵點擊）後，§6[方塊靈活放置]§r 狀態將從第一個放置的方塊開始記住朝向。\n不需要一直按住 §6[方塊靈活放置]§r 的快捷鍵即可將所有方塊快速放置在同一方向", "tweakeroo.config.generic.comment.renderLimitItem": "設定 §6[限制實體繪製]§r 功能對§e掉落物§f的最大繪製數量。\n填寫 -1 代表不限制數量，即停用此限制", "tweakeroo.config.generic.comment.renderLimitXPOrb": "設定 §6[限制實體繪製]§r 功能對§e經驗球§f的最大繪製數量。\n填寫 -1 代表不限制數量，即停用此限制", "tweakeroo.config.generic.comment.sculkSensorPulseLength": "啟用 §6[調整伏聆植物脈衝距離]§r 時，該數值為伏聆植物發出的脈衝長度。（單位：遊戲刻）", "tweakeroo.config.generic.comment.selectiveBlocksHideEntities": "Whether or not to hide entities for selective block rendering\nWritten by Andrew54757 under TweakFork", "tweakeroo.config.generic.comment.selectiveBlocksHideParticles": "Whether or not to hide particles for selective block rendering\nWritten by Andrew54757 under TweakFork", "tweakeroo.config.generic.comment.selectiveBlocksNoHit": "Whether or not to disable targeting hidden blocks\nWritten by Andrew54757 under TweakFork", "tweakeroo.config.generic.comment.selectiveBlocksTrackPistons": "Whether or not to track piston movements for selective block rendering\nWritten by Andrew54757 under TweakFork", "tweakeroo.config.generic.comment.serverDataSyncCacheRefresh": "The Cache refresh value as a fraction of a second.\nThis value is the trigger for whenever requesting entity data\ngets refreshed from the server, even if it still exists in the Cache.\nThis should be set around 25% or less of\nthe \"entityDataSyncCacheTimeout\" value.\nA value of '0.05f' means 50 ms or once every game tick.", "tweakeroo.config.generic.comment.serverDataSyncCacheTimeout": "實體快取中逾時時間的值，以秒為單位。值越低，更新越頻繁", "tweakeroo.config.generic.comment.serverNbtRequestRate": "限制伺服器實體資料同步器的請求頻率", "tweakeroo.config.generic.comment.shulkerDisplayBgColor": "將 §6[界伏盒預覽]§r 的預覽介面背景顏色設定為界伏盒的顏色", "tweakeroo.config.generic.comment.shulkerDisplayEnderChest": "如果該選項啟用，當你按住 Shift 同時指向終界箱時，\n將顯示終界箱內的物品\n\n譯註：需要先啟用 §6[界伏盒預覽]§r", "tweakeroo.config.generic.comment.shulkerDisplayRequireShift": "界伏盒預覽是否需要按住 Shift 鍵", "tweakeroo.config.generic.comment.slotSyncWorkaround": "開啟此項可以防止伺服器在快速使用物品時（例如使用 §6[右鍵快速點擊]§r）覆蓋物品的耐久度或堆疊大小", "tweakeroo.config.generic.comment.slotSyncWorkaroundAlways": "啟用後，會在按住右鍵時始終啟用[同步物品欄]，\n而不僅僅是使用 §6[方塊快速放置]§r 或 §6[右鍵快速點擊]§r 時。\n這主要是為其他可能在按住右鍵時快速放置物品的模組設計，\n比如 §e[Litematica 模組]§r 的 §6[輕鬆放置功能]§r", "tweakeroo.config.generic.comment.snapAimIndicator": "是否顯示瞄準輔助指示器", "tweakeroo.config.generic.comment.snapAimIndicatorColor": "§6[瞄準輔助指示器]§r 的背景顏色", "tweakeroo.config.generic.comment.snapAimMode": "切換瞄準輔助模式：偏航角[← →]或俯仰角[↑ ↓]，或都顯示", "tweakeroo.config.generic.comment.snapAimOnlyCloseToAngle": "啟用該功能後那麼視角鎖定功能\n將僅在一定閾值內的角度時鎖定該角度。\n角度閾值可以在 §6[通用功能] -> [角度閾值（垂直/水平方向）]§f 中設定", "tweakeroo.config.generic.comment.snapAimPitchOvershoot": "是否允許俯仰角度從正常的 +/- 90度 \n擴展到 +/- 180度", "tweakeroo.config.generic.comment.snapAimPitchStep": "§6[瞄準輔助]§r 俯仰角度步進（單位°）", "tweakeroo.config.generic.comment.snapAimThresholdPitch": "當玩家的視角在這個角度閾值內時，\n玩家的視角旋轉將被捕獲到固定角度", "tweakeroo.config.generic.comment.snapAimThresholdYaw": "當玩家的視角在這個角度閾值內時，\n玩家的視角旋轉將被捕獲到固定角度", "tweakeroo.config.generic.comment.snapAimYawStep": "§6[瞄準輔助]§r 偏航角度步進（單位°）", "tweakeroo.config.generic.comment.structureBlockMaxSize": "修改 §6[變更結構方塊範圍上限]§r 功能結構方塊能儲存的最大範圍", "tweakeroo.config.generic.comment.toolSwapBetterEnchants": "在工具切換時，優先考慮工具是否匹配，\n之後考慮工具的附魔和稀有度", "tweakeroo.config.generic.comment.toolSwapPreferSilkTouch": "Consider your final Silk Touch preference\nwith tools whenever all other results are\nequal (Block Breaking Speed, Enchantments, Materials, etc).\nThis is useful for determining the default\npickaxe to choose when SilkTouchFirst is not\napplicable for the block that you are mining.\n§6NOTE: When this is disabled, the mod will always prefer\n§6to use the non-silk touch tool during the\n§6comparison if one is found and matches\n§6all of the criteria.", "tweakeroo.config.generic.comment.toolSwapBambooUsesSwordFirst": "Check if the targeted block is Bamboo,\nand if so; prefer using a Sword instead\nof an Axe to mine it.", "tweakeroo.config.generic.comment.toolSwapNeedsShearsFirst": "Check if the block requires Shears first,\nsuch as wool, vines or cobwebs.\nThe blocks chosen are based on the new\n'§b#malilib:needs_shears§r' Block Tag\nsince this doesn't exist in Vanilla.\n§6NOTE: This feature is only activate when\n§6the block is not properly matched in Vanilla.", "tweakeroo.config.generic.comment.toolSwapSilkTouchFirst": "Check if the block requires Silk Touch first,\nsuch as glass or an ender chest.\nThe blocks chosen is based on the new\n'§b#malilib:needs_silk_touch§r' Block Tag\nsince this doesn't exist in Vanilla.", "tweakeroo.config.generic.comment.toolSwapSilkTouchOres": "Check if the block is an Ore Block via the\n'§b#malilib:ore_blocks§r' tag, and then use\nSilk Touch First on it.\n§6NOTE: Disable this to use fortune.§r", "tweakeroo.config.generic.comment.toolSwapSilkTouchOverride": "Check the Silk Touch Override list for\nblocks to apply SilkTouchFirst to.\n§6NOTE: This can work without 'toolSwapSilkTouchFirst'\n§6being enabled, but it is designed to work\n§6the same, and can co-exist with it on.", "tweakeroo.config.generic.comment.toolSwitchIgnoredSlots": "當工具切換啟動時不進行選擇的欄位", "tweakeroo.config.generic.comment.toolSwitchableSlots": "工具選擇的物品欄調整為允許選擇的物品欄位。\n請注意，如果快捷欄欄位中的其他物品欄擁有首選工具，\n工具切換也可以切換到該工具所在的欄位，\n但新的工具只會交換到這裡指定的欄位", "tweakeroo.config.generic.comment.weaponSwapBetterEnchants": "在武器切換時，優先考慮武器是否匹配，\n之後慮武器的附魔和稀有度", "tweakeroo.config.generic.comment.zoomAdjustMouseSensitivity": "開啟此項後，在按下 §6[啟用望遠鏡]§r 快捷鍵時，會降低靈敏度", "tweakeroo.config.generic.comment.zoomFov": "設定使用望遠鏡時的視野角度", "tweakeroo.config.generic.comment.zoomResetFovOnActivate": "如果使用滑鼠滾輪調整了望遠鏡縮放視野，\n在停用時重設該視野", "tweakeroo.config.generic.name.accuratePlacementProtocol": "精確放置協定", "tweakeroo.config.generic.name.accuratePlacementProtocolMode": "精確放置協定模式", "tweakeroo.config.generic.name.afterClickerClickCount": "放置後點擊次數", "tweakeroo.config.generic.name.angelBlockPlacementDistance": "憑空放置距離", "tweakeroo.config.generic.name.areaSelectionUseAll": "areaSelectionUseAll", "tweakeroo.config.generic.name.blockReachDistance": "方塊放置距離", "tweakeroo.config.generic.name.blockTypeBreakRestrictionWarn": "破壞限制警告", "tweakeroo.config.generic.name.breakingGridSize": "破壞間隔調節", "tweakeroo.config.generic.name.breakingRestrictionMode": "破壞模式限制類型調節", "tweakeroo.config.generic.name.bundleDisplayBgColor": "彩色束口袋背景", "tweakeroo.config.generic.name.bundleDisplayRequireShift": "束口袋預覽需要 Shift", "tweakeroo.config.generic.name.bundleDisplayRowWidth": "bundleDisplayRowWidth", "tweakeroo.config.generic.name.chatBackgroundColor": "聊天背景顏色", "tweakeroo.config.generic.name.chatTimeFormat": "聊天時間格式", "tweakeroo.config.generic.name.clientPlacementRotation": "客戶端放置旋轉", "tweakeroo.config.generic.name.customInventoryGuiScale": "自訂容器比例", "tweakeroo.config.generic.name.debugLogging": "除錯日誌", "tweakeroo.config.generic.name.darknessScaleOverrideValue": "darknessScaleOverrideValue", "tweakeroo.config.generic.name.elytraCameraIndicator": "顯示仰角指示器", "tweakeroo.config.generic.name.entityReachDistance": "實體互動距離", "tweakeroo.config.generic.name.entityTypeAttackRestrictionWarn": "攻擊限制警告", "tweakeroo.config.generic.name.fastBlockPlacementCount": "快速放置數量", "tweakeroo.config.generic.name.fastLeftClickAllowTools": "左鍵連點工具相容", "tweakeroo.config.generic.name.fastLeftClickCount": "左鍵連點頻率", "tweakeroo.config.generic.name.fastPlacementRememberOrientation": "快速放置記憶", "tweakeroo.config.generic.name.fastRightClickCount": "右鍵連點頻率", "tweakeroo.config.generic.name.fillCloneLimit": "填充方塊上限", "tweakeroo.config.generic.name.flexibleBlockPlacementOverlayColor": "方塊靈活放置 GUI 顏色", "tweakeroo.config.generic.name.flyDecelerationFactor": "飛行停止速度", "tweakeroo.config.generic.name.flySpeedIncrement1": "flySpeedIncrement1", "tweakeroo.config.generic.name.flySpeedIncrement2": "flySpeedIncrement2", "tweakeroo.config.generic.name.flySpeedPreset1": "飛行速度預設 1", "tweakeroo.config.generic.name.flySpeedPreset2": "飛行速度預設 2", "tweakeroo.config.generic.name.flySpeedPreset3": "飛行速度預設 3", "tweakeroo.config.generic.name.flySpeedPreset4": "飛行速度預設 4", "tweakeroo.config.generic.name.freeCameraPlayerInputs": "靈魂出竅玩家操作", "tweakeroo.config.generic.name.freeCameraPlayerMovement": "靈魂出竅玩家移動", "tweakeroo.config.generic.name.gammaOverrideValue": "覆寫伽馬設定", "tweakeroo.config.generic.name.handRestockPre": "自動補貨", "tweakeroo.config.generic.name.handRestockPreThreshold": "自動補貨閾值", "tweakeroo.config.generic.name.hangableEntityBypassInverse": "懸掛實體互動方式", "tweakeroo.config.generic.name.hotbarSlotCycleMax": "物品欄循環上限", "tweakeroo.config.generic.name.hotbarSlotRandomizerMax": "物品欄隨機上限", "tweakeroo.config.generic.name.hotbarSwapOverlayAlignment": "背包顯示位置", "tweakeroo.config.generic.name.hotbarSwapOverlayOffsetX": "背包顯示 X 軸偏移", "tweakeroo.config.generic.name.hotbarSwapOverlayOffsetY": "背包顯示 Y 軸偏移", "tweakeroo.config.generic.name.inventoryPreviewVillagerBGColor": "inventoryPreviewVillagerBGColor", "tweakeroo.config.generic.name.itemSwapDurabilityThreshold": "工具取代耐久閾值", "tweakeroo.config.generic.name.itemUsePacketCheckBypass": "物品使用校驗", "tweakeroo.config.generic.name.mapPreviewRequireShift": "地圖預覽需按 Shift", "tweakeroo.config.generic.name.mapPreviewSize": "地圖預覽尺寸", "tweakeroo.config.generic.name.periodicAttackInterval": "點擊左鍵間隔", "tweakeroo.config.generic.name.periodicAttackResetIntervalOnActivate": "點擊左鍵間隔重設", "tweakeroo.config.generic.name.periodicHoldAttackDuration": "左鍵按住時間", "tweakeroo.config.generic.name.periodicHoldAttackInterval": "左鍵按住間隔", "tweakeroo.config.generic.name.periodicHoldAttackResetIntervalOnActivate": "左鍵按住間隔重設", "tweakeroo.config.generic.name.periodicHoldUseDuration": "右鍵按住時間", "tweakeroo.config.generic.name.periodicHoldUseInterval": "右鍵按住間隔", "tweakeroo.config.generic.name.periodicHoldUseResetIntervalOnActivate": "右鍵按住間隔重設", "tweakeroo.config.generic.name.periodicUseInterval": "點擊右鍵間隔", "tweakeroo.config.generic.name.periodicUseResetIntervalOnActivate": "點擊右鍵間隔重設", "tweakeroo.config.generic.name.permanentSneakAllowInGUIs": "永久潛行", "tweakeroo.config.generic.name.placementGridSize": "放置間距大小", "tweakeroo.config.generic.name.placementLimit": "放置數量限制", "tweakeroo.config.generic.name.placementRestrictionMode": "放置模式限制", "tweakeroo.config.generic.name.placementRestrictionTiedToFast": "放置範圍限制綁定", "tweakeroo.config.generic.name.potionWarningBeneficialOnly": "僅顯示正面效果警報", "tweakeroo.config.generic.name.potionWarningThreshold": "效果警報閾值", "tweakeroo.config.generic.name.rememberFlexibleFromClick": "方塊靈活放置記憶", "tweakeroo.config.generic.name.renderLimitItem": "掉落物數量限制", "tweakeroo.config.generic.name.renderLimitXPOrb": "經驗球數量限制", "tweakeroo.config.generic.name.sculkSensorPulseLength": "伏聆植物脈衝距離", "tweakeroo.config.generic.name.selectiveBlocksHideEntities": "selectiveBlocksHideEntities", "tweakeroo.config.generic.name.selectiveBlocksHideParticles": "selectiveBlocksHideParticles", "tweakeroo.config.generic.name.selectiveBlocksNoHit": "selectiveBlocksNoHit", "tweakeroo.config.generic.name.selectiveBlocksTrackPistons": "selectiveBlocksTrackPistons", "tweakeroo.config.generic.name.serverDataSyncCacheRefresh": "serverDataSyncCacheRefresh", "tweakeroo.config.generic.name.serverDataSyncCacheTimeout": "伺服器資料同步快取逾時", "tweakeroo.config.generic.name.serverNbtRequestRate": "伺服器 NBT 請求頻率", "tweakeroo.config.generic.name.shulkerDisplayBgColor": "彩色界伏盒預覽", "tweakeroo.config.generic.name.shulkerDisplayEnderChest": "終界箱預覽", "tweakeroo.config.generic.name.shulkerDisplayRequireShift": "界伏盒預覽需按 Shift", "tweakeroo.config.generic.name.slotSyncWorkaround": "同步物品欄", "tweakeroo.config.generic.name.slotSyncWorkaroundAlways": "一直同步物品欄", "tweakeroo.config.generic.name.snapAimIndicator": "瞄準輔助指示器", "tweakeroo.config.generic.name.snapAimIndicatorColor": "瞄準輔助指示器顏色", "tweakeroo.config.generic.name.snapAimMode": "瞄準輔助模式", "tweakeroo.config.generic.name.snapAimOnlyCloseToAngle": "預設角度內鎖定視角", "tweakeroo.config.generic.name.snapAimPitchOvershoot": "瞄準輔助俯仰角增大", "tweakeroo.config.generic.name.snapAimPitchStep": "瞄準輔助俯仰角步進", "tweakeroo.config.generic.name.snapAimThresholdPitch": "角度閾值（垂直方向）", "tweakeroo.config.generic.name.snapAimThresholdYaw": "角度閾值（水平方向）", "tweakeroo.config.generic.name.snapAimYawStep": "瞄準輔助偏航角步進", "tweakeroo.config.generic.name.structureBlockMaxSize": "結構方塊範圍上限", "tweakeroo.config.generic.name.toolSwapBetterEnchants": "切換附魔更好的工具", "tweakeroo.config.generic.name.toolSwapPreferSilkTouch": "toolSwapPreferSilkTouch", "tweakeroo.config.generic.name.toolSwapBambooUsesSwordFirst": "toolSwapBambooUsesSwordFirst", "tweakeroo.config.generic.name.toolSwapNeedsShearsFirst": "toolSwapNeedsShearsFirst", "tweakeroo.config.generic.name.toolSwapSilkTouchFirst": "toolSwapSilkTouchFirst", "tweakeroo.config.generic.name.toolSwapSilkTouchOres": "toolSwapSilkTouchOres", "tweakeroo.config.generic.name.toolSwapSilkTouchOverride": "toolSwapSilkTouchOverride", "tweakeroo.config.generic.name.toolSwitchIgnoredSlots": "工具選擇忽略欄位", "tweakeroo.config.generic.name.toolSwitchableSlots": "工具選擇範圍", "tweakeroo.config.generic.name.weaponSwapBetterEnchants": "切換附魔更好的武器", "tweakeroo.config.generic.name.zoomAdjustMouseSensitivity": "調整望遠鏡縮放靈敏度", "tweakeroo.config.generic.name.zoomFov": "望遠鏡視角廣度", "tweakeroo.config.generic.name.zoomResetFovOnActivate": "望遠鏡視角廣度重設", "tweakeroo.config.generic.prettyName.freeCameraPlayerInputs": "靈魂出竅玩家操作", "tweakeroo.config.generic.prettyName.freeCameraPlayerMovement": "靈魂出竅玩家移動", "tweakeroo.config.generic.prettyName.toolSwapBetterEnchants": "Tool Swap Better Enchants", "tweakeroo.config.generic.prettyName.toolSwapBambooUsesSwordFirst": "Tool Swap Bamboo Uses Sword First", "tweakeroo.config.generic.prettyName.toolSwapNeedsShearsFirst": "Tool Swap Needs Shears First", "tweakeroo.config.generic.prettyName.toolSwapSilkTouchFirst": "Tool Swap Silk Touch First", "tweakeroo.config.generic.prettyName.toolSwapPreferSilkTouch": "Tool Swap Prefer Silk Touch", "tweakeroo.config.generic.prettyName.toolSwapSilkTouchOres": "Tool Swap Silk Touch Ores", "tweakeroo.config.generic.prettyName.toolSwapSilkTouchOverride": "Tool Swap Silk Touch Override", "tweakeroo.config.generic.prettyName.weaponSwapBetterEnchants": "Weapon Swap Better Enchants", "tweakeroo.config.hotkey.comment.accurateBlockPlacementInto": "啟動 §6[精確放置]§r 的快捷鍵，使用後方塊將放置於點擊的塊面上", "tweakeroo.config.hotkey.comment.accurateBlockPlacementReverse": "啟動 §6[精確放置]§r 的快捷鍵，使用後方塊將反向放置於點擊的塊面上", "tweakeroo.config.hotkey.comment.areaSelectionAddToList": "Add selected blocks to list\nWritten by Andrew54757 under TweakFork", "tweakeroo.config.hotkey.comment.areaSelectionOffset": "The key to offset selection pos\nWritten by Andrew54757 under TweakFork", "tweakeroo.config.hotkey.comment.areaSelectionRemoveFromList": "remove selected blocks from list\nWritten by Andrew54757 under TweakFork", "tweakeroo.config.hotkey.comment.breakingRestrictionModeColumn": "將破壞範圍限制在 目前直線", "tweakeroo.config.hotkey.comment.breakingRestrictionModeDiagonal": "將破壞範圍限制在 對角線", "tweakeroo.config.hotkey.comment.breakingRestrictionModeFace": "將破壞範圍限制在 面朝方向", "tweakeroo.config.hotkey.comment.breakingRestrictionModeLayer": "將破壞範圍限制在 同水平面上", "tweakeroo.config.hotkey.comment.breakingRestrictionModeLine": "將破壞範圍限制在  放置方塊的稜邊上", "tweakeroo.config.hotkey.comment.breakingRestrictionModePlane": "將破壞範圍限制在 同平面上", "tweakeroo.config.hotkey.comment.copySignText": "開啟後上一個告示牌中輸入的文字將作為初始值輸入至下一個告示牌", "tweakeroo.config.hotkey.comment.elytraCamera": "按住此快捷鍵會鎖定玩家目前的視角方向，\n使玩家滑鼠移動僅改變攝影機視角而不改變移動方向。\n套用此功能需要開啟§6[工具] -> [環顧四周]§r\n\n非常適合你在鞘翅飛行時回頭或向四周看", "tweakeroo.config.hotkey.comment.flexibleBlockPlacementAdjacent": "按下以啟動 §6[方塊靈活放置]§r，按下該按鍵時可將方塊放置在目前方塊的 相鄰 的位置", "tweakeroo.config.hotkey.comment.flexibleBlockPlacementOffset": "按下以 §6[方塊靈活放置]§r，按下該按鍵時可將方塊放置在目前方塊的 斜角或隔空 的位置", "tweakeroo.config.hotkey.comment.flexibleBlockPlacementRotation": "按下以啟動 §6[方塊靈活放置]§r，按下該按鍵時可將方塊以選定的方向放置", "tweakeroo.config.hotkey.comment.flyIncrement1": "Change fly speed by increment 1", "tweakeroo.config.hotkey.comment.flyIncrement2": "Change fly speed by increment 2", "tweakeroo.config.hotkey.comment.flyPreset1": "切換至飛行速度預設 1", "tweakeroo.config.hotkey.comment.flyPreset2": "切換至飛行速度預設 2", "tweakeroo.config.hotkey.comment.flyPreset3": "切換至飛行速度預設 3", "tweakeroo.config.hotkey.comment.flyPreset4": "切換至飛行速度預設 4", "tweakeroo.config.hotkey.comment.freeCameraPlayerInputs": "用於切換 §6[通用功能] -> [靈魂出竅玩家操作]§r 功能的開啟與關閉", "tweakeroo.config.hotkey.comment.freeCameraPlayerMovement": "用於切換 §6[通用功能] -> [靈魂出竅玩家移動]§r 功能的開啟與關閉", "tweakeroo.config.hotkey.comment.hotbarScroll": "按下快捷鍵時，可以按行循環捲動物品欄", "tweakeroo.config.hotkey.comment.hotbarSwap1": "按下快捷鍵時，將物品欄與背包頂層一行交換", "tweakeroo.config.hotkey.comment.hotbarSwap2": "按下快捷鍵時，將物品欄與背包中層一行交換", "tweakeroo.config.hotkey.comment.hotbarSwap3": "按下快捷鍵時，將物品欄與背包底層一行交換", "tweakeroo.config.hotkey.comment.hotbarSwapBase": "按下快捷鍵時，將顯示背包內容物\n顯示位置可在 §6[通用功能] -> [背包顯示位置]§r 處變更", "tweakeroo.config.hotkey.comment.inventoryPreview": "按下快捷鍵時，可預覽容器內的物品。", "tweakeroo.config.hotkey.comment.inventoryPreviewToggleScreen": "按下後釋放滑鼠指標，以便檢視具體的物品資訊。", "tweakeroo.config.hotkey.comment.openConfigGui": "開啟 Tweakeroo 設定介面的快捷鍵", "tweakeroo.config.hotkey.comment.placementRestrictionModeColumn": "將快速放置的方塊放置範圍限制在 目前直線", "tweakeroo.config.hotkey.comment.placementRestrictionModeDiagonal": "將快速放置的方塊放置範圍限制在 對角線 \n§6注意：此功能無法憑空放置方塊§f", "tweakeroo.config.hotkey.comment.placementRestrictionModeFace": "將快速放置的方塊放置範圍限制在 面朝方向", "tweakeroo.config.hotkey.comment.placementRestrictionModeLayer": "將快速放置的方塊放置範圍限制在 同水平面上", "tweakeroo.config.hotkey.comment.placementRestrictionModeLine": "將快速放置的方塊放置範圍限制在 放置方塊的稜邊上", "tweakeroo.config.hotkey.comment.placementRestrictionModePlane": "將快速放置的方塊放置範圍限制在 同平面上", "tweakeroo.config.hotkey.comment.placementYMirror": "按下快捷鍵時，將方塊以 Y 軸鏡像的方式放置出來。\n需要開啟 §6[工具] -> [Y軸鏡像放置]§r", "tweakeroo.config.hotkey.comment.playerInventoryPeek": "啟動 §6[玩家背包預覽]§r 功能的快速鍵", "tweakeroo.config.hotkey.comment.sitDownNearbyPets": "讓附近所有的寵物坐下", "tweakeroo.config.hotkey.comment.skipAllRendering": "開啟此項後，所有的繪製運算都會被跳過", "tweakeroo.config.hotkey.comment.skipWorldRendering": "開啟此項後，所有關於世界的繪製運算都會被跳過", "tweakeroo.config.hotkey.comment.standUpNearbyPets": "讓附近所有的寵物站起", "tweakeroo.config.hotkey.comment.swapElytraChestplate": "按下快捷鍵時，更換胸部裝備為鞘翅/胸甲", "tweakeroo.config.hotkey.comment.toggleAccuratePlacementProtocol": "切換 §6[通用功能] -> [精確放置協定]§r 選項的值", "tweakeroo.config.hotkey.comment.toggleGrabCursor": "將滑鼠從目前視窗切換到外部視窗，在外部視窗的滑鼠操作不影響遊戲內操作", "tweakeroo.config.hotkey.comment.toolPick": "按下快捷鍵後切換到適合採集目前方塊的工具", "tweakeroo.config.hotkey.comment.writeMapsAsImages": "將所有目前可用的地圖作為影像寫入到\n「config/tweakeroo/map images/<world name>/」目錄", "tweakeroo.config.hotkey.comment.zoomActivate": "按下快捷鍵時，啟用望遠鏡", "tweakeroo.config.hotkey.name.accurateBlockPlacementInto": "精準放置模式", "tweakeroo.config.hotkey.name.accurateBlockPlacementReverse": "精準放置模式（反向）", "tweakeroo.config.hotkey.name.areaSelectionAddToList": "areaSelectionAddToList", "tweakeroo.config.hotkey.name.areaSelectionOffset": "areaSelectionOffset", "tweakeroo.config.hotkey.name.areaSelectionRemoveFromList": "areaSelectionRemoveFromList", "tweakeroo.config.hotkey.name.breakingRestrictionModeColumn": "破壞範圍限制 直線 ", "tweakeroo.config.hotkey.name.breakingRestrictionModeDiagonal": "破壞範圍限制 對角線 ", "tweakeroo.config.hotkey.name.breakingRestrictionModeFace": "破壞範圍限制 面朝", "tweakeroo.config.hotkey.name.breakingRestrictionModeLayer": "破壞範圍限制 水平面", "tweakeroo.config.hotkey.name.breakingRestrictionModeLine": "破壞範圍限制 稜邊", "tweakeroo.config.hotkey.name.breakingRestrictionModePlane": "破壞範圍限制 平面", "tweakeroo.config.hotkey.name.copySignText": "告示牌文字複製", "tweakeroo.config.hotkey.name.elytraCamera": "環顧四周", "tweakeroo.config.hotkey.name.flexibleBlockPlacementAdjacent": "方塊靈活放置（相鄰）", "tweakeroo.config.hotkey.name.flexibleBlockPlacementOffset": "方塊靈活放置（稜角）", "tweakeroo.config.hotkey.name.flexibleBlockPlacementRotation": "方塊靈活放置（旋轉）", "tweakeroo.config.hotkey.name.flyIncrement1": "flyIncrement1", "tweakeroo.config.hotkey.name.flyIncrement2": "flyIncrement2", "tweakeroo.config.hotkey.name.flyPreset1": "飛行預設 1", "tweakeroo.config.hotkey.name.flyPreset2": "飛行預設 2", "tweakeroo.config.hotkey.name.flyPreset3": "飛行預設 3", "tweakeroo.config.hotkey.name.flyPreset4": "飛行預設 4", "tweakeroo.config.hotkey.name.freeCameraPlayerInputs": "靈魂出竅玩家操作", "tweakeroo.config.hotkey.name.freeCameraPlayerMovement": "靈魂出竅玩家移動", "tweakeroo.config.hotkey.name.hotbarScroll": "物品欄捲動", "tweakeroo.config.hotkey.name.hotbarSwap1": "頂層快捷切換", "tweakeroo.config.hotkey.name.hotbarSwap2": "中層快捷切換", "tweakeroo.config.hotkey.name.hotbarSwap3": "底層快捷切換", "tweakeroo.config.hotkey.name.hotbarSwapBase": "顯示背包內容物", "tweakeroo.config.hotkey.name.inventoryPreview": "容器預覽（已棄用）§f", "tweakeroo.config.hotkey.name.inventoryPreviewToggleScreen": "容器預覽滑鼠釋放", "tweakeroo.config.hotkey.name.openConfigGui": "開啟設定介面", "tweakeroo.config.hotkey.name.placementRestrictionModeColumn": "方塊放置限制 直線", "tweakeroo.config.hotkey.name.placementRestrictionModeDiagonal": "方塊放置限制 對角線", "tweakeroo.config.hotkey.name.placementRestrictionModeFace": "方塊放置限制 面朝", "tweakeroo.config.hotkey.name.placementRestrictionModeLayer": "方塊放置限制 水平面", "tweakeroo.config.hotkey.name.placementRestrictionModeLine": "方塊放置限制 稜邊", "tweakeroo.config.hotkey.name.placementRestrictionModePlane": "方塊放置限制 平面", "tweakeroo.config.hotkey.name.placementYMirror": "Y 軸鏡像放置", "tweakeroo.config.hotkey.name.playerInventoryPeek": "玩家背包預覽", "tweakeroo.config.hotkey.name.sitDownNearbyPets": "坐下！", "tweakeroo.config.hotkey.name.skipAllRendering": "跳過全部繪製", "tweakeroo.config.hotkey.name.skipWorldRendering": "跳過世界繪製", "tweakeroo.config.hotkey.name.standUpNearbyPets": "起來！", "tweakeroo.config.hotkey.name.swapElytraChestplate": "鞘翅胸甲切換（手動）", "tweakeroo.config.hotkey.name.toggleAccuratePlacementProtocol": "精準放置協定", "tweakeroo.config.hotkey.name.toggleGrabCursor": "切換滑鼠", "tweakeroo.config.hotkey.name.toolPick": "自動選擇工具", "tweakeroo.config.hotkey.name.writeMapsAsImages": "地圖匯出", "tweakeroo.config.hotkey.name.zoomActivate": "啟用望遠鏡", "tweakeroo.config.internal.comment.darknessScaleValueOriginal": "The original darkness scale value, before the 'tweak darkness visibility' was enabled", "tweakeroo.config.internal.comment.flySpeedPreset": "用於內部跟蹤目前選擇的飛行速度預設", "tweakeroo.config.internal.comment.gammaValueOriginal": "在啟用 §6[伽馬覆寫]§r 之前的原始亮度值", "tweakeroo.config.internal.comment.hotbarScrollCurrentRow": "這只是用於模組內部跟蹤「目前物品欄」，以支援 §6[物品欄捲動]§r 功能", "tweakeroo.config.internal.comment.slimeBlockSlipperinessOriginal": "黏液塊的原始滑動係數", "tweakeroo.config.internal.comment.snapAimLastPitch": "上次瞄準的俯仰角", "tweakeroo.config.internal.comment.snapAimLastYaw": "上次瞄準的偏航角", "tweakeroo.config.internal.name.darknessScaleValueOriginal": "darknessScaleValueOriginal", "tweakeroo.config.internal.name.flySpeedPreset": "飛行速度預設", "tweakeroo.config.internal.name.gammaValueOriginal": "原始亮度值", "tweakeroo.config.internal.name.hotbarScrollCurrentRow": "物品欄循環使用目前行", "tweakeroo.config.internal.name.slimeBlockSlipperinessOriginal": "黏液塊滑動係數", "tweakeroo.config.internal.name.snapAimLastPitch": "瞄準輔助上次俯仰角", "tweakeroo.config.internal.name.snapAimLastYaw": "瞄準輔助上次偏航角", "tweakeroo.config.lists.comment.blockTypeBreakRestrictionBlackList": "啟用 §6[方塊破壞限制]§r 功能時，如果在 §6[方塊破壞限制類型]§r 中設定為黑名單，此類方塊將無法被破壞", "tweakeroo.config.lists.comment.blockTypeBreakRestrictionListType": "使用 §6[方塊破壞限制]§r 時的限制類型", "tweakeroo.config.lists.comment.blockTypeBreakRestrictionWhiteList": "啟用 §6[方塊破壞限制]§r 功能時，如果在 §6[方塊破壞限制類型]§r 中設定為白名單，此類方塊將只可被破壞", "tweakeroo.config.lists.comment.creativeExtraItems": "在創造模式物品選單中的 [交通運輸] 中加入額外物品", "tweakeroo.config.lists.comment.entityTypeAttackRestrictionBlackList": "啟用 §6[實體攻擊限制]§r 功能時，如果在 §6[實體攻擊限制類型]§r 中設定為黑名單，這些實體將不可被攻擊", "tweakeroo.config.lists.comment.entityTypeAttackRestrictionListType": "使用 §6[實體攻擊限制]§r 時的限制類型", "tweakeroo.config.lists.comment.entityTypeAttackRestrictionWhiteList": "啟用 §6[實體攻擊限制]§r 功能時，如果在 §6[實體攻擊限制類型]§r 中設定為白名單，這些實體只可被攻擊", "tweakeroo.config.lists.comment.entityWeaponMapping": "透過開啟 §6[武器自動選擇]§r 選項來選擇應該使用的武器。\n- <default>：將在沒有定義其他實體時使用\n- <ignore>：不會觸發武器切換", "tweakeroo.config.lists.comment.fastPlacementItemBlackList": "啟用 §6[快速放置方塊]§r 功能時，如果在 §6[快速放置限制類型]§r 中設定為黑名單，\n該列表中的方塊均不可用於快速放置", "tweakeroo.config.lists.comment.fastPlacementItemListType": "使用 §6[快速放置方塊]§r 時的限制類型", "tweakeroo.config.lists.comment.fastPlacementItemWhiteList": "啟用 §6[快速放置方塊]§r 功能時，如果在 §6[快速放置限制類型]§r 中設定為白名單，\n那麼只有該列表中的方塊可用於快速放置", "tweakeroo.config.lists.comment.fastRightClickBlackList": "啟用 §6[右鍵快速點擊]§r 功能時，如果在 §6[快速右鍵物品限制類型]§r 中設定為黑名單，則這些物品不允許用於右鍵快速點擊", "tweakeroo.config.lists.comment.fastRightClickBlockBlackList": "啟用[右鍵快速點擊]功能時，如果在§6[快速右鍵方塊限制類型]§f中設定為黑名單，\n該列表中的方塊均不可用於右鍵快速點擊", "tweakeroo.config.lists.comment.fastRightClickBlockListType": "使用§6[右鍵快速點擊]§f時的限制類型", "tweakeroo.config.lists.comment.fastRightClickBlockWhiteList": "啟用 §6[右鍵快速點擊]§r 功能時，如果在 §6[快速右鍵方塊限制類型]§r 中設定為白名單，\n那麼只有該列表中的方塊可用於右鍵快速點擊", "tweakeroo.config.lists.comment.fastRightClickListType": "使用 §6[右鍵快速點擊]§r 時的限制類型", "tweakeroo.config.lists.comment.fastRightClickWhiteList": "啟用 §6[右鍵快速點擊]§r 功能時，如果在 §6[快速右鍵物品限制類型]§r 中設定為白名單，\n只有手持該列表中物品時能夠使用右鍵快速點擊", "tweakeroo.config.lists.comment.flatWorldPresets": "自訂超平坦世界方案\n格式為：\n名稱；方塊類型；生態域；地形；圖示（使用英文符號）\n例如：62*minecraft:dirt, minecraft:grass\n生態域可以使用數字 ID 或註冊名\n圖示請使用原版格式，如：minecraft:iron_nugget", "tweakeroo.config.lists.comment.handRestockBlackList": "啟用 §6[自動補貨]§r 功能時，如果在 §6[自動補貨限制類型]§r 中設定為黑名單，\n那麼自動補貨時將無法補充此類物品", "tweakeroo.config.lists.comment.handRestockListType": "使用 §6[自動補貨]§r 時的限制類型", "tweakeroo.config.lists.comment.handRestockWhiteList": "啟用 §6[自動補貨]§r 功能時，如果在 §6[自動補貨限制類型]§r 中設定為白名單，\n那麼自動補貨時將只能補充此類物品", "tweakeroo.config.lists.comment.potionWarningBlackList": "不會被提醒的藥水效果列表", "tweakeroo.config.lists.comment.potionWarningListType": "使用 §6[效果不足警報]§r 時的限制類型", "tweakeroo.config.lists.comment.potionWarningWhiteList": "會被提醒的藥水效果列表", "tweakeroo.config.lists.comment.silkTouchOverride": "When using tweak tool switch, add blocks or\nblock tags to this list to apply SilkTouchFirst to,\nin addition to using the '§b#malilib:needs_silk_touch§r' Block Tag\nbased 'toolSwapSilkTouchFirst' method when\n'toolSwapSilkTouchOverride' is enabled.\n\nA useful example, would be adding\n'§bminecraft:stone§r' to this list.\n\nThe Block Tag exists, so that you don't need\nto add dozens of blocks to a list like this.\nBut; You can configure this list to work this way\nby disabling 'toolSwapSilkTouchFirst' and only enabling\n`toolSwapSilkTouchOverride` instead.\nYou may then want to make use of some of\nthe existing `§b#malilib:§r` or '§b#minecraft:§r' block tags here;\nsuch as adding '§b#malilib:glass_blocks§r', for example\ninstead of typing in all 18 glass blocks one by one.", "tweakeroo.config.lists.comment.repairModeSlots": "修補模式套用的範圍。\n有效值：\n> 慣用手（mainhand）\n> 非慣用手（offhand）\n> 頭部（head）\n> 胸部（chest）\n> 腿部（legs）\n> 鞋子（feet）", "tweakeroo.config.lists.comment.selectiveBlocksBlacklist": "The block positions you want to blacklist\nWritten by Andrew54757 under TweakFork", "tweakeroo.config.lists.comment.selectiveBlocksListType": "The list type for selective blocks tweak\nWritten by Andrew54757 under TweakFork", "tweakeroo.config.lists.comment.selectiveBlocksWhitelist": "The block positions you want to whitelist\nWritten by Andrew54757 under TweakFork", "tweakeroo.config.lists.comment.unstackingItems": "功能 §6[不可堆疊物品保護]§r 所保護的物品", "tweakeroo.config.lists.name.blockTypeBreakRestrictionBlackList": "方塊破壞限制黑名單", "tweakeroo.config.lists.name.blockTypeBreakRestrictionListType": "方塊破壞限制類型", "tweakeroo.config.lists.name.blockTypeBreakRestrictionWhiteList": "方塊破壞限制白名單", "tweakeroo.config.lists.name.creativeExtraItems": "創造額外物品", "tweakeroo.config.lists.name.entityTypeAttackRestrictionBlackList": "實體攻擊限制黑名單", "tweakeroo.config.lists.name.entityTypeAttackRestrictionListType": "實體攻擊限制類型", "tweakeroo.config.lists.name.entityTypeAttackRestrictionWhiteList": "實體攻擊限制白名單", "tweakeroo.config.lists.name.entityWeaponMapping": "武器切換規則", "tweakeroo.config.lists.name.fastPlacementItemBlackList": "快速放置黑名單", "tweakeroo.config.lists.name.fastPlacementItemListType": "快速放置限制類型", "tweakeroo.config.lists.name.fastPlacementItemWhiteList": "快速放置白名單", "tweakeroo.config.lists.name.fastRightClickBlackList": "快速右鍵物品黑名單", "tweakeroo.config.lists.name.fastRightClickBlockBlackList": "快速右鍵方塊黑名單", "tweakeroo.config.lists.name.fastRightClickBlockListType": "快速右鍵方塊限制類型", "tweakeroo.config.lists.name.fastRightClickBlockWhiteList": "快速右鍵方塊白名單", "tweakeroo.config.lists.name.fastRightClickListType": "快速右鍵物品限制類型", "tweakeroo.config.lists.name.fastRightClickWhiteList": "快速右鍵物品白名單", "tweakeroo.config.lists.name.flatWorldPresets": "超平坦世界方案", "tweakeroo.config.lists.name.handRestockBlackList": "自動補貨黑名單", "tweakeroo.config.lists.name.handRestockListType": "自動補貨限制類型", "tweakeroo.config.lists.name.handRestockWhiteList": "自動補貨白名單", "tweakeroo.config.lists.name.potionWarningBlackList": "效果不足警報黑名單", "tweakeroo.config.lists.name.potionWarningListType": "效果不足警報限制類型", "tweakeroo.config.lists.name.potionWarningWhiteList": "效果不足警報白名單", "tweakeroo.config.lists.name.silkTouchOverride": "silkTouchOverride", "tweakeroo.config.lists.name.repairModeSlots": "修補模式的套用範圍", "tweakeroo.config.lists.name.selectiveBlocksBlacklist": "selectiveBlocksBlacklist", "tweakeroo.config.lists.name.selectiveBlocksListType": "selectiveBlocksListType", "tweakeroo.config.lists.name.selectiveBlocksWhitelist": "selective<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tweakeroo.config.lists.name.unstackingItems": "不可堆疊物品保護", "tweakeroo.gui.button.config_gui.disables": "停用類功能", "tweakeroo.gui.button.config_gui.fixes": "修復", "tweakeroo.gui.button.config_gui.generic": "通用功能", "tweakeroo.gui.button.config_gui.generic_hotkeys": "通用功能快捷鍵", "tweakeroo.gui.button.config_gui.generic_config_hotkeys": "Generic Hotkeys", "tweakeroo.gui.button.config_gui.lists": "設定表單", "tweakeroo.gui.button.config_gui.placement": "方塊放置模式", "tweakeroo.gui.button.config_gui.tweaks": "工具", "tweakeroo.gui.button.misc.command_block.hover.update_execution": "是否允許每個 Tick 內進行多次觸發", "tweakeroo.gui.button.misc.command_block.set_name": "命名", "tweakeroo.gui.button.misc.command_block.update_execution.looping": "循環", "tweakeroo.gui.button.misc.command_block.update_execution.off": "循環：§c關閉", "tweakeroo.gui.button.misc.command_block.update_execution.on": "循環：§a開啟", "tweakeroo.gui.label.easy_place_protocol.auto": "自動", "tweakeroo.gui.label.easy_place_protocol.none": "關閉", "tweakeroo.gui.label.easy_place_protocol.slabs_only": "僅半磚", "tweakeroo.gui.label.easy_place_protocol.v2": "模式 2", "tweakeroo.gui.label.easy_place_protocol.v3": "模式 3", "tweakeroo.gui.title.configs": "Tweakeroo 設定介面 - %s", "tweakeroo.hotkeys.category.disable_toggle_hotkeys": "停用快捷鍵切換", "tweakeroo.hotkeys.category.generic_hotkeys": "通用功能快捷鍵", "tweakeroo.hotkeys.category.tweak_toggle_hotkeys": "開啟快捷鍵切換", "tweakeroo.label.config_comment.single_player_only": "§6注意：此功能要麼完全無法執行§r\n§6或者至少只能在單人遊戲中執行§r", "tweakeroo.label.placement_restriction_mode.column": "列", "tweakeroo.label.placement_restriction_mode.diagonal": "對角線", "tweakeroo.label.placement_restriction_mode.face": "面", "tweakeroo.label.placement_restriction_mode.layer": "層", "tweakeroo.label.placement_restriction_mode.line": "線", "tweakeroo.label.placement_restriction_mode.plane": "平面", "tweakeroo.label.snap_aim_mode.both": "偏航角與俯仰角", "tweakeroo.label.snap_aim_mode.pitch": "俯仰角", "tweakeroo.label.snap_aim_mode.yaw": "偏航角", "tweakeroo.message.death_coordinates": "§6你死亡了§r @ [§b%d, %d, %d§r] 在 §a%s", "tweakeroo.message.focusing_game": "§6聚焦§f 遊戲（取得游標）", "tweakeroo.message.potion_effects_running_out": "§6警告：%s 種藥水效果將於 %s 秒後消失！！§r", "tweakeroo.message.repair_mode.swapped_repairable_item_to_slot": "將可修復的物品移至第 %s 格中", "tweakeroo.message.set_after_clicker_count_to": "將快速點擊的次數修改為 %s", "tweakeroo.message.set_breaking_grid_size_to": "設定破壞範圍 %s", "tweakeroo.message.set_breaking_restriction_mode_to": "設定破壞模式 %s", "tweakeroo.message.set_fly_speed_preset_to": "調整預設的飛行速度 %s（速度：%s）", "tweakeroo.message.set_fly_speed_to": "將飛行速度由 %s 調整至 %s", "tweakeroo.message.set_hotbar_slot_cycle_max_to": "設定快捷欄欄位循環的最大值 %s", "tweakeroo.message.set_hotbar_slot_randomizer_max_to": "設定快捷欄欄位的最大隨機欄位至 %s", "tweakeroo.message.set_periodic_attack_interval_to": "設定週期性左鍵間隔為 %s", "tweakeroo.message.set_periodic_hold_attack_interval_to": "設定週期性按住左鍵間隔為 %s", "tweakeroo.message.set_periodic_hold_use_interval_to": "設定週期性按住左鍵間隔為 %s", "tweakeroo.message.set_periodic_use_interval_to": "設定週期性右鍵間隔為 %s", "tweakeroo.message.set_placement_grid_size_to": "調整放置區域 %s", "tweakeroo.message.set_placement_limit_to": "方塊放置數量限制為 %s", "tweakeroo.message.set_placement_restriction_mode_to": "設定方塊放置模式為 %s", "tweakeroo.message.set_snap_aim_pitch_step_to": "已將朝向對齊工具的俯仰角度間距設定為 %s", "tweakeroo.message.set_snap_aim_yaw_step_to": "已將朝向對齊工具的偏航角度間距設定為 %s", "tweakeroo.message.set_zoom_fov_to": "設定視角廣度範圍為 %s", "tweakeroo.message.sign_text_copied": "已複製告示牌文字", "tweakeroo.message.snapped_to_pitch": "將俯仰角的朝向角度對齊為 %s", "tweakeroo.message.snapped_to_yaw": "將偏航角的朝向角度對齊為 %s", "tweakeroo.message.swapped_low_durability_item_for_better_durability": "已經將低耐久物品自動切換為高耐久物品", "tweakeroo.message.swapped_low_durability_item_for_dummy_item": "將低耐久物品自動切換為虛擬物品", "tweakeroo.message.swapped_low_durability_item_off_players_hand": "移除玩家手中的低耐久物品", "tweakeroo.message.toggled": "功能 %s %s", "tweakeroo.message.toggled_after_clicker_on": "切換連點器 %s，點擊頻率 %s", "tweakeroo.message.toggled_breaking_grid_on": "調整破壞網格區域 %s，區域範圍 %s", "tweakeroo.message.toggled_fast_placement_mode_on": "切換快速放置模式 %s，模式為 %s", "tweakeroo.message.toggled_fly_speed_on": "調整飛行速度為 %s，初始速度 %s，目前速度 %s", "tweakeroo.message.toggled_periodic": "功能 %s %s，速度：%s", "tweakeroo.message.toggled_placement_grid_on": "調整放置網格區域 %s，區域範圍 %s", "tweakeroo.message.toggled_placement_limit_on": "調整放置數量 %s，數量為 %s", "tweakeroo.message.toggled_slot_cycle_on": "切換快捷欄欄位循環為 %s，最大欄位為 %s", "tweakeroo.message.toggled_slot_randomizer_on": "切換快捷欄欄位隨機為 %s，最大欄位為 %s", "tweakeroo.message.toggled_snap_aim_on_both": "對齊偏航與俯仰角度 %s，偏航角度步進為 %s，俯仰角度步進為 %s", "tweakeroo.message.toggled_snap_aim_on_pitch": "對齊俯仰角度為 %s，角度步進為 %s", "tweakeroo.message.toggled_snap_aim_on_yaw": "對齊偏航角度為 %s，角度步進為 %s", "tweakeroo.message.toggled_zoom_activate_off": "望遠鏡已關閉，視角廣度範圍為 %s", "tweakeroo.message.toggled_zoom_activate_on": "望遠鏡已啟動，視角廣度範圍為 %s", "tweakeroo.message.toggled_zoom_on": "望遠鏡視角廣度已切換 %s，視角廣度範圍為：%s", "tweakeroo.message.unfocusing_game": "§6取消聚焦§f 遊戲（釋放游標）", "tweakeroo.message.value.off": "關閉", "tweakeroo.message.value.on": "開啟", "tweakeroo.message.warning.block_type_break_restriction": "§6方塊破壞被 [方塊破壞限制] 功能阻止§f", "tweakeroo.message.warning.entity_type_attack_restriction": "§6實體攻擊被 [實體攻擊限制] 功能阻止§f"}