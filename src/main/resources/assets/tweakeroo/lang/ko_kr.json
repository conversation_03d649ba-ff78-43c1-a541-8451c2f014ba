{"modmenu.descriptionTranslation.tweakeroo": "설정 가능한 다양한 클라이언트 측 트윅을 추가합니다.", "tweakeroo.config.disable.comment.disableArmorStandRendering": "모든 갑옷 거치대 엔티티 렌더링을 비활성화합니다.", "tweakeroo.config.disable.comment.disableAtmosphericFog": "Disables all Atmospheric Fog that exists\nbetween the player and the Render Distance Fog.", "tweakeroo.config.disable.comment.disableAxeStripping": "도끼로 원목 벗기기 기능을 비활성화합니다.", "tweakeroo.config.disable.comment.disableBatSpawning": "싱글 플레이어에서 박쥐 생성을 비활성화합니다.", "tweakeroo.config.disable.comment.disableBeaconBeamRendering": "신호기 빔 렌더링을 비활성화합니다.", "tweakeroo.config.disable.comment.disableBlockBreakCooldown": "블록 파괴 사이에 걸리는 블록 파괴\n쿨다운을 비활성화합니다.", "tweakeroo.config.disable.comment.disableBlockBreakingParticles": "블록 파괴 입자를 비활성화합니다.\n(이것은 nessie의 usefulmod에서 따온 것입니다.)", "tweakeroo.config.disable.comment.disableBossBar": "보스 바 렌더링을 비활성화합니다.", "tweakeroo.config.disable.comment.disableBossFog": "보스 몹이 발생시키는 안개를 제거합니다.", "tweakeroo.config.disable.comment.disableChunkRendering": "청크 (재)렌더링을 비활성화합니다. 이렇게 하면 F3 + A를 사용하여 월드 렌더링을\n새로고침 하거나 이 기능을 다시 비활성화하기 전까지는 블록 변화가 표시되지 않습니다.\n이는 블록 변화를 관측할 필요는 없는데 많은 블록 변화 때문에 fps가 낮은 경우에\n도움이 될 수 있습니다.", "tweakeroo.config.disable.comment.disableClientEntityUpdates": "클라이언트에서 플레이어를 제외한 모든 엔티티 업데이트를 비활성화합니다.\n이는 주로 과도한 엔티티 수 관련 문제를 해결하기 위해 작업을 수행해야\n하는 상황을 위한 것입니다.", "tweakeroo.config.disable.comment.disableClientLightUpdates": "모든 클라이언트 측 빛 업데이트를 비활성화합니다.", "tweakeroo.config.disable.comment.disableConstantChunkSaving": "일반적인 자동 저장 주기 외에 게임이 항상 매 틱마다\n최대 20개의 청크를 저장하는 기능을 비활성화합니다.", "tweakeroo.config.disable.comment.disableCreativeMenuInfestedBlocks": "크리에이티브 검색 메뉴에서 벌레 먹은 블록을 제거합니다.", "tweakeroo.config.disable.comment.disableDeadMobRendering": "죽은 몹(체력이 0인 엔티티) 렌더링을 방지합니다.", "tweakeroo.config.disable.comment.disableDeadMobTargeting": "체력이 0인 엔티티를 타겟팅하는 것을 방지합니다.\n이것은 이미 죽은 몹을 때리는 문제를 해결합니다.", "tweakeroo.config.disable.comment.disableDoubleTapSprint": "앞으로 전진 키 더블 탭 달리기를 비활성화합니다.", "tweakeroo.config.disable.comment.disableEntityRendering": "플레이어를 제외한 모든 엔티티 렌더링을 비활성화합니다.\n이는 주로 과도한 엔티티 수 관련 문제를 해결하기 위해 작업을 수행해야\n하는 상황을 위한 것입니다.", "tweakeroo.config.disable.comment.disableEntityTicking": "플레이어 엔티티를 제외한 모든 엔티티가 연산 되는 것을 방지합니다.", "tweakeroo.config.disable.comment.disableFallingBlockEntityRendering": "활성화된 경우, 낙하 블록 엔티티가 전혀 렌더링 되지 않습니다.", "tweakeroo.config.disable.comment.disableFirstPersonEffectParticles": "1인칭 시점에서 물약 효과의 입자/소용돌이를 제거합니다.\n(플레이어 자체에서)", "tweakeroo.config.disable.comment.disableInventoryEffectRendering": "보관함 GUI에서 물약 효과 렌더링을 제거합니다.", "tweakeroo.config.disable.comment.disableItemSwitchRenderCooldown": "true인 경우, 들고 있는 아이템을 바꾸거나 아이템을 사용할 때\n사용 쿨다운/장착 애니메이션이 발생하지 않습니다.", "tweakeroo.config.disable.comment.disableMobSpawnerMobRendering": "몹 스포너에서 엔티티 렌더링을 제거합니다.", "tweakeroo.config.disable.comment.disableNauseaEffect": "멀미 시각 효과를 비활성화합니다.", "tweakeroo.config.disable.comment.disableNetherFog": "네더의 안개를 제거합니다.", "tweakeroo.config.disable.comment.disableNetherPortalSound": "네더 포탈 소리를 비활성화합니다.", "tweakeroo.config.disable.comment.disableObserver": "관측기가 전혀 작동하지 않도록 비활성화합니다.", "tweakeroo.config.disable.comment.disableOffhandRendering": "보조 손의 아이템 렌더링을 비활성화합니다.", "tweakeroo.config.disable.comment.disableParticles": "모든 입자를 비활성화합니다.", "tweakeroo.config.disable.comment.disablePortalGuiClosing": "활성화된 경우, 네더 포탈 안에 있는 동안에도 GUI를 열 수 있습니다.", "tweakeroo.config.disable.comment.disableRainEffects": "비 렌더링과 소리를 비활성화합니다.", "tweakeroo.config.disable.comment.disableRenderDistanceFog": "렌더 거리 주변에서 증가하는 안개를 비활성화합니다.", "tweakeroo.config.disable.comment.disableRenderingScaffolding": "비계 블록 렌더링을 비활성화합니다.", "tweakeroo.config.disable.comment.disableScoreboardRendering": "사이드 바 점수판 렌더링을 제거합니다.", "tweakeroo.config.disable.comment.disableShovelPathing": "삽으로 잔디 등을 길 블록으로 변환하는 기능을 비활성화합니다.", "tweakeroo.config.disable.comment.disableShulkerBoxTooltip": "셜커 박스 내용물에 대한 바닐라 텍스트 툴팁을 비활성화합니다.", "tweakeroo.config.disable.comment.disableSignGui": "표지판 편집 GUI가 열리지 않도록 방지합니다.", "tweakeroo.config.disable.comment.disableSkyDarkness": "y = 63 이하의 하늘 어두움을 비활성화합니다.\n\n(y 임계값을 월드 바닥 2 블록 아래로 이동합니다.)", "tweakeroo.config.disable.comment.disableSlimeBlockSlowdown": "슬라임 블록 위를 걷는 속도 감속을 제거합니다.\n(이것은 nessie의 usefulmod에서 따온 것입니다.)", "tweakeroo.config.disable.comment.disableStatusEffectHud": "상태 효과 HUD 렌더링을 비활성화합니다.\n(일반적으로 화면 오른쪽 상단에 있음)", "tweakeroo.config.disable.comment.disableTileEntityRendering": "모든 타일 엔티티 렌더링을 방지합니다.", "tweakeroo.config.disable.comment.disableTileEntityTicking": "모든 타일 엔티티가 연산 되지 않도록 방지합니다.", "tweakeroo.config.disable.comment.disableVillagerTradeLocking": "주민과의 거래량이 증가하면 최대 사용량을 늘려서\n주민의 거래가 잠기는 것을 방지합니다.", "tweakeroo.config.disable.comment.disableWallUnsprint": "벽에 닿아도 달리기 모드가 해제되지 않습니다.", "tweakeroo.config.disable.comment.disableWorldViewBob": "월드의 뷰 밥 흔들림 효과를 비활성화하지만, 손은 비활성화하지 않습니다.\nIris가 설치되어 있으면 이 설정은 작동하지 않습니다.", "tweakeroo.config.disable.name.disableArmorStandRendering": "비활성화 갑옷 거치대 렌더링", "tweakeroo.config.disable.name.disableAtmosphericFog": "disableAtmosphericFog", "tweakeroo.config.disable.name.disableAxeStripping": "비활성화 도끼 벗기기", "tweakeroo.config.disable.name.disableBatSpawning": "비활성화 박쥐 스폰", "tweakeroo.config.disable.name.disableBeaconBeamRendering": "비활성화 신호기 빔 렌더링", "tweakeroo.config.disable.name.disableBlockBreakCooldown": "비활성화 블록 파괴 쿨다운", "tweakeroo.config.disable.name.disableBlockBreakingParticles": "비활성화 블록 파괴 입자", "tweakeroo.config.disable.name.disableBossBar": "비활성화 보스 바", "tweakeroo.config.disable.name.disableBossFog": "비활성화 보스 안개", "tweakeroo.config.disable.name.disableChunkRendering": "비활성화 청크 렌더링", "tweakeroo.config.disable.name.disableClientEntityUpdates": "비활성화 클라이언트 엔티티 업데이트", "tweakeroo.config.disable.name.disableClientLightUpdates": "비활성화 클라이언트 빛 업데이트", "tweakeroo.config.disable.name.disableConstantChunkSaving": "비활성화 지속 청크 저장", "tweakeroo.config.disable.name.disableCreativeMenuInfestedBlocks": "비활성화 크리에이티브 메뉴 벌레 먹은 블록", "tweakeroo.config.disable.name.disableDeadMobRendering": "비활성화 죽은 몹 렌더링", "tweakeroo.config.disable.name.disableDeadMobTargeting": "비활성화 죽은 몹 타겟팅", "tweakeroo.config.disable.name.disableDoubleTapSprint": "비활성화 더블 탭 달리기", "tweakeroo.config.disable.name.disableEntityRendering": "비활성화 엔티티 렌더링", "tweakeroo.config.disable.name.disableEntityTicking": "비활성화 엔티티 틱킹", "tweakeroo.config.disable.name.disableFallingBlockEntityRendering": "비활성화 낙하 블록 엔티티 렌더링", "tweakeroo.config.disable.name.disableFirstPersonEffectParticles": "비활성화 1인칭 효과 입자", "tweakeroo.config.disable.name.disableInventoryEffectRendering": "비활성화 보관함 효과 렌더링", "tweakeroo.config.disable.name.disableItemSwitchRenderCooldown": "비활성화 아이템 전환 렌더링 쿨다운", "tweakeroo.config.disable.name.disableMobSpawnerMobRendering": "비활성화 몹 스포너 몹 렌더링", "tweakeroo.config.disable.name.disableNauseaEffect": "비활성화 멀미 효과", "tweakeroo.config.disable.name.disableNetherFog": "비활성화 네더 안개", "tweakeroo.config.disable.name.disableNetherPortalSound": "비활성화 네더 포탈 소리", "tweakeroo.config.disable.name.disableObserver": "비활성화 관측기", "tweakeroo.config.disable.name.disableOffhandRendering": "비활성화 보조 손 렌더링", "tweakeroo.config.disable.name.disableParticles": "비활성화 입자", "tweakeroo.config.disable.name.disablePortalGuiClosing": "비활성화 포탈 GUI 닫기", "tweakeroo.config.disable.name.disableRainEffects": "비활성화 비 효과", "tweakeroo.config.disable.name.disableRenderDistanceFog": "비활성화 렌더 거리 안개", "tweakeroo.config.disable.name.disableRenderingScaffolding": "비활성화 렌더링 비계", "tweakeroo.config.disable.name.disableScoreboardRendering": "비활성화 점수판 렌더링", "tweakeroo.config.disable.name.disableShovelPathing": "비활성화 삽 길 만들기", "tweakeroo.config.disable.name.disableShulkerBoxTooltip": "비활성화 셜커 박스 툴팁", "tweakeroo.config.disable.name.disableSignGui": "비활성화 표지판 GUI", "tweakeroo.config.disable.name.disableSkyDarkness": "비활성화 하늘 어두움", "tweakeroo.config.disable.name.disableSlimeBlockSlowdown": "비활성화 슬라임 블록 감속", "tweakeroo.config.disable.name.disableStatusEffectHud": "비활성화 상태 효과 HUD", "tweakeroo.config.disable.name.disableTileEntityRendering": "비활성화 타일 엔티티 렌더링", "tweakeroo.config.disable.name.disableTileEntityTicking": "비활성화 타일 엔티티 틱킹", "tweakeroo.config.disable.name.disableVillagerTradeLocking": "비활성화 주민 거래 잠금", "tweakeroo.config.disable.name.disableWallUnsprint": "비활성화 벽 달리기 해제", "tweakeroo.config.disable.name.disableWorldViewBob": "비활성화 월드 뷰 밥", "tweakeroo.config.disable.prettyName.disableArmorStandRendering": "비활성화 갑옷 거치대 렌더링", "tweakeroo.config.disable.prettyName.disableAtmosphericFog": "Disable Atmospheric Fog", "tweakeroo.config.disable.prettyName.disableAxeStripping": "비활성화 도끼 벗기기", "tweakeroo.config.disable.prettyName.disableBatSpawning": "비활성화 박쥐 스폰", "tweakeroo.config.disable.prettyName.disableBeaconBeamRendering": "비활성화 신호기 빔 렌더링", "tweakeroo.config.disable.prettyName.disableBlockBreakCooldown": "비활성화 블록 파괴 쿨다운", "tweakeroo.config.disable.prettyName.disableBlockBreakingParticles": "비활성화 블록 파괴 입자", "tweakeroo.config.disable.prettyName.disableBossBar": "비활성화 보스 바", "tweakeroo.config.disable.prettyName.disableBossFog": "비활성화 보스 안개", "tweakeroo.config.disable.prettyName.disableChunkRendering": "비활성화 청크 렌더링", "tweakeroo.config.disable.prettyName.disableClientEntityUpdates": "비활성화 클라이언트 엔티티 업데이트", "tweakeroo.config.disable.prettyName.disableClientLightUpdates": "비활성화 클라이언트 빛 업데이트", "tweakeroo.config.disable.prettyName.disableConstantChunkSaving": "비활성화 지속 청크 저장", "tweakeroo.config.disable.prettyName.disableCreativeMenuInfestedBlocks": "비활성화 크리에이티브 메뉴 벌레 먹은 블록", "tweakeroo.config.disable.prettyName.disableDeadMobRendering": "비활성화 죽은 몹 렌더링", "tweakeroo.config.disable.prettyName.disableDeadMobTargeting": "비활성화 죽은 몹 타겟팅", "tweakeroo.config.disable.prettyName.disableDoubleTapSprint": "비활성화 더블 탭 달리기", "tweakeroo.config.disable.prettyName.disableEntityRendering": "비활성화 엔티티 렌더링", "tweakeroo.config.disable.prettyName.disableEntityTicking": "비활성화 엔티티 틱킹", "tweakeroo.config.disable.prettyName.disableFallingBlockEntityRendering": "비활성화 낙하 블록 엔티티 렌더링", "tweakeroo.config.disable.prettyName.disableFirstPersonEffectParticles": "비활성화 1인칭 효과 입자", "tweakeroo.config.disable.prettyName.disableInventoryEffectRendering": "비활성화 보관함 효과 렌더링", "tweakeroo.config.disable.prettyName.disableItemSwitchRenderCooldown": "비활성화 아이템 전환 렌더링 쿨다운", "tweakeroo.config.disable.prettyName.disableMobSpawnerMobRendering": "비활성화 몹 스포너 몹 렌더링", "tweakeroo.config.disable.prettyName.disableNauseaEffect": "비활성화 멀미 효과", "tweakeroo.config.disable.prettyName.disableNetherFog": "비활성화 네더 안개", "tweakeroo.config.disable.prettyName.disableNetherPortalSound": "비활성화 네더 포탈 소리", "tweakeroo.config.disable.prettyName.disableObserver": "비활성화 관측기", "tweakeroo.config.disable.prettyName.disableOffhandRendering": "비활성화 보조 손 렌더링", "tweakeroo.config.disable.prettyName.disableParticles": "비활성화 입자", "tweakeroo.config.disable.prettyName.disablePortalGuiClosing": "비활성화 포탈 GUI 닫기", "tweakeroo.config.disable.prettyName.disableRainEffects": "비활성화 비 효과", "tweakeroo.config.disable.prettyName.disableRenderDistanceFog": "비활성화 렌더 거리 안개", "tweakeroo.config.disable.prettyName.disableRenderingScaffolding": "비활성화 렌더링 비계", "tweakeroo.config.disable.prettyName.disableScoreboardRendering": "비활성화 점수판 렌더링", "tweakeroo.config.disable.prettyName.disableShovelPathing": "비활성화 삽 길 만들기", "tweakeroo.config.disable.prettyName.disableShulkerBoxTooltip": "비활성화 셜커 박스 툴팁", "tweakeroo.config.disable.prettyName.disableSignGui": "비활성화 표지판 GUI", "tweakeroo.config.disable.prettyName.disableSkyDarkness": "비활성화 하늘 어두움", "tweakeroo.config.disable.prettyName.disableSlimeBlockSlowdown": "비활성화 슬라임 블록 감속", "tweakeroo.config.disable.prettyName.disableStatusEffectHud": "비활성화 상태 효과 HUD", "tweakeroo.config.disable.prettyName.disableTileEntityRendering": "비활성화 타일 엔티티 렌더링", "tweakeroo.config.disable.prettyName.disableTileEntityTicking": "비활성화 타일 엔티티 틱킹", "tweakeroo.config.disable.prettyName.disableVillagerTradeLocking": "비활성화 주민 거래 잠금", "tweakeroo.config.disable.prettyName.disableWallUnsprint": "비활성화 벽 달리기 해제", "tweakeroo.config.disable.prettyName.disableWorldViewBob": "비활성화 월드 뷰 밥", "tweakeroo.config.feature_toggle.comment.tweakAccurateBlockPlacement": "카펫 모드와 비슷하지만 더 간단한 버전의 유연한 설치를\n활성화합니다. 기본적으로 클릭한 블록 면의 안쪽이나\n바깥쪽을 향하게 됩니다.", "tweakeroo.config.feature_toggle.comment.tweakAfterClicker": "방금 설치한 블록에 자동으로 오른쪽 클릭을\n하는 \"클리커 이후\" 트윅을 활성화합니다.\n중계기(지연 조정) 등에 유용합니다.\n값을 빠르게 조정하려면 트윅 토글\n키지정을 누른 채로 스크롤합니다.", "tweakeroo.config.feature_toggle.comment.tweakAimLock": "조준 잠금을 활성화하여 요 및 피치 회전을\n현재 값으로 고정합니다.\n이것은 스냅 된 값으로 고정하는 스냅 조준 잠금과는 별개입니다.\n이를 통해 현재 값으로 \"자유롭게\" 잠글 수 있습니다.", "tweakeroo.config.feature_toggle.comment.tweakAngelBlock": "크리에이티브 모드에서 공중에 블록을 설치할\n수 있는 \"천사 블록\" 트윅을 활성화합니다.\n\"Flotato\" 기술로 구동됩니다.", "tweakeroo.config.feature_toggle.comment.tweakAreaSelector": "영역 선택기를 활성화합니다.\nAndrew54757가 TweakFork에서 작성했습니다.", "tweakeroo.config.feature_toggle.comment.tweakAutoSwitchElytra": "추락 시 자동으로 겉날개로 전환하고,\n착지 시에는 이전의 흉갑 장비로 전환합니다.", "tweakeroo.config.feature_toggle.comment.tweakBlockReachOverride": "일반 -> 블록 도달 거리에서 설정된 값으로\n블록 도달 거리로 오버라이드합니다.", "tweakeroo.config.feature_toggle.comment.tweakBlockTypeBreakRestriction": "(직접) 파괴할 수 있는 블록을 제한합니다.\n목록 카테고리에서 해당 '블록 파괴 제한*' 설정을 참조하세요.", "tweakeroo.config.feature_toggle.comment.tweakBreakingGrid": "활성화하면 조정 가능한 간격으로 격자\n패턴으로만 블록을 파괴할 수 있게 됩니다.\n간격을 빠르게 조정하려면 트윅 토글\n키지정을 누른 채로 스크롤합니다.", "tweakeroo.config.feature_toggle.comment.tweakBreakingRestriction": "파괴 제한 모드를 활성화합니다.\n(평면, 레이어, 면, 열, 라인, 대각)\n기본적으로 공격 키를 누르고 있는 동안\n해당 패턴의 블록들만 파괴할 수 있습니다.", "tweakeroo.config.feature_toggle.comment.tweakBundleDisplay": "꾸러미 위에 마우스를 올려놓은 상태에서 Shift 키를 누르면\n꾸러미의 내용물을 미리보기 할 수 있습니다.", "tweakeroo.config.feature_toggle.comment.tweakChatBackgroundColor": "기본 채팅 배경색을 일반 -> '채팅 배경 색상'의\n색상으로 오버라이드합니다.", "tweakeroo.config.feature_toggle.comment.tweakChatPersistentText": "채팅 입력 텍스트 필드의 텍스트를 저장하고\n채팅이 다시 열릴 때 이를 복원합니다.", "tweakeroo.config.feature_toggle.comment.tweakChatTimestamp": "채팅 메시지에 타임 스탬프를 추가합니다.", "tweakeroo.config.feature_toggle.comment.tweakCommandBlockExtraFields": "명령 블록 GUI에 추가 필드를 추가하여 명령 블록의\n이름을 설정하고 통계 결과를 확인합니다.", "tweakeroo.config.feature_toggle.comment.tweakCreativeExtraItems": "아이템 그룹에 사용자 지정 아이템을 추가합니다.\n목록에 추가되는 아이템을 수정하려면 목록 -> '크리에이티브 추가 아이템'을 참조하세요.\n참고: 현재 추가 아이템들은 운송 카테고리에 추가되지만 (아이템이 가장 적기 때문)\n미래에는 추가된 아이템별로 그룹을 설정할 수 있을 것입니다.", "tweakeroo.config.feature_toggle.comment.tweakCustomFlatPresets": "사용자 지정 평지 월드 프리셋을 목록에 추가할 수 있습니다.\n목록 -> 평지 월드 프리셋에서 정의됩니다.", "tweakeroo.config.feature_toggle.comment.tweakCustomFlyDeceleration": "크리에이티브 또는 관람자 모드에서 비행 감속을 변경할 수 있습니다.\n이는 주로 더 빠른 감속, 즉 이동 키를 놓을 때\n덜 \"미끄러지기\" 위한 것입니다.\n일반 -> 비행 감속 계수 값을 참조하세요.", "tweakeroo.config.feature_toggle.comment.tweakCustomInventoryScreenScale": "모든 보관함 화면에 사용자 지정 GUI 크기를 사용할 수 있습니다.\n크기 값은 일반 -> §e사용자 지정 보관함 GUI 크기§r를 참조하세요.", "tweakeroo.config.feature_toggle.comment.tweakDarknessVisibility": "If enabled, this will increase visibility\nwhile effected by the Darkness Status Effect.", "tweakeroo.config.feature_toggle.comment.tweakElytraCamera": "'겉날개 카메라' 활성화 키를 누르고 있는 동안 실제 플레이어의 회전을 고정할 수 있습니다.\n그러면 컨트롤은 렌더링/카메라에 대한 별도의 '카메라 회전'에만 영향을 미칩니다.\n겉날개로 직선 비행을 할 때 아래/주변을 자유롭게 바라보기 위한 것입니다.", "tweakeroo.config.feature_toggle.comment.tweakEmptyShulkerBoxesStack": "비어 있는 셜커 박스를 64개까지 스택 할 수 있게 합니다.\n참고: 보관함 안에도 스택됩니다!\n서버에서는 동일한 기능을 수행하는 모드가\n없는 한 비동기화/글리치가 발생합니다.\n싱글 플레이어에서는 셜커 박스 기반 시스템 작동이 변경됩니다.", "tweakeroo.config.feature_toggle.comment.tweakEntityReachOverride": "일반 -> 엔티티 도달 거리에 설정된 값으로\n엔티티 도달 거리를 오버라이드합니다.", "tweakeroo.config.feature_toggle.comment.tweakEntityTypeAttackRestriction": "(직접) 공격할 수 있는 엔티티를 제한합니다.\n목록 카테고리에서 해당 '엔티티 공격 제한*' 설정을 참조하세요.", "tweakeroo.config.feature_toggle.comment.tweakExplosionReducedParticles": "활성화된 경우, 모든 폭발 입자는 EXPLOSION_LARGE\n또는 EXPLOSION_HUGE 입자 대신 EXPLOSION_NORMAL\n입자를 사용합니다.", "tweakeroo.config.feature_toggle.comment.tweakF3Cursor": "F3 화면 커서를 항상 렌더링하도록 설정합니다.", "tweakeroo.config.feature_toggle.comment.tweakFakeSneakPlacement": "이 트윅은 클릭 위치를 실제로 클릭한 블록에서\n인접한 공기 블록으로 오프셋합니다.\n기본적으로 이를 통해 웅크리지 않고도 보관함 GUI를 여는\n것과 같은 클릭 동작이 있는 블록에 블록을 설치할 수 있습니다.\n참고로 이것은 실제로 가상 웅크리기를 하는 것이 아니고\n그저 겉보기에 비슷한 효과일 뿐입니다.", "tweakeroo.config.feature_toggle.comment.tweakFakeSneaking": "\"가상 웅크리기\"를 활성화합니다. 이동 속도를 늦추지\n않고 가장자리에서 떨어지는 것을 방지합니다.", "tweakeroo.config.feature_toggle.comment.tweakFastBlockPlacement": "새로운 블록 위로 커서를 이동할 때 빠르고 편리한 블록 설치를 활성화합니다.", "tweakeroo.config.feature_toggle.comment.tweakFastLeftClick": "공격 버튼(왼쪽 클릭)을 누르고 있는 동안\n자동 빠른 왼쪽 클릭을 활성화합니다.\n틱당 클릭 수는 일반 설정에서 입력됩니다.", "tweakeroo.config.feature_toggle.comment.tweakFastRightClick": "사용 버튼(오른쪽 클릭)을 누르고 있는 동안\n자동 빠른 오른쪽 클릭을 활성화합니다.\n틱당 클릭 수는 일반 설정에서 입력됩니다.", "tweakeroo.config.feature_toggle.comment.tweakFillCloneLimit": "싱글 플레이어에서 /fill 및 /clone 명령의 블록\n한계를 오버라이드 할 수 있습니다.\n새로운 한계 값은 일반 설정의 'fill clone 한계'\n설정 값에서 입력됩니다.", "tweakeroo.config.feature_toggle.comment.tweakFlexibleBlockPlacement": "모드에 해당하는 단축키를 누르고 있는 동안\n블록의 방향을 바꾸거나 오프셋하여 설치할\n수 있게 활성화합니다.", "tweakeroo.config.feature_toggle.comment.tweakFlySpeed": "크리에이티브나 관전자 모드에서 비행 속도를 오버라이드하고\n이를 위한 몇 가지 프리셋을 사용합니다.", "tweakeroo.config.feature_toggle.comment.tweakFreeCamera": "관전자 모드와 유사한 자유 카메라 모드를 활성화합니다.\n플레이어는 자유 카메라 모드를 처음 활성화한 위치에\n머물러 있게 됩니다.", "tweakeroo.config.feature_toggle.comment.tweakGammaOverride": "비디오 설정 감마\n값을 일반 설정에 입력된 값으로 오버라이드합니다.", "tweakeroo.config.feature_toggle.comment.tweakHandRestock": "사용 중인 아이템의 스택이 소진되면 새로운 스택을 주 또는\n보조 손으로 스왑하는 기능을 활성화합니다.", "tweakeroo.config.feature_toggle.comment.tweakHangableEntityBypass": "걸 수 있는 엔티티(아이템 액자 및 그림)를 타겟팅 하지 않도록 합니다.\n일반 -> 걸 수 있는 엔티티 바이패스 역전 옵션을 사용하면 엔티티를\n타겟팅 하기 위해 웅크리기를 해야 하는지 아닌지 결정할 수 있습니다.", "tweakeroo.config.feature_toggle.comment.tweakHoldAttack": "공격 버튼을 누르고 있습니다.", "tweakeroo.config.feature_toggle.comment.tweakHoldUse": "사용 버튼을 누르고 있습니다.", "tweakeroo.config.feature_toggle.comment.tweakHotbarScroll": "스크롤로 단축바를 스왑하는 기능을 활성화합니다.", "tweakeroo.config.feature_toggle.comment.tweakHotbarSlotCycle": "블록 설치 후 설정한 최대 슬롯 번호\n내에서 단축바 선택을 순환합니다.\n최대 슬롯 번호를 빠르게 조정하려면\n트윅 토글 키지정을 누른 채로 스크롤합니다.", "tweakeroo.config.feature_toggle.comment.tweakHotbarSlotRandomizer": "블록 설치 후 설정한 최대 슬롯 번호\n내에서 단축바 선택을 무작위로 전환합니다.\n최대 슬롯 번호를 빠르게 조정하려면\n트윅 토글 키지정을 누른 채로 스크롤합니다.", "tweakeroo.config.feature_toggle.comment.tweakHotbarSwap": "단축키를 이용한 단축바 스왑을 활성화합니다.", "tweakeroo.config.feature_toggle.comment.tweakInventoryPreview": "보관함이 있는 블록이나 엔티티 위에 조준점을 두고 설정한\n단축키를 누르면 보관함을 미리보기 할 수 있습니다.\n§61.21+ MiniHUD 버전으로 인해 더 이상 사용되지 않습니다.", "tweakeroo.config.feature_toggle.comment.tweakItemUnstackingProtection": "활성화된 경우, 목록 -> 아이템 언스택에서 설정한\n아이템이 사용 시 쏟아지지 않습니다.\n이는 예를 들어 용암을 채울 때 양동이를 용암에\n던지는 것을 방지할 수 있습니다.", "tweakeroo.config.feature_toggle.comment.tweakLavaVisibility": "활성화된 경우, 호흡과 친수성 인챈트 그리고\n화염 저항 효과 활성화 시 용암 아래에서 보이는\n가시성이 대폭 증가합니다.", "tweakeroo.config.feature_toggle.comment.tweakMapPreview": "활성화된 경우, 보관함에서 지도 위에 Shift 키를\n누르고 있으면 지도 미리보기가 렌더링됩니다.", "tweakeroo.config.feature_toggle.comment.tweakMovementKeysLast": "활성화된 경우, 반대편 이동 키가 서로 취소되지 않고\n대신 마지막으로 누른 키가 활성 입력이 됩니다.", "tweakeroo.config.feature_toggle.comment.tweakPeriodicAttack": "반복 공격(왼쪽 클릭)을 활성화합니다.\n일반 -> 반복 공격 간격에서 간격을 설정합니다.", "tweakeroo.config.feature_toggle.comment.tweakPeriodicHoldAttack": "설정한 시간 만큼 공격 누르기 반복을 활성화합니다.\n일반 -> 반복 공격 누르기 간격에서 간격을 설정하고,\n간격 반복 공격 누르기 시간에서 시간을 설정합니다.\n§6참고: 공격 누르기 또는 반복 공격과 동시에 사용해서는 안 됩니다.", "tweakeroo.config.feature_toggle.comment.tweakPeriodicHoldUse": "설정한 시간 만큼 사용 누르기 반복을 활성화합니다.\n일반 -> 반복 사용 누르기 간격에서 간격을 설정하고,\n간격 반복 사용 누르기 시간에서 시간을 설정합니다.\n§6참고: 사용 누르기 또는 반복 사용과 동시에 사용해서는 안 됩니다.", "tweakeroo.config.feature_toggle.comment.tweakPeriodicUse": "반복 사용(오른쪽 클릭)을 활성화합니다.\n일반 -> 반복 사용 간격에서 간격을 설정합니다.", "tweakeroo.config.feature_toggle.comment.tweakPermanentSneak": "활성화된 경우, 플레이어는 항상 웅크리기 상태가 됩니다.", "tweakeroo.config.feature_toggle.comment.tweakPermanentSprint": "활성화된 경우, 플레이어는 앞으로 이동할 때 항상 달리기 상태가 됩니다.", "tweakeroo.config.feature_toggle.comment.tweakPickBeforePlace": "활성화된 경우, 각 블록 설치 전에 바라보고 있는 블록과\n같은 블록이 손으로 전환됩니다.", "tweakeroo.config.feature_toggle.comment.tweakPlacementGrid": "활성화된 경우, 설정한 간격으로 격자\n패턴으로만 블록을 설치할 수 있습니다.\n값을 빠르게 조정하려면 트윅 토글\n키지정을 누른 채로 스크롤합니다.", "tweakeroo.config.feature_toggle.comment.tweakPlacementLimit": "활성화된 경우, 사용/오른쪽 클릭당\n정해진 수의 블록만 설치할 수 있습니다.\n값을 빠르게 조정하려면 트윅 토글\n키지정을 누른 채로 스크롤합니다.", "tweakeroo.config.feature_toggle.comment.tweakPlacementRestriction": "설치 제한 모드를 활성화합니다.\n  (평면, 레이어, 면, 열, 라인, 대각)", "tweakeroo.config.feature_toggle.comment.tweakPlacementRestrictionFirst": "블록 설치를 제한하여 처음 클릭한 것과\n동일한 블록 유형에 대해서만 블록을\n설치할 수 있도록 합니다.", "tweakeroo.config.feature_toggle.comment.tweakPlacementRestrictionHand": "블록 설치를 제한하여 손에 들고 있는 것과\n동일한 블록 유형에 대해서만 블록을\n설치할 수 있도록 합니다.", "tweakeroo.config.feature_toggle.comment.tweakPlayerInventoryPeek": "설정한 단축키를 누르고 있는 동안 플레이어\n보관함을 미리보기 할 수 있습니다.", "tweakeroo.config.feature_toggle.comment.tweakPlayerListAlwaysVisible": "활성화된 경우, 플레이어 목록 키(기본적으로 Tab키)를\n누르지 않고도 플레이어 목록이 항상 렌더링됩니다.", "tweakeroo.config.feature_toggle.comment.tweakPotionWarning": "신호기 효과를 제외한 물약 효과가 소멸하려고\n하면 단축바에 경고 메시지를 표시합니다.", "tweakeroo.config.feature_toggle.comment.tweakPrintDeathCoordinates": "사망 시 채팅에 플레이어의 사망 좌표를 표기합니다.\n이 기능은 nessie의 usefulmod에서 따온 것입니다.", "tweakeroo.config.feature_toggle.comment.tweakRenderEdgeChunks": "클라이언트에서 로드된 제일 가장자리에 있는 청크가 렌더링\n되도록 허용합니다. 바닐라 설정에서는 인접하지 않은 청크가\n렌더링 되는 것을 허용하지 않으므로 클라이언트에서 로드된\n제일 가장자리에 있는 청크는 렌더링 되지 않습니다.\n§l이는 자유 카메라 모드에서도 매우 유용합니다!§r", "tweakeroo.config.feature_toggle.comment.tweakRenderInvisibleEntities": "활성화된 경우, 투명 엔티티가 관전자 모드에 있는\n것처럼 렌더링됩니다.", "tweakeroo.config.feature_toggle.comment.tweakRenderLimitEntities": "프레임당 렌더링 할 특정 유형의 엔티티 수를 제한합니다.\n현재 경험치 구슬과 아이템 엔티티가 지원되며 제한 항목은\n일반 설정을 참조하세요.", "tweakeroo.config.feature_toggle.comment.tweakRepairMode": "활성화된 경우, 손에 들고 있는 아이템이 완전히 수리되면\n수선 인챈트가 되어 있는 손상된 아이템으로 스왑됩니다.", "tweakeroo.config.feature_toggle.comment.tweakSculkPulseLength": "스컬크 센서의 펄스 길이를 수정합니다.\n일반 -> 스컬크 센서 펄스 길이에서 설정합니다.", "tweakeroo.config.feature_toggle.comment.tweakSelectiveBlocksRenderOutline": "리스트 된 블록들의 외곽선을 렌더링합니다.\nAndrew54757가 TweakFork에서 작성했습니다.", "tweakeroo.config.feature_toggle.comment.tweakSelectiveBlocksRendering": "선택적으로 보이는 블록들을 렌더링합니다.\nAndrew54757가 TweakFork에서 작성했습니다.", "tweakeroo.config.feature_toggle.comment.tweakServerDataSync": "Servux를 사용하는 서버에서 서버 데이터 동기화를 사용하면\n셜커 박스와 같은 엔티티의 보관함 미리보기를 사용할 수 있습니다.\n§6이 옵션이 true로 설정되어 있어도 동기화가 작동하려면 서버\n§6운영자이거나 서버 측 동기화 모드가 설치되어 있어야 합니다.", "tweakeroo.config.feature_toggle.comment.tweakServerDataSyncBackup": "Servux를 사용할 수 없는 경우 Vanilla NbtQueryRequest\n패킷을 사용하도록 서버 데이터 동기화를 설정할 수 있습니다.", "tweakeroo.config.feature_toggle.comment.tweakShulkerBoxDisplay": "보관함에서 셜커 박스 위에 마우스 커서를 올리고\nShift 키를 누르면 내용물이 표시됩니다.", "tweakeroo.config.feature_toggle.comment.tweakSignCopy": "활성화된 경우, 설치되는 표지판은 이전에\n설치한 표지판의 텍스트를 그대로 사용합니다.\n비활성화 표지판 GUI 트윅과 결합하여 첫 번째\n표지판을 만든 후 해당 트윅을 활성화하여\n표지판의 사본을 빠르게 설치할 수 있습니다.", "tweakeroo.config.feature_toggle.comment.tweakSnapAim": "스냅 조준 트윅을 활성화하여 플레이어가 사전에\n설정한 정확한 요 방향을 향하도록 합니다.", "tweakeroo.config.feature_toggle.comment.tweakSnapAimLock": "스냅 조준 잠금을 활성화하여 현재 스냅 된 값으로\n요 및/또는 피치를 고정합니다.", "tweakeroo.config.feature_toggle.comment.tweakSneak_1_15_2": "버전 1.15.2의 웅크리기 동작을 복원합니다.", "tweakeroo.config.feature_toggle.comment.tweakSpectatorTeleport": "관전자 모드에서 다른 관전자에게 순간이동할 수 있게 해줍니다.\n이것은 nessie의 usefulmod에서 따온 것입니다.", "tweakeroo.config.feature_toggle.comment.tweakStructureBlockLimit": "구조물 블록의 한계를 오버라이드 할 수 있습니다.\n새로운 한계는 일반 -> 구조물 블록 한계에서 설정됩니다.", "tweakeroo.config.feature_toggle.comment.tweakSwapAlmostBrokenTools": "활성화된 경우, 손에 든 파괴 가능 아이템 중 파괴 직전인\n아이템을 새 아이템으로 스왑합니다.", "tweakeroo.config.feature_toggle.comment.tweakTabCompleteCoordinate": "활성화된 경우, 블록을 보지 않은 상태에서 명령어에\n좌표를 탭으로 완성하면 ~ 문자를 추가하는 대신\n플레이어의 현재 위치가 사용됩니다.", "tweakeroo.config.feature_toggle.comment.tweakToolSwitch": "목표 블록에 대해 효과적인 도구로 자동 전환됩니다.", "tweakeroo.config.feature_toggle.comment.tweakWaterVisibility": "If enabled, then the level of Respiration and Aqua Affinity enchantments,\nwill greatly increase the visibility under water.", "tweakeroo.config.feature_toggle.comment.tweakWeaponSwitch": "목표 엔티티에 대해 무기로 자동 전환됩니다.", "tweakeroo.config.feature_toggle.comment.tweakYMirror": "블록 경계 내에서 대상의 y 위치를 대칭합니다.\n이는 기본적으로 반블록이나 계단을 정상과 반대되는\n상단/하단 상태로 설치하기 위한 것입니다.\n예를 들어 다른 반블록에 설치해야 하는 경우에 유용합니다.", "tweakeroo.config.feature_toggle.comment.tweakZoom": "확대 단축키를 사용하여 확대합니다.", "tweakeroo.config.feature_toggle.name.tweakAccurateBlockPlacement": "트윅 정확한 블록 설치", "tweakeroo.config.feature_toggle.name.tweakAfterClicker": "트윅 클리커 이후", "tweakeroo.config.feature_toggle.name.tweakAimLock": "트윅 조준 잠금", "tweakeroo.config.feature_toggle.name.tweakAngelBlock": "트윅 천사 블록", "tweakeroo.config.feature_toggle.name.tweakAreaSelector": "트윅 영역 선택기", "tweakeroo.config.feature_toggle.name.tweakAutoSwitchElytra": "트윅 자동 전환 겉날개", "tweakeroo.config.feature_toggle.name.tweakBlockReachOverride": "§6트윅 블록 도달 오버라이드§r", "tweakeroo.config.feature_toggle.name.tweakBlockTypeBreakRestriction": "트윅 블록 타입 파괴 제한", "tweakeroo.config.feature_toggle.name.tweakBreakingGrid": "트윅 파괴 격자", "tweakeroo.config.feature_toggle.name.tweakBreakingRestriction": "트윅 파괴 제한", "tweakeroo.config.feature_toggle.name.tweakBundleDisplay": "트윅 꾸러미 표시", "tweakeroo.config.feature_toggle.name.tweakChatBackgroundColor": "트윅 채팅 배경 색상", "tweakeroo.config.feature_toggle.name.tweakChatPersistentText": "트윅 채팅 텍스트 유지", "tweakeroo.config.feature_toggle.name.tweakChatTimestamp": "트윅 채팅 타임 스탬프", "tweakeroo.config.feature_toggle.name.tweakCommandBlockExtraFields": "트윅 명령 블록 추가 필드", "tweakeroo.config.feature_toggle.name.tweakCreativeExtraItems": "트윅 크리에이티브 추가 아이템", "tweakeroo.config.feature_toggle.name.tweakCustomFlatPresets": "트윅 사용자 지정 평지 프리셋", "tweakeroo.config.feature_toggle.name.tweakCustomFlyDeceleration": "트윅 사용자 지정 비행 감속", "tweakeroo.config.feature_toggle.name.tweakCustomInventoryScreenScale": "트윅 사용자 지정 보관함 화면 크기", "tweakeroo.config.feature_toggle.name.tweakDarknessVisibility": "tweakDarknessVisibility", "tweakeroo.config.feature_toggle.name.tweakElytraCamera": "트윅 겉날개 카메라", "tweakeroo.config.feature_toggle.name.tweakEmptyShulkerBoxesStack": "§6트윅 빈 셜커 박스 스택§r", "tweakeroo.config.feature_toggle.name.tweakEntityReachOverride": "트윅 엔티티 도달 오버라이드", "tweakeroo.config.feature_toggle.name.tweakEntityTypeAttackRestriction": "트윅 엔티티 타입 공격 제한", "tweakeroo.config.feature_toggle.name.tweakExplosionReducedParticles": "트윅 폭발 입자 감소", "tweakeroo.config.feature_toggle.name.tweakF3Cursor": "트윅 F3 커서", "tweakeroo.config.feature_toggle.name.tweakFakeSneakPlacement": "트윅 가상 웅크리기 설치", "tweakeroo.config.feature_toggle.name.tweakFakeSneaking": "트윅 가상 웅크리기", "tweakeroo.config.feature_toggle.name.tweakFastBlockPlacement": "트윅 빠른 블록 설치", "tweakeroo.config.feature_toggle.name.tweakFastLeftClick": "트윅 빠른 왼쪽 클릭", "tweakeroo.config.feature_toggle.name.tweakFastRightClick": "트윅 빠른 오른쪽 클릭", "tweakeroo.config.feature_toggle.name.tweakFillCloneLimit": "트윅 fill clone 한계", "tweakeroo.config.feature_toggle.name.tweakFlexibleBlockPlacement": "트윅 유연한 블록 설치", "tweakeroo.config.feature_toggle.name.tweakFlySpeed": "트윅 비행 속도", "tweakeroo.config.feature_toggle.name.tweakFreeCamera": "트윅 자유 카메라", "tweakeroo.config.feature_toggle.name.tweakGammaOverride": "트윅 감마 오버라이드", "tweakeroo.config.feature_toggle.name.tweakHandRestock": "트윅 손 보충", "tweakeroo.config.feature_toggle.name.tweakHangableEntityBypass": "트윅 걸 수 있는 엔티티 바이패스", "tweakeroo.config.feature_toggle.name.tweakHoldAttack": "트윅 공격 누르기", "tweakeroo.config.feature_toggle.name.tweakHoldUse": "트윅 사용 누르기", "tweakeroo.config.feature_toggle.name.tweakHotbarScroll": "트윅 단축바 스크롤", "tweakeroo.config.feature_toggle.name.tweakHotbarSlotCycle": "트윅 단축바 슬롯 사이클", "tweakeroo.config.feature_toggle.name.tweakHotbarSlotRandomizer": "트윅 단축바 슬롯 랜덤화", "tweakeroo.config.feature_toggle.name.tweakHotbarSwap": "트윅 단축바 스왑", "tweakeroo.config.feature_toggle.name.tweakInventoryPreview": "§6트윅 보관함 미리보기§r", "tweakeroo.config.feature_toggle.name.tweakItemUnstackingProtection": "트윅 아이템 언스택 보호", "tweakeroo.config.feature_toggle.name.tweakLavaVisibility": "트윅 용암 가시성", "tweakeroo.config.feature_toggle.name.tweakMapPreview": "트윅 지도 미리보기", "tweakeroo.config.feature_toggle.name.tweakMovementKeysLast": "트윅 마지막 이동 키", "tweakeroo.config.feature_toggle.name.tweakPeriodicAttack": "트윅 반복 공격", "tweakeroo.config.feature_toggle.name.tweakPeriodicHoldAttack": "트윅 반복 공격 누르기", "tweakeroo.config.feature_toggle.name.tweakPeriodicHoldUse": "트윅 반복 사용 누르기", "tweakeroo.config.feature_toggle.name.tweakPeriodicUse": "트윅 반복 사용", "tweakeroo.config.feature_toggle.name.tweakPermanentSneak": "트윅 계속 웅크리기", "tweakeroo.config.feature_toggle.name.tweakPermanentSprint": "트윅 계속 달리기", "tweakeroo.config.feature_toggle.name.tweakPickBeforePlace": "트윅 설치 전에 선택", "tweakeroo.config.feature_toggle.name.tweakPlacementGrid": "트윅 설치 격자", "tweakeroo.config.feature_toggle.name.tweakPlacementLimit": "트윅 설치 한계", "tweakeroo.config.feature_toggle.name.tweakPlacementRestriction": "트윅 설치 제한", "tweakeroo.config.feature_toggle.name.tweakPlacementRestrictionFirst": "트윅 설치 제한 첫 번째", "tweakeroo.config.feature_toggle.name.tweakPlacementRestrictionHand": "트윅 설치 제한 손", "tweakeroo.config.feature_toggle.name.tweakPlayerInventoryPeek": "트윅 플레이어 보관함 미리보기", "tweakeroo.config.feature_toggle.name.tweakPlayerListAlwaysVisible": "트윅 플레이어 목록 언제나 표시", "tweakeroo.config.feature_toggle.name.tweakPotionWarning": "트윅 물약 경고", "tweakeroo.config.feature_toggle.name.tweakPrintDeathCoordinates": "트윅 사망 좌표 표기", "tweakeroo.config.feature_toggle.name.tweakRenderEdgeChunks": "트윅 렌더 가장자리 청크", "tweakeroo.config.feature_toggle.name.tweakRenderInvisibleEntities": "트윅 투명 엔티티", "tweakeroo.config.feature_toggle.name.tweakRenderLimitEntities": "트윅 렌더 제한 엔티티", "tweakeroo.config.feature_toggle.name.tweakRepairMode": "트윅 수리 모드", "tweakeroo.config.feature_toggle.name.tweakSculkPulseLength": "§6트윅 스컬크 펄스 길이§r", "tweakeroo.config.feature_toggle.name.tweakSelectiveBlocksRenderOutline": "트윅 선택적 블록 렌더 외곽선", "tweakeroo.config.feature_toggle.name.tweakSelectiveBlocksRendering": "트윅 선택적 블록 렌더링", "tweakeroo.config.feature_toggle.name.tweakServerDataSync": "트윅 서버 데이터 동기화", "tweakeroo.config.feature_toggle.name.tweakServerDataSyncBackup": "트윅 서버 데이터 동기화 백업", "tweakeroo.config.feature_toggle.name.tweakShulkerBoxDisplay": "트윅 셜커 박스 표시", "tweakeroo.config.feature_toggle.name.tweakSignCopy": "트윅 표지판 복사", "tweakeroo.config.feature_toggle.name.tweakSnapAim": "트윅 스냅 조준", "tweakeroo.config.feature_toggle.name.tweakSnapAimLock": "트윅 스냅 조준 잠금", "tweakeroo.config.feature_toggle.name.tweakSneak_1_15_2": "트윅 웅크리기_1.15.2", "tweakeroo.config.feature_toggle.name.tweakSpectatorTeleport": "트윅 관전자 순간이동", "tweakeroo.config.feature_toggle.name.tweakStructureBlockLimit": "§6트윅 구조물 블록 한계§r", "tweakeroo.config.feature_toggle.name.tweakSwapAlmostBrokenTools": "트윅 스왑 파괴 직전 도구", "tweakeroo.config.feature_toggle.name.tweakTabCompleteCoordinate": "트윅 탭 완성 좌표", "tweakeroo.config.feature_toggle.name.tweakToolSwitch": "트윅 도구 전환", "tweakeroo.config.feature_toggle.name.tweakWaterVisibility": "tweakWaterVisibility", "tweakeroo.config.feature_toggle.name.tweakWeaponSwitch": "트윅 무기 전환", "tweakeroo.config.feature_toggle.name.tweakYMirror": "트윅 Y 대칭", "tweakeroo.config.feature_toggle.name.tweakZoom": "트윅 확대", "tweakeroo.config.feature_toggle.prettyName.tweakAccurateBlockPlacement": "정확한 블록 설치", "tweakeroo.config.feature_toggle.prettyName.tweakAfterClicker": "클리커 이후", "tweakeroo.config.feature_toggle.prettyName.tweakAimLock": "조준 잠금", "tweakeroo.config.feature_toggle.prettyName.tweakAngelBlock": "천사 블록", "tweakeroo.config.feature_toggle.prettyName.tweakAreaSelector": "영역 선택기", "tweakeroo.config.feature_toggle.prettyName.tweakAutoSwitchElytra": "자동 전환 겉날개", "tweakeroo.config.feature_toggle.prettyName.tweakBlockReachOverride": "블록 도달 오버라이드", "tweakeroo.config.feature_toggle.prettyName.tweakBlockTypeBreakRestriction": "블록 타입 파괴 제한", "tweakeroo.config.feature_toggle.prettyName.tweakBreakingGrid": "파괴 격자", "tweakeroo.config.feature_toggle.prettyName.tweakBreakingRestriction": "파괴 제한", "tweakeroo.config.feature_toggle.prettyName.tweakBundleDisplay": "꾸러미 표시", "tweakeroo.config.feature_toggle.prettyName.tweakChatBackgroundColor": "채팅 배경 색상", "tweakeroo.config.feature_toggle.prettyName.tweakChatPersistentText": "채팅 텍스트 유지", "tweakeroo.config.feature_toggle.prettyName.tweakChatTimestamp": "채팅 타임 스탬프", "tweakeroo.config.feature_toggle.prettyName.tweakCommandBlockExtraFields": "명령 블록 추가 필드", "tweakeroo.config.feature_toggle.prettyName.tweakCreativeExtraItems": "크리에이티브 추가 아이템", "tweakeroo.config.feature_toggle.prettyName.tweakCustomFlatPresets": "사용자 지정 평지 프리셋", "tweakeroo.config.feature_toggle.prettyName.tweakCustomFlyDeceleration": "사용자 지정 비행 감속", "tweakeroo.config.feature_toggle.prettyName.tweakCustomInventoryScreenScale": "사용자 지정 보관함 화면 크기", "tweakeroo.config.feature_toggle.prettyName.tweakDarknessVisibility": "Darkness Visibility", "tweakeroo.config.feature_toggle.prettyName.tweakElytraCamera": "겉날개 카메라", "tweakeroo.config.feature_toggle.prettyName.tweakEmptyShulkerBoxesStack": "빈 셜커 박스 스택", "tweakeroo.config.feature_toggle.prettyName.tweakEntityReachOverride": "엔티티 도달 오버라이드", "tweakeroo.config.feature_toggle.prettyName.tweakEntityTypeAttackRestriction": "엔티티 타입 공격 제한", "tweakeroo.config.feature_toggle.prettyName.tweakExplosionReducedParticles": "폭발 입자 감소", "tweakeroo.config.feature_toggle.prettyName.tweakF3Cursor": "F3 커서", "tweakeroo.config.feature_toggle.prettyName.tweakFakeSneakPlacement": "가상 웅크리기 설치", "tweakeroo.config.feature_toggle.prettyName.tweakFakeSneaking": "가상 웅크리기", "tweakeroo.config.feature_toggle.prettyName.tweakFastBlockPlacement": "빠른 블록 설치", "tweakeroo.config.feature_toggle.prettyName.tweakFastLeftClick": "빠른 왼쪽 클릭", "tweakeroo.config.feature_toggle.prettyName.tweakFastRightClick": "빠른 오른쪽 클릭", "tweakeroo.config.feature_toggle.prettyName.tweakFillCloneLimit": "fill clone 한계", "tweakeroo.config.feature_toggle.prettyName.tweakFlexibleBlockPlacement": "유연한 블록 설치", "tweakeroo.config.feature_toggle.prettyName.tweakFlySpeed": "비행 속도", "tweakeroo.config.feature_toggle.prettyName.tweakFreeCamera": "자유 카메라", "tweakeroo.config.feature_toggle.prettyName.tweakGammaOverride": "감마 오버라이드", "tweakeroo.config.feature_toggle.prettyName.tweakHandRestock": "손 보충", "tweakeroo.config.feature_toggle.prettyName.tweakHangableEntityBypass": "걸 수 있는 엔티티 바이패스", "tweakeroo.config.feature_toggle.prettyName.tweakHoldAttack": "공격 누르기", "tweakeroo.config.feature_toggle.prettyName.tweakHoldUse": "사용 누르기", "tweakeroo.config.feature_toggle.prettyName.tweakHotbarScroll": "단축바 스크롤", "tweakeroo.config.feature_toggle.prettyName.tweakHotbarSlotCycle": "단축바 슬롯 사이클", "tweakeroo.config.feature_toggle.prettyName.tweakHotbarSlotRandomizer": "단축바 슬롯 랜덤화", "tweakeroo.config.feature_toggle.prettyName.tweakHotbarSwap": "단축바 스왑", "tweakeroo.config.feature_toggle.prettyName.tweakInventoryPreview": "보관함 미리보기", "tweakeroo.config.feature_toggle.prettyName.tweakItemUnstackingProtection": "아이템 언스택 보호", "tweakeroo.config.feature_toggle.prettyName.tweakLavaVisibility": "용암 가시성", "tweakeroo.config.feature_toggle.prettyName.tweakMapPreview": "지도 미리보기", "tweakeroo.config.feature_toggle.prettyName.tweakMovementKeysLast": "마지막 이동 키", "tweakeroo.config.feature_toggle.prettyName.tweakPeriodicAttack": "반복 공격", "tweakeroo.config.feature_toggle.prettyName.tweakPeriodicHoldAttack": "반복 공격 누르기", "tweakeroo.config.feature_toggle.prettyName.tweakPeriodicHoldUse": "반복 사용 누르기", "tweakeroo.config.feature_toggle.prettyName.tweakPeriodicUse": "반복 사용", "tweakeroo.config.feature_toggle.prettyName.tweakPermanentSneak": "계속 웅크리기", "tweakeroo.config.feature_toggle.prettyName.tweakPermanentSprint": "계속 달리기", "tweakeroo.config.feature_toggle.prettyName.tweakPickBeforePlace": "설치 전에 선택", "tweakeroo.config.feature_toggle.prettyName.tweakPlacementGrid": "설치 격자", "tweakeroo.config.feature_toggle.prettyName.tweakPlacementLimit": "설치 한계", "tweakeroo.config.feature_toggle.prettyName.tweakPlacementRestriction": "설치 제한", "tweakeroo.config.feature_toggle.prettyName.tweakPlacementRestrictionFirst": "설치 제한 첫 번째", "tweakeroo.config.feature_toggle.prettyName.tweakPlacementRestrictionHand": "설치 제한 손", "tweakeroo.config.feature_toggle.prettyName.tweakPlayerInventoryPeek": "플레이어 보관함 미리보기", "tweakeroo.config.feature_toggle.prettyName.tweakPlayerListAlwaysVisible": "플레이어 목록 언제나 표시", "tweakeroo.config.feature_toggle.prettyName.tweakPotionWarning": "물약 경고", "tweakeroo.config.feature_toggle.prettyName.tweakPrintDeathCoordinates": "사망 좌표 표기", "tweakeroo.config.feature_toggle.prettyName.tweakRenderEdgeChunks": "렌더 가장자리 청크", "tweakeroo.config.feature_toggle.prettyName.tweakRenderInvisibleEntities": "투명 엔티티", "tweakeroo.config.feature_toggle.prettyName.tweakRenderLimitEntities": "렌더 제한 엔티티", "tweakeroo.config.feature_toggle.prettyName.tweakRepairMode": "수리 모드", "tweakeroo.config.feature_toggle.prettyName.tweakSculkPulseLength": "스컬크 펄스 길이", "tweakeroo.config.feature_toggle.prettyName.tweakSelectiveBlocksRenderOutline": "선택적 블록 렌더 외곽선", "tweakeroo.config.feature_toggle.prettyName.tweakSelectiveBlocksRendering": "선택적 블록 렌더링", "tweakeroo.config.feature_toggle.prettyName.tweakServerDataSync": "서버 데이터 동기화", "tweakeroo.config.feature_toggle.prettyName.tweakServerDataSyncBackup": "서버 데이터 동기화 백업", "tweakeroo.config.feature_toggle.prettyName.tweakShulkerBoxDisplay": "셜커 박스 표시", "tweakeroo.config.feature_toggle.prettyName.tweakSignCopy": "표지판 복사", "tweakeroo.config.feature_toggle.prettyName.tweakSnapAim": "스냅 조준", "tweakeroo.config.feature_toggle.prettyName.tweakSnapAimLock": "스냅 조준 잠금", "tweakeroo.config.feature_toggle.prettyName.tweakSneak_1_15_2": "웅크리기_1.15.2", "tweakeroo.config.feature_toggle.prettyName.tweakSpectatorTeleport": "관전자 순간이동", "tweakeroo.config.feature_toggle.prettyName.tweakStructureBlockLimit": "구조물 블록 한계", "tweakeroo.config.feature_toggle.prettyName.tweakSwapAlmostBrokenTools": "스왑 파괴 직전 도구", "tweakeroo.config.feature_toggle.prettyName.tweakTabCompleteCoordinate": "탭 완성 좌표", "tweakeroo.config.feature_toggle.prettyName.tweakToolSwitch": "도구 전환", "tweakeroo.config.feature_toggle.prettyName.tweakWaterVisibility": "Water Visibility", "tweakeroo.config.feature_toggle.prettyName.tweakWeaponSwitch": "무기 전환", "tweakeroo.config.feature_toggle.prettyName.tweakYMirror": "Y 대칭", "tweakeroo.config.feature_toggle.prettyName.tweakZoom": "확대", "tweakeroo.config.fixes.comment.elytraFix": "Earthcomputer와 N<PERSON>ie의 겉날개 착륙 개선입니다.\n겉날개 전개 개선은 이제 게임에 적용되었으므로,\n지금은 착륙에만 영향을 미칩니다.", "tweakeroo.config.fixes.comment.elytraSprintCancel": "겉날개 사용 도중 달리기를 하면 플레이어가\n지워지는 바닐라 버그(MC-279688)를 수정합니다.\n이 설정은 1.21.5에서 고쳐졌기 때문에 1.21.4에서만 필요합니다.", "tweakeroo.config.fixes.comment.macHorizontalScroll": "Mac/OSX를 사용하는 경우, 이는 malilib 기반 모드의 모든\n스크롤 처리를 중단하지 않고 hscroll 모드와 동일한\n개선/변경 사항을 적용합니다.", "tweakeroo.config.fixes.comment.ravagerClientBlockBreakFix": "클라이언트 측에서 파괴수가 블록을 파괴할 때 발생하는\n성가신 고스트 블록 비동기 문제를 개선합니다.", "tweakeroo.config.fixes.name.elytraFix": "겉날개 개선", "tweakeroo.config.fixes.name.elytraSprintCancel": "겉날개 달리기 취소", "tweakeroo.config.fixes.name.macHorizontalScroll": "맥 수평 스크롤", "tweakeroo.config.fixes.name.ravagerClientBlockBreakFix": "파괴수 클라이언트 블록 파괴 개선", "tweakeroo.config.generic.comment.accuratePlacementProtocol": "활성화된 경우, '유연한 블록 설치'와 '정확한 블록 설치'는\nCarpet 모드에 구현된 프로토콜을 사용합니다.\n§6참고: 이것은 클릭한 블록 측면만 고려하는 블록(호퍼, 원목 등)을\n§6제외한 모든 블록의 회전이 작동하는 데 필요합니다.", "tweakeroo.config.generic.comment.accuratePlacementProtocolMode": "\"정확한 설치 프로토콜\"에서 사용하는 타입.\n- 자동: 싱글 플레이어에서는 버전 3를 사용하고, 멀티 플레이어에서는\n  기본적으로 반블록 전용 타입을 사용합니다. 다만 서버에 'carpet:hello'\n  패킷을 보내는 Carpet 모드가 있는 경우, 해당 서버에서 버전 2를 사용합니다.\n- 버전 3: 트위커루 자체 지원만 (싱글 플레이어에서) 하거나 Servux와 사용합니다.\n- 버전 2: Carpet 모드가 적용된 서버와 호환됩니다.\n  (skyrising과 DeadlyMC의 QuickCarpet 또는\n  FabricCarpet에 추가로 CarpetExtra 사용 가능.\n  두 경우 모두 서버에서 'accurateBlockPlacement'\n  Carpet 규칙을 활성화해야 합니다.)\n- 반블록 전용: 상단 반블록만 개선합니다. Paper 서버와 호환됩니다.\n- 없음: 좌표를 수정하지 않습니다.", "tweakeroo.config.generic.comment.afterClickerClickCount": "트윅 클리커 이후'가 활성화된 경우,\n블록 설치 당 수행할 오른쪽 클릭 수입니다.", "tweakeroo.config.generic.comment.angelBlockPlacementDistance": "'트윅 천사 블록'이 활성화된 경우, 공중에 설치할\n수 있는 블록과 플레이어와의 거리입니다.\n서버가 허용하는 최대 거리는 5입니다.", "tweakeroo.config.generic.comment.areaSelectionUseAll": "공기를 선택 영역에 포함할지 여부입니다.\nAndrew54757가 TweakFork에서 작성했습니다.", "tweakeroo.config.generic.comment.blockReachDistance": "오버라이드 트윅이 활성화된 경우 사용할 블록 도달 거리입니다.\n게임에서 허용하는 최대값은 64입니다.\n§6서버에서 정의된 규칙보다 [0.5 - 1.0]\n§6이상 높은 값으로 사용하지 마십시오.", "tweakeroo.config.generic.comment.blockTypeBreakRestrictionWarn": "블록 타입 제한 기능이 블록 파괴를 방지하는 경우\n표시할 경고 메시지(있는 경우)의 타입을 선택합니다.", "tweakeroo.config.generic.comment.breakingGridSize": "격자 파괴 모드의 격자 간격 크기입니다.\n값을 빠르게 조정하려면 트윅 토글 키지정을\n누른 채로 휠을 스크롤합니다.", "tweakeroo.config.generic.comment.breakingRestrictionMode": "사용할 파괴 제한 모드입니다. (단축키로 선택 가능)", "tweakeroo.config.generic.comment.bundleDisplayBgColor": "꾸러미 미리보기의 배경색을 꾸러미의 염색 색상과 일치시킵니다.", "tweakeroo.config.generic.comment.bundleDisplayRequireShift": "꾸러미 미리보기를 위해 Shift 키를 눌러야 하는지 여부입니다.\n§6참고: 이것을 비활성화하면 바닐라 꾸러미 툴팁이 완전히 안 보이게 됩니다.", "tweakeroo.config.generic.comment.bundleDisplayRowWidth": "꾸러미 미리보기의 행 너비 크기를 조정합니다.\n기본값보다 작거나 크면 텍스처 표시 문제가 발생할 수 있습니다.", "tweakeroo.config.generic.comment.chatBackgroundColor": "'트윅 채팅 배경 색상'이 활성화된 경우\n채팅 메시지의 배경색입니다.", "tweakeroo.config.generic.comment.chatTimeFormat": "채팅 메시지의 시간 형식입니다. '트윅 채팅 타임 스탬프'가 활성화된 경우\nJava SimpleDateFormat 형식 지정자를 사용합니다.", "tweakeroo.config.generic.comment.clientPlacementRotation": "Carpet 모드 없이 싱글 플레이어에서 정확한 설치가 작동하는 것처럼,\n싱글 플레이어 및 클라이언트 측 설치 회전을 활성화합니다.", "tweakeroo.config.generic.comment.customInventoryGuiScale": "트윅 사용자 지정 보관함 화면 크기'가 활성화된 경우,\n보관함 화면에 사용할 GUI 크기 값입니다.", "tweakeroo.config.generic.comment.debugLogging": "특정 문제나 충돌을 디버깅하기 위해 게임 콘솔에서\n일부 디버그 로그 메시지를 활성화합니다.", "tweakeroo.config.generic.comment.darknessScaleOverrideValue": "The override value used by 'tweakDarknessVisibility'\nto automatically weaken the 'darkening' screen effect.\nThis is a percentage value for the amount\nthat the screen darkens for each effect pulse.", "tweakeroo.config.generic.comment.elytraCameraIndicator": "겉날개 카메라 모드가 활성화되었을 때 실제 피치\n각도 표시기를 렌더링할 것인지 여부입니다.", "tweakeroo.config.generic.comment.entityReachDistance": "오버라이드 트윅이 활성화된 경우 사용할 엔티티 도달 거리입니다.\n게임에서 허용하는 최대값은 64입니다.\n§6서버에서 정의된 규칙보다 [0.5 - 1.0]\n§6이상 높은 값으로 사용하지 마십시오.", "tweakeroo.config.generic.comment.entityTypeAttackRestrictionWarn": "엔티티 타입 공격 제한 기능이 엔티티 공격을 방지하는 경우\n표시할 경고 메시지(있는 경우)의 타입을 선택합니다.", "tweakeroo.config.generic.comment.fastBlockPlacementCount": "빠른 블록 설치 트윅을 통해 게임 틱 당 설치할 수 있는 최대 블록 수입니다.", "tweakeroo.config.generic.comment.fastLeftClickAllowTools": "서바이벌 모드에서 도구 아이템을 들고 있는\n동안에도 빠른 왼쪽 클릭이 작동하도록 허용합니다.", "tweakeroo.config.generic.comment.fastLeftClickCount": "'트윅 빠른 왼쪽 클릭'이 활성화되어 있고 공격 버튼을\n눌렀을 때 게임 틱 당 왼쪽 클릭 횟수입니다.", "tweakeroo.config.generic.comment.fastPlacementRememberOrientation": "활성화된 경우, 빠른 블록 설치 기능은 항상\n설치한 첫 번째 블록의 방향을 기억합니다.\n이 기능이 없으면 방향은 유연한 블록 설치가\n활성화되고 발동한 경우에만 기억됩니다.", "tweakeroo.config.generic.comment.fastRightClickCount": "'트윅 빠른 오른쪽 클릭'이 활성화되어 있고 사용 버튼을\n눌렀을 때 게임 틱 당 오른쪽 클릭 횟수입니다.", "tweakeroo.config.generic.comment.fillCloneLimit": "블록 한계에 대한 오버라이드 트윅이 활성화된 경우,\n싱글 플레이어에서 /fill 및 /clone의 새로운 블록 한계값입니다.", "tweakeroo.config.generic.comment.flexibleBlockPlacementOverlayColor": "블록 설치 오버레이에서 현재 가리키는 영역의 색상입니다.", "tweakeroo.config.generic.comment.flyDecelerationFactor": "'사용자 지정 비행 감속' 트윅이 활성화된 경우\n플레이어가 얼마나 빨리 멈출지 조정합니다.", "tweakeroo.config.generic.comment.flySpeedIncrement1": "증가량1에 대한 비행 속도 변화량입니다.", "tweakeroo.config.generic.comment.flySpeedIncrement2": "증가량2에 대한 비행 속도 변화량입니다.", "tweakeroo.config.generic.comment.flySpeedPreset1": "비행 속도 프리셋 1", "tweakeroo.config.generic.comment.flySpeedPreset2": "비행 속도 프리셋 2", "tweakeroo.config.generic.comment.flySpeedPreset3": "비행 속도 프리셋 3", "tweakeroo.config.generic.comment.flySpeedPreset4": "비행 속도 프리셋 4", "tweakeroo.config.generic.comment.freeCameraPlayerInputs": "활성화된 경우, 자유 카메라 모드에서\n공격과 사용 동작(왼쪽, 오른쪽 클릭)이\n실제 플레이어에게 전달됩니다.", "tweakeroo.config.generic.comment.freeCameraPlayerMovement": "활성화된 경우, 자유 카메라 모드에서\n이동 입력이 카메라 대신 실제\n클라이언트 플레이어를 움직입니다.", "tweakeroo.config.generic.comment.gammaOverrideValue": "오버라이드 옵션이 활성화된 경우 사용할 감마 값입니다.", "tweakeroo.config.generic.comment.handRestockPre": "활성화된 경우, 손에 들고 있는 아이템\n스택이 소진되기 전에 재충전됩니다.", "tweakeroo.config.generic.comment.handRestockPreThreshold": "사전 보충 모드에서 아이템이 보충되는 스택 크기의 임계값입니다.", "tweakeroo.config.generic.comment.hangableEntityBypassInverse": "걸 수 있는 엔티티 타겟팅 바이패스 트윅이 활성화 된 경우,\n걸 수 있는 엔티티(아이템 액자 또는 그림)를 타겟팅 하기 위해\n웅크리기를 해야 하는지 안 해야 하는지를 제어합니다.\n > true - 웅크리기 = 무시/바이패스 엔티티\n > false - 웅크리기 = 타겟 엔티티", "tweakeroo.config.generic.comment.hotbarSlotCycleMax": "단축바 슬롯 사이클 트윅이 활성화된 경우\n사용/사이클 할 마지막 단축바 슬롯입니다.\n기본적으로 사이클은 여기에 설정된 최대 슬롯 번호를\n초과하면 첫 번째 슬롯으로 돌아옵니다.", "tweakeroo.config.generic.comment.hotbarSlotRandomizerMax": "단축바 슬롯 랜덤화 트윅이 활성화된 경우\n사용할 마지막 단축바 슬롯입니다.\n기본적으로 아이템 사용 후 1에서 이 최대 슬롯까지\n중에 무작위 단축바 슬롯이 선택됩니다.", "tweakeroo.config.generic.comment.hotbarSwapOverlayAlignment": "단축바 스왑 오버레이의 정렬 위치입니다.", "tweakeroo.config.generic.comment.hotbarSwapOverlayOffsetX": "단축바 스왑 오버레이의 수평 오프셋입니다.", "tweakeroo.config.generic.comment.hotbarSwapOverlayOffsetY": "단축바 스왑 오버레이의 수직 오프셋입니다.", "tweakeroo.config.generic.comment.inventoryPreviewVillagerBGColor": "직업에 따른 주민 거래 창의 배경 색상을\n활성화/비활성화합니다.", "tweakeroo.config.generic.comment.itemSwapDurabilityThreshold": "낮은 내구도 아이템 교체 기능에 대한\n내구도 임계값(남은 내구도 값)입니다.\n참고로 최대 내구도가 낮은 아이템은 이 값이 더\n낮아지고 내구도가 5%% 남은 시점에 교체됩니다.", "tweakeroo.config.generic.comment.itemUsePacketCheckBypass": "1.18.2에 추가된 새로운 거리/좌표 검사를 우회합니다.\n\n해당 검사는 \"정확한 설치 프로토콜\"을 망가뜨리고 회전\n(또는 다른 속성) 요청으로 설치된 모든 블록이 고스트 블록이 됩니다.\n\n기본적으로 이 기능을 비활성화할 필요가 없습니다.\n이 검사는 1.18.2 이전에는 존재하지도 않았습니다.", "tweakeroo.config.generic.comment.mapPreviewRequireShift": "지도 미리보기 할 때 Shift 키를 눌러야 하는지 여부를 설정합니다.", "tweakeroo.config.generic.comment.mapPreviewSize": "렌더링된 맵 미리보기의 크기입니다.", "tweakeroo.config.generic.comment.periodicAttackInterval": "자동 공격(왼쪽 클릭) 사이의 게임 틱 수입니다.", "tweakeroo.config.generic.comment.periodicAttackResetIntervalOnActivate": "마우스 스크롤 휠을 이용해 반복 공격 간격을 조정한 경우,\n비활성화 했을 때 공격 간격을 재설정합니다.", "tweakeroo.config.generic.comment.periodicHoldAttackDuration": "공격을 누르고 있는 게임 틱 수입니다.", "tweakeroo.config.generic.comment.periodicHoldAttackInterval": "공격 누르기(왼쪽 클릭) 시작 시점 간격의 게임 틱 수 입니다.", "tweakeroo.config.generic.comment.periodicHoldAttackResetIntervalOnActivate": "마우스 스크롤 휠을 이용해 공격 누르기 간격을 조정한 경우,\n비활성화 했을 때 공격 누르기 간격을 재설정합니다.", "tweakeroo.config.generic.comment.periodicHoldUseDuration": "사용을 누르고 있는 게임 틱 수입니다.", "tweakeroo.config.generic.comment.periodicHoldUseInterval": "사용 누르기(오른쪽 클릭) 시작 시점 간격의 게임 틱 수 입니다.", "tweakeroo.config.generic.comment.periodicHoldUseResetIntervalOnActivate": "마우스 스크롤 휠을 이용해 사용 누르기 간격을 조정한 경우,\n비활성화 했을 때 사용 누르기 간격을 재설정합니다.", "tweakeroo.config.generic.comment.periodicUseInterval": "자동 사용(오른쪽 클릭) 사이의 게임 틱 수입니다.", "tweakeroo.config.generic.comment.periodicUseResetIntervalOnActivate": "마우스 스크롤 휠을 이용해 반복 사용 간격을 조정한 경우,\n비활성화 했을 때 사용 간격을 재설정합니다.", "tweakeroo.config.generic.comment.permanentSneakAllowInGUIs": "true인 경우, GUI가 열려 있는 동안에도\n계속 웅크리기 트윅이 작동합니다.", "tweakeroo.config.generic.comment.placementGridSize": "격자 설치 모드의 격자 간격 크기입니다.\n값을 빠르게 조정하려면 트윅 토글 키지정을\n누른 채로 휠을 스크롤합니다.", "tweakeroo.config.generic.comment.placementLimit": "트윅 설치 한계가 활성화된 경우, 오른쪽 클릭당\n최대로 설치할 수 있는 블록 수입니다.\n값을 빠르게 조정하려면 트윅 토글 키지정을\n누른 채로 휠을 스크롤합니다.", "tweakeroo.config.generic.comment.placementRestrictionMode": "사용할 설치 제한 모드입니다. (단축키로 선택 가능)", "tweakeroo.config.generic.comment.placementRestrictionTiedToFast": "활성화된 경우, 빠른 설치 모드를 토글 했을 때\n설치 제한 모드 또한 이에 종속되어 on/off 됩니다.", "tweakeroo.config.generic.comment.potionWarningBeneficialOnly": "\"유익한\" 물약의 효과가 사라지는 경우에만 이를 경고합니다.", "tweakeroo.config.generic.comment.potionWarningThreshold": "경고가 표시되기 시작하는 물약 효과의\n남은 지속 시간(틱 단위) 값입니다.", "tweakeroo.config.generic.comment.rememberFlexibleFromClick": "활성화된 경우, 사용 키(오른쪽 클릭)를 누르고 있는 동안,\n유연한 블록 설치 상태가 첫 번째로 설치한 블록 상태를 기억합니다.\n기본적으로 모든 블록을 같은 방향으로 빠르게 설치하기 위해\n모든 유연한 활성화 키를 계속 누르고 있을 필요는 없습니다.", "tweakeroo.config.generic.comment.renderLimitItem": "프레임당 렌더링 되는 아이템 엔티티의 최대 수입니다.\n일반적으로 -1을 사용합니다. 즉, 이 한계를 비활성화합니다.", "tweakeroo.config.generic.comment.renderLimitXPOrb": "프레임당 렌더링 되는 경험치 구슬 엔티티의 최대 수입니다.\n일반적으로 -1을 사용합니다. 즉, 이 한계를 비활성화합니다.", "tweakeroo.config.generic.comment.sculkSensorPulseLength": "'트윅 스컬크 펄스 길이' 트윅이 활성화된 경우 스컬크 센서가 감지하는 펄스 거리입니다.", "tweakeroo.config.generic.comment.selectiveBlocksHideEntities": "선택적 블록 렌더링 시 엔티티를 숨길지 여부입니다.\nAndrew54757가 TweakFork에서 작성했습니다.", "tweakeroo.config.generic.comment.selectiveBlocksHideParticles": "선택적 블록 렌더링 시 입자를 숨길지 여부입니다.\nAndrew54757가 TweakFork에서 작성했습니다.", "tweakeroo.config.generic.comment.selectiveBlocksNoHit": "타겟팅한 숨겨진 블록들을 비활성화할지 여부입니다.\nAndrew54757가 TweakFork에서 작성했습니다.", "tweakeroo.config.generic.comment.selectiveBlocksTrackPistons": "선택적 블록 렌더링 시 피스톤 움직임을 추적할지 여부입니다.\nAndrew54757가 TweakFork에서 작성했습니다.", "tweakeroo.config.generic.comment.serverDataSyncCacheRefresh": "초 단위로 표시되는 캐시 새로고침 값입니다.\n이 값은 엔터티 데이터 요청이 서버에서 새로고침 될 때마다\n사용되며, 캐시에 아직 남아있더라도 마찬가지입니다.\n이 값은 \"엔티티 데이터 동기화 캐시 시간 지연\" 값의\n약 25% 이하로 설정해야 합니다.\n'0.05f' 값은 50ms 또는 게임 틱마다 한 번을 의미합니다.", "tweakeroo.config.generic.comment.serverDataSyncCacheTimeout": "엔티티 캐시가 정보를 보관하는 시간 지연 값(초).\n값이 낮을수록 업데이트가 더 자주 이루어집니다.", "tweakeroo.config.generic.comment.serverNbtRequestRate": "서버 엔티티 데이터 동기화에 대한 요청 속도 한계값입니다.", "tweakeroo.config.generic.comment.shulkerDisplayBgColor": "셜커 상자 표시 배경 색상을 상자의 염색 색상으로 설정합니다.", "tweakeroo.config.generic.comment.shulkerDisplayEnderChest": "엔더 상자의 표시를 셜커 상자 표시와 같게 설정합니다.", "tweakeroo.config.generic.comment.shulkerDisplayRequireShift": "셜커 상자 미리보기에 Shift 키를 눌러야 하는지 여부입니다.", "tweakeroo.config.generic.comment.slotSyncWorkaround": "이 기능은 빠른 오른쪽 클릭 트윅처럼 빠르게 사용되는 아이템의\n내구도나 스택 크기를 서버가 덮어쓰는 것을 방지합니다.", "tweakeroo.config.generic.comment.slotSyncWorkaroundAlways": "빠른 오른쪽 클릭이나 빠른 블록 설치를 사용할 때 뿐만 아니라\n사용 키를 누르고 있으면 언제나 슬롯 동기화 차선책을 활성화합니다.\n이는 주로 라이트매티카의 쉬운 설치 모드처럼 사용 키를 누르고 있을\n때 아이템을 빠르게 사용할 수 있게 해주는 다른 모드를 위한 것입니다.", "tweakeroo.config.generic.comment.snapAimIndicator": "스냅 조준 각도 표시기를 렌더링할지 여부입니다.", "tweakeroo.config.generic.comment.snapAimIndicatorColor": "스냅 조준 표시기의 배경 색상입니다.", "tweakeroo.config.generic.comment.snapAimMode": "스냅 조준 모드: 요, 피치 또는 둘 다", "tweakeroo.config.generic.comment.snapAimOnlyCloseToAngle": "활성화된 경우, 스냅 조준은 현재 각도가\n목표 각도에 충분히 가까운 경우에만 스냅됩니다.\n임계값은 스냅 조준 임계값 에서 설정할 수 있습니다.", "tweakeroo.config.generic.comment.snapAimPitchOvershoot": "피치 각도를 정상값인 +/- 90도에서 +/- 180도까지\n초과하여 설정할 수 있게 허용할 것인지 여부입니다.", "tweakeroo.config.generic.comment.snapAimPitchStep": "스냅 조준 트윅의 피치 각도 증감폭입니다.", "tweakeroo.config.generic.comment.snapAimThresholdPitch": "플레이어 회전이 목표 각도에 스냅되는 각도 임계값입니다.", "tweakeroo.config.generic.comment.snapAimThresholdYaw": "플레이어 회전이 목표 각도에 스냅되는 각도 임계값입니다.", "tweakeroo.config.generic.comment.snapAimYawStep": "스냅 조준 트윅의 요 각도 증감폭입니다.", "tweakeroo.config.generic.comment.structureBlockMaxSize": "구조물 블록의 저장 영역에 대한 최대 크기입니다.", "tweakeroo.config.generic.comment.toolSwapBetterEnchants": "도구 전환 시 목표에 사용하기에 적합한\n도구인지 확인한 후, 도구의 인챈트\n부여와 희귀도를 고려합니다.", "tweakeroo.config.generic.comment.toolSwapPreferSilkTouch": "Consider your final Silk Touch preference\nwith tools whenever all other results are\nequal (Block Breaking Speed, Enchantments, Materials, etc).\nThis is useful for determining the default\npickaxe to choose when SilkTouchFirst is not\napplicable for the block that you are mining.\n§6NOTE: When this is disabled, the mod will always prefer\n§6to use the non-silk touch tool during the\n§6comparison if one is found and matches\n§6all of the criteria.", "tweakeroo.config.generic.comment.toolSwapBambooUsesSwordFirst": "Check if the targeted block is Bamboo,\nand if so; prefer using a Sword instead\nof an Axe to mine it.", "tweakeroo.config.generic.comment.toolSwapNeedsShearsFirst": "Check if the block requires Shears first,\nsuch as wool, vines or cobwebs.\nThe blocks chosen are based on the new\n'§b#malilib:needs_shears§r' Block Tag\nsince this doesn't exist in Vanilla.\n§6NOTE: This feature is only activate when\n§6the block is not properly matched in Vanilla.", "tweakeroo.config.generic.comment.toolSwapSilkTouchFirst": "유리 또는 엔더 상자같이 섬세한 손길이\n필요한 블록인지 우선 확인합니다.\n바닐라에는 관련 태그가 없으므로 블록 선택은\n새로운 '§b#malilib:needs_silk_touch§r'\n블록 태그를 기준으로 합니다.", "tweakeroo.config.generic.comment.toolSwapSilkTouchOres": "'§b#malilib:ore_blocks§r' 태그를 통해\n블록이 광석 블록인지 우선 확인한 다음,\n여기에 섬세한 손길을 사용합니다.\n§6참고: 행운을 사용하려면 비활성화하세요.§r", "tweakeroo.config.generic.comment.toolSwapSilkTouchOverride": "Check the Silk Touch Override list for\nblocks to apply SilkTouchFirst to.\n§6NOTE: This can work without 'toolSwapSilkTouchFirst'\n§6being enabled, but it is designed to work\n§6the same, and can co-exist with it on.", "tweakeroo.config.generic.comment.toolSwitchIgnoredSlots": "도구 전환 트윅이 활성화되어 있을 때 사용하지 않는 슬롯입니다.", "tweakeroo.config.generic.comment.toolSwitchableSlots": "도구 전환 트윅이 도구를 배치할 수 있는 슬롯입니다.\n참고로 선호하는 도구가 이미 단축바에 있는 경우에도\n다른 슬롯으로 전환할 수 있지만, 여기에 설정된\n슬롯으로만 새 도구를 전환합니다.", "tweakeroo.config.generic.comment.weaponSwapBetterEnchants": "무기 전환 시 목표에 사용하기에 적합한\n무기인지 확인한 후, 무기의 인챈트\n부여와 희귀도를 고려합니다.", "tweakeroo.config.generic.comment.zoomAdjustMouseSensitivity": "활성화된 경우, 확대 기능이 활성화된 때와 확대\n키가 활성화된 때에 마우스 감도가 감소합니다.", "tweakeroo.config.generic.comment.zoomFov": "확대 기능에 사용되는 시야 범위 값입니다.", "tweakeroo.config.generic.comment.zoomResetFovOnActivate": "마우스 스크롤 휠을 이용해 확대 시야 범위를 조정한 경우,\n비활성화 했을 때 확대 시야 범위를 재설정합니다.", "tweakeroo.config.generic.name.accuratePlacementProtocol": "정확한 설치 프로토콜", "tweakeroo.config.generic.name.accuratePlacementProtocolMode": "정확한 설치 프로토콜 모드", "tweakeroo.config.generic.name.afterClickerClickCount": "클리커 이후 클릭 수", "tweakeroo.config.generic.name.angelBlockPlacementDistance": "천사 블록 설치 거리", "tweakeroo.config.generic.name.areaSelectionUseAll": "영역 선택기 사용 모두", "tweakeroo.config.generic.name.blockReachDistance": "블록 도달 거리", "tweakeroo.config.generic.name.blockTypeBreakRestrictionWarn": "블록 타입 파괴 제한 경고", "tweakeroo.config.generic.name.breakingGridSize": "파괴 격자 크기", "tweakeroo.config.generic.name.breakingRestrictionMode": "파괴 제한 모드", "tweakeroo.config.generic.name.bundleDisplayBgColor": "꾸러미 표시 배경색", "tweakeroo.config.generic.name.bundleDisplayRequireShift": "꾸러미 표시 Shift 여부", "tweakeroo.config.generic.name.bundleDisplayRowWidth": "꾸러미 표시 행 너비", "tweakeroo.config.generic.name.chatBackgroundColor": "채팅 배경색", "tweakeroo.config.generic.name.chatTimeFormat": "채팅 시간 형식", "tweakeroo.config.generic.name.clientPlacementRotation": "클라이언트 설치 회전", "tweakeroo.config.generic.name.customInventoryGuiScale": "사용자 지정 보관함 GUI 크기", "tweakeroo.config.generic.name.debugLogging": "디버그 로깅", "tweakeroo.config.generic.name.darknessScaleOverrideValue": "darknessScaleOverrideValue", "tweakeroo.config.generic.name.elytraCameraIndicator": "겉날개 카메라 표시기", "tweakeroo.config.generic.name.entityReachDistance": "엔티티 도달 거리", "tweakeroo.config.generic.name.entityTypeAttackRestrictionWarn": "엔티티 타입 공격 제한 경고", "tweakeroo.config.generic.name.fastBlockPlacementCount": "빠른 블록 설치 수", "tweakeroo.config.generic.name.fastLeftClickAllowTools": "빠른 왼쪽 클릭 허용 도구", "tweakeroo.config.generic.name.fastLeftClickCount": "빠른 왼쪽 클릭 수", "tweakeroo.config.generic.name.fastPlacementRememberOrientation": "빠른 설치 방향 기억", "tweakeroo.config.generic.name.fastRightClickCount": "빠른 오른쪽 클릭 수", "tweakeroo.config.generic.name.fillCloneLimit": "fill clone 한계", "tweakeroo.config.generic.name.flexibleBlockPlacementOverlayColor": "유연한 블록 설치 오버레이 색상", "tweakeroo.config.generic.name.flyDecelerationFactor": "비행 감속 계수", "tweakeroo.config.generic.name.flySpeedIncrement1": "비행 속도 증가량1", "tweakeroo.config.generic.name.flySpeedIncrement2": "비행 속도 증가량2", "tweakeroo.config.generic.name.flySpeedPreset1": "비행 속도 프리셋1", "tweakeroo.config.generic.name.flySpeedPreset2": "비행 속도 프리셋2", "tweakeroo.config.generic.name.flySpeedPreset3": "비행 속도 프리셋3", "tweakeroo.config.generic.name.flySpeedPreset4": "비행 속도 프리셋4", "tweakeroo.config.generic.name.freeCameraPlayerInputs": "자유 카메라 플레이어 입력", "tweakeroo.config.generic.name.freeCameraPlayerMovement": "자유 카메라 플레이어 이동", "tweakeroo.config.generic.name.gammaOverrideValue": "감마 오버라이드 값", "tweakeroo.config.generic.name.handRestockPre": "손 사전 보충", "tweakeroo.config.generic.name.handRestockPreThreshold": "손 사전 보충 임계값", "tweakeroo.config.generic.name.hangableEntityBypassInverse": "걸 수 있는 엔티티 바이패스 역전", "tweakeroo.config.generic.name.hotbarSlotCycleMax": "단축바 슬롯 사이클 최대", "tweakeroo.config.generic.name.hotbarSlotRandomizerMax": "단축바 슬롯 랜덤화 최대", "tweakeroo.config.generic.name.hotbarSwapOverlayAlignment": "단축바 스왑 오버레이 정렬 위치", "tweakeroo.config.generic.name.hotbarSwapOverlayOffsetX": "단축바 스왑 오버레이 오프셋 X", "tweakeroo.config.generic.name.hotbarSwapOverlayOffsetY": "단축바 스왑 오버레이 오프셋 Y", "tweakeroo.config.generic.name.inventoryPreviewVillagerBGColor": "보관함 미리보기 주민 배경 색상", "tweakeroo.config.generic.name.itemSwapDurabilityThreshold": "아이템 스왑 내구도 임계값", "tweakeroo.config.generic.name.itemUsePacketCheckBypass": "아이템 사용 패킷 체크 바이패스", "tweakeroo.config.generic.name.mapPreviewRequireShift": "지도 미리보기 Shift 여부", "tweakeroo.config.generic.name.mapPreviewSize": "지도 미리보기 크기", "tweakeroo.config.generic.name.periodicAttackInterval": "반복 공격 간격", "tweakeroo.config.generic.name.periodicAttackResetIntervalOnActivate": "활성화 시 반복 공격 재설정 간격", "tweakeroo.config.generic.name.periodicHoldAttackDuration": "반복 공격 누르기 시간", "tweakeroo.config.generic.name.periodicHoldAttackInterval": "반복 공격 누르기 간격", "tweakeroo.config.generic.name.periodicHoldAttackResetIntervalOnActivate": "반복 공격 누르기 활성화 시 간격 재설정", "tweakeroo.config.generic.name.periodicHoldUseDuration": "반복 사용 누르기 시간", "tweakeroo.config.generic.name.periodicHoldUseInterval": "반복 사용 누르기 간격", "tweakeroo.config.generic.name.periodicHoldUseResetIntervalOnActivate": "반복 사용 누르기 활성화 시 간격 재설정", "tweakeroo.config.generic.name.periodicUseInterval": "반복 사용 간격", "tweakeroo.config.generic.name.periodicUseResetIntervalOnActivate": "활성화 시 반복 사용 재설정 간격", "tweakeroo.config.generic.name.permanentSneakAllowInGUIs": "GUI에서 계속 웅크리기 허용", "tweakeroo.config.generic.name.placementGridSize": "설치 격자 크기", "tweakeroo.config.generic.name.placementLimit": "설치 한계", "tweakeroo.config.generic.name.placementRestrictionMode": "설치 제한 모드", "tweakeroo.config.generic.name.placementRestrictionTiedToFast": "설치 제한 모드가 빠른에 종속", "tweakeroo.config.generic.name.potionWarningBeneficialOnly": "물약 경고 유익한 경우만", "tweakeroo.config.generic.name.potionWarningThreshold": "물약 경고 임계값", "tweakeroo.config.generic.name.rememberFlexibleFromClick": "클릭 시 유연성 기억", "tweakeroo.config.generic.name.renderLimitItem": "렌더 제한 아이템", "tweakeroo.config.generic.name.renderLimitXPOrb": "렌더 제한 경험치 구슬", "tweakeroo.config.generic.name.sculkSensorPulseLength": "스컬크 센서 펄스 길이", "tweakeroo.config.generic.name.selectiveBlocksHideEntities": "선택적 블록 숨기기 엔티티", "tweakeroo.config.generic.name.selectiveBlocksHideParticles": "선택적 블록 숨기기 입자", "tweakeroo.config.generic.name.selectiveBlocksNoHit": "선택적 블록 노 히트", "tweakeroo.config.generic.name.selectiveBlocksTrackPistons": "선택적 블록 피스톤 추적", "tweakeroo.config.generic.name.serverDataSyncCacheRefresh": "서버 데이터 동기화 캐시 새로고침", "tweakeroo.config.generic.name.serverDataSyncCacheTimeout": "서버 데이터 동기화 캐시 시간 지연", "tweakeroo.config.generic.name.serverNbtRequestRate": "서버 NBT 요청률", "tweakeroo.config.generic.name.shulkerDisplayBgColor": "셜커 표시 배경 색상", "tweakeroo.config.generic.name.shulkerDisplayEnderChest": "셜커 표시 엔더 상자", "tweakeroo.config.generic.name.shulkerDisplayRequireShift": "셜커 표시 Shift 여부", "tweakeroo.config.generic.name.slotSyncWorkaround": "슬롯 동기화 차선책", "tweakeroo.config.generic.name.slotSyncWorkaroundAlways": "슬롯 동기화 차선책 언제나", "tweakeroo.config.generic.name.snapAimIndicator": "스냅 조준 표시기", "tweakeroo.config.generic.name.snapAimIndicatorColor": "스냅 조준 표시기 색상", "tweakeroo.config.generic.name.snapAimMode": "스냅 조준 모드", "tweakeroo.config.generic.name.snapAimOnlyCloseToAngle": "스냅 조준 각도 가까운 경우에만", "tweakeroo.config.generic.name.snapAimPitchOvershoot": "스냅 조준 피치 초과", "tweakeroo.config.generic.name.snapAimPitchStep": "스냅 조준 피치 증감폭", "tweakeroo.config.generic.name.snapAimThresholdPitch": "스냅 조준 임계값 피치", "tweakeroo.config.generic.name.snapAimThresholdYaw": "스냅 조준 임계값 요", "tweakeroo.config.generic.name.snapAimYawStep": "스냅 조준 요 증감폭", "tweakeroo.config.generic.name.structureBlockMaxSize": "구조물 블록 최대 크기", "tweakeroo.config.generic.name.toolSwapBetterEnchants": "도구 전환 인챈트 고려", "tweakeroo.config.generic.name.toolSwapPreferSilkTouch": "toolSwapPreferSilkTouch", "tweakeroo.config.generic.name.toolSwapBambooUsesSwordFirst": "toolSwapBambooUsesSwordFirst", "tweakeroo.config.generic.name.toolSwapNeedsShearsFirst": "toolSwapNeedsShearsFirst", "tweakeroo.config.generic.name.toolSwapSilkTouchFirst": "도구 전환 섬세한 손길 우선", "tweakeroo.config.generic.name.toolSwapSilkTouchOres": "도구 전환 섬세한 손길 광석", "tweakeroo.config.generic.name.toolSwapSilkTouchOverride": "toolSwapSilkTouchOverride", "tweakeroo.config.generic.name.toolSwitchIgnoredSlots": "도구 전환 무시 슬롯", "tweakeroo.config.generic.name.toolSwitchableSlots": "도구 전환 가능 슬롯", "tweakeroo.config.generic.name.weaponSwapBetterEnchants": "무기 전환 인챈트 고려", "tweakeroo.config.generic.name.zoomAdjustMouseSensitivity": "확대 마우스 감도 조정", "tweakeroo.config.generic.name.zoomFov": "확대 시야 범위", "tweakeroo.config.generic.name.zoomResetFovOnActivate": "확대 활성화 시 시야 범위 재설정", "tweakeroo.config.generic.prettyName.freeCameraPlayerInputs": "자유 카메라 플레이어 입력", "tweakeroo.config.generic.prettyName.freeCameraPlayerMovement": "자유 카메라 플레이어 이동", "tweakeroo.config.generic.prettyName.toolSwapBetterEnchants": "도구 전환 인챈트 고려", "tweakeroo.config.generic.prettyName.toolSwapBambooUsesSwordFirst": "Tool Swap Bamboo Uses Sword First", "tweakeroo.config.generic.prettyName.toolSwapNeedsShearsFirst": "Tool Swap Needs Shears First", "tweakeroo.config.generic.prettyName.toolSwapSilkTouchFirst": "도구 전환 섬세한 손길 우선", "tweakeroo.config.generic.prettyName.toolSwapPreferSilkTouch": "Tool Swap Prefer Silk Touch", "tweakeroo.config.generic.prettyName.toolSwapSilkTouchOres": "도구 전환 섬세한 손길 광석", "tweakeroo.config.generic.prettyName.toolSwapSilkTouchOverride": "Tool Swap Silk Touch Override", "tweakeroo.config.generic.prettyName.weaponSwapBetterEnchants": "무기 전환 인챈트 고려", "tweakeroo.config.hotkey.comment.accurateBlockPlacementInto": "클릭한 블록의 면을 향해 블록을 설치하는 정확한 블록 설치\n모드/오버레이 활성화 키입니다.", "tweakeroo.config.hotkey.comment.accurateBlockPlacementReverse": "원래 설치되는 방향에 반대 방향으로 블록을 설치하는\n정확한 블록 설치 모드/오버레이 활성화 키입니다.", "tweakeroo.config.hotkey.comment.areaSelectionAddToList": "선택된 블록들을 리스트에 추가합니다.\nAndrew54757가 TweakFork에서 작성했습니다.", "tweakeroo.config.hotkey.comment.areaSelectionOffset": "선택 위치를 오프셋하는 키입니다.\nAndrew54757가 TweakFork에서 작성했습니다.", "tweakeroo.config.hotkey.comment.areaSelectionRemoveFromList": "선택된 블록들을 리스트에서 제거합니다.\nAndrew54757가 TweakFork에서 작성했습니다.", "tweakeroo.config.hotkey.comment.breakingRestrictionModeColumn": "파괴 제한 모드를 열 모드로 전환합니다.", "tweakeroo.config.hotkey.comment.breakingRestrictionModeDiagonal": "파괴 제한 모드를 대각 모드로 전환합니다.", "tweakeroo.config.hotkey.comment.breakingRestrictionModeFace": "파괴 제한 모드를 면 모드로 전환합니다.", "tweakeroo.config.hotkey.comment.breakingRestrictionModeLayer": "파괴 제한 모드를 레이어 모드로 전환합니다.", "tweakeroo.config.hotkey.comment.breakingRestrictionModeLine": "파괴 제한 모드를 라인 모드로 전환합니다.", "tweakeroo.config.hotkey.comment.breakingRestrictionModePlane": "파괴 제한 모드를 평면 모드로 전환합니다.", "tweakeroo.config.hotkey.comment.copySignText": "이미 설치된 표지판에서 텍스트를 복사합니다.\n해당 텍스트는 트윅 표지판 복사 트윅과 함께 사용할 수 있습니다.", "tweakeroo.config.hotkey.comment.elytraCamera": "키가 활성화된 동안 현재 플레이어의 실제 회전은 고정하고 입력(마우스)이\n렌더링을 위한 별도의 \"카메라 회전\"에만 영향을 미치게 합니다.\n겉날개로 직선 비행을 할 때 아래/주변을 자유롭게 바라보기 위한 것입니다.", "tweakeroo.config.hotkey.comment.flexibleBlockPlacementAdjacent": "인접한 위치에 블록을 설치하는 유연한 블록 설치\n모드/오버레이 활성화 키입니다.", "tweakeroo.config.hotkey.comment.flexibleBlockPlacementOffset": "오프셋 또는 대각 위치에 블록을 설치하는 유연한\n블록 설치 모드/오버레이 활성화 키입니다.", "tweakeroo.config.hotkey.comment.flexibleBlockPlacementRotation": "회전/주시 방향 위치에 블록을 설치하는 유연한\n블록 설치 모드/오버레이 활성화 키입니다.", "tweakeroo.config.hotkey.comment.flyIncrement1": "증가량1로 비행 속도 변경합니다.", "tweakeroo.config.hotkey.comment.flyIncrement2": "증가량2로 비행 속도 변경합니다.", "tweakeroo.config.hotkey.comment.flyPreset1": "비행 프리셋 1(으)로 전환", "tweakeroo.config.hotkey.comment.flyPreset2": "비행 프리셋 2(으)로 전환", "tweakeroo.config.hotkey.comment.flyPreset3": "비행 프리셋 3(으)로 전환", "tweakeroo.config.hotkey.comment.flyPreset4": "비행 프리셋 4(으)로 전환", "tweakeroo.config.hotkey.comment.freeCameraPlayerInputs": "일반 -> 자유 카메라 플레이어 입력 옵션을 토글합니다.", "tweakeroo.config.hotkey.comment.freeCameraPlayerMovement": "일반 -> 자유 카메라 플레이어 이동 옵션을 토글합니다.", "tweakeroo.config.hotkey.comment.hotbarScroll": "플레이어 보관함 행을 통해 단축바를 스크롤\n할 수 있도록 하는 홀드 키입니다.", "tweakeroo.config.hotkey.comment.hotbarSwap1": "보관함에 첫 번째 행과 단축바를 스왑합니다.", "tweakeroo.config.hotkey.comment.hotbarSwap2": "보관함에 두 번째 행과 단축바를 스왑합니다.", "tweakeroo.config.hotkey.comment.hotbarSwap3": "보관함에 세 번째 행과 단축바를 스왑합니다.", "tweakeroo.config.hotkey.comment.hotbarSwapBase": "단축바/인벤토리 오버레이를 표시하는 기본 키입니다.", "tweakeroo.config.hotkey.comment.inventoryPreview": "보관함 미리보기 기능을 활성화하는 키입니다.", "tweakeroo.config.hotkey.comment.inventoryPreviewToggleScreen": "보관함 미리보기 화면을 엽니다.\n마우스를 이용해 툴팁을 볼 수 있습니다.", "tweakeroo.config.hotkey.comment.openConfigGui": "게임 내 설정 GUI를 여는 키입니다.", "tweakeroo.config.hotkey.comment.placementRestrictionModeColumn": "설치 제한 모드를 열 모드로 전환합니다.", "tweakeroo.config.hotkey.comment.placementRestrictionModeDiagonal": "설치 제한 모드를 대각 모드로 전환합니다.", "tweakeroo.config.hotkey.comment.placementRestrictionModeFace": "설치 제한 모드를 면 모드로 전환합니다.", "tweakeroo.config.hotkey.comment.placementRestrictionModeLayer": "설치 제한 모드를 레이어 모드로 전환합니다.", "tweakeroo.config.hotkey.comment.placementRestrictionModeLine": "설치 제한 모드를 라인 모드로 전환합니다.", "tweakeroo.config.hotkey.comment.placementRestrictionModePlane": "설치 제한 모드를 평면 모드로 전환합니다.", "tweakeroo.config.hotkey.comment.placementYMirror": "블록 내에서 목표한 y 위치를 대칭하는 키입니다.", "tweakeroo.config.hotkey.comment.playerInventoryPeek": "플레이어 보관함 미리보기 기능을 활성화하는 키입니다.", "tweakeroo.config.hotkey.comment.sitDownNearbyPets": "근처의 모든 애완동물을 앉힙니다.", "tweakeroo.config.hotkey.comment.skipAllRendering": "모든 렌더링을 스킵을 토글합니다.", "tweakeroo.config.hotkey.comment.skipWorldRendering": "월드 렌더링 스킵을 토글합니다.", "tweakeroo.config.hotkey.comment.standUpNearbyPets": "근처의 모든 애완동물을 일으킵니다.", "tweakeroo.config.hotkey.comment.swapElytraChestplate": "상의 슬롯에 현재 장착된 아이템을 겉날개과 흉갑 사이에서 스왑합니다.", "tweakeroo.config.hotkey.comment.toggleAccuratePlacementProtocol": "일반 -> '정확한 설치 프로토콜' 옵션의 값을 토글합니다.", "tweakeroo.config.hotkey.comment.toggleGrabCursor": "현재 상태에 따라 마우스 커서를 그랩하거나 그랩하지 않습니다.", "tweakeroo.config.hotkey.comment.toolPick": "목표 블록에 대해 효과적인 도구로 전환합니다.", "tweakeroo.config.hotkey.comment.writeMapsAsImages": "현재 사용 가능한 모든 지도를 이미지로 'config/tweakeroo/map_images/<worldname>/' 디렉토리에 저장합니다.", "tweakeroo.config.hotkey.comment.zoomActivate": "확대 활성화 단축키입니다.", "tweakeroo.config.hotkey.name.accurateBlockPlacementInto": "정확한 블록 설치 면 향해", "tweakeroo.config.hotkey.name.accurateBlockPlacementReverse": "정확한 블록 설치 역방향", "tweakeroo.config.hotkey.name.areaSelectionAddToList": "영역 선택 리스트에 추가", "tweakeroo.config.hotkey.name.areaSelectionOffset": "영역 선택 오프셋", "tweakeroo.config.hotkey.name.areaSelectionRemoveFromList": "영역 선택 리스트에서 랜덤", "tweakeroo.config.hotkey.name.breakingRestrictionModeColumn": "파괴 제한 모드 열", "tweakeroo.config.hotkey.name.breakingRestrictionModeDiagonal": "파괴 제한 모드 대각", "tweakeroo.config.hotkey.name.breakingRestrictionModeFace": "파괴 제한 모드 면", "tweakeroo.config.hotkey.name.breakingRestrictionModeLayer": "파괴 제한 모드 레이어", "tweakeroo.config.hotkey.name.breakingRestrictionModeLine": "파괴 제한 모드 라인", "tweakeroo.config.hotkey.name.breakingRestrictionModePlane": "파괴 제한 모드 평면", "tweakeroo.config.hotkey.name.copySignText": "복사 표지판 텍스트", "tweakeroo.config.hotkey.name.elytraCamera": "겉날개 카메라", "tweakeroo.config.hotkey.name.flexibleBlockPlacementAdjacent": "유연한 블록 설치 인접", "tweakeroo.config.hotkey.name.flexibleBlockPlacementOffset": "유연한 블록 설치 오프셋", "tweakeroo.config.hotkey.name.flexibleBlockPlacementRotation": "유연한 블록 설치 회전", "tweakeroo.config.hotkey.name.flyIncrement1": "비행 증가량1", "tweakeroo.config.hotkey.name.flyIncrement2": "비행 증가량2", "tweakeroo.config.hotkey.name.flyPreset1": "비행 프리셋1", "tweakeroo.config.hotkey.name.flyPreset2": "비행 프리셋2", "tweakeroo.config.hotkey.name.flyPreset3": "비행 프리셋3", "tweakeroo.config.hotkey.name.flyPreset4": "비행 프리셋4", "tweakeroo.config.hotkey.name.freeCameraPlayerInputs": "자유 카메라 플레이어 입력", "tweakeroo.config.hotkey.name.freeCameraPlayerMovement": "자유 카메라 플레이어 이동", "tweakeroo.config.hotkey.name.hotbarScroll": "단축바 스크롤", "tweakeroo.config.hotkey.name.hotbarSwap1": "단축바 스왑1", "tweakeroo.config.hotkey.name.hotbarSwap2": "단축바 스왑2", "tweakeroo.config.hotkey.name.hotbarSwap3": "단축바 스왑3", "tweakeroo.config.hotkey.name.hotbarSwapBase": "단축바 스왑 기본", "tweakeroo.config.hotkey.name.inventoryPreview": "보관함 미리보기", "tweakeroo.config.hotkey.name.inventoryPreviewToggleScreen": "보관함 미리보기 화면 토글", "tweakeroo.config.hotkey.name.openConfigGui": "설정 GUI 열기", "tweakeroo.config.hotkey.name.placementRestrictionModeColumn": "설치 제한 모드 열", "tweakeroo.config.hotkey.name.placementRestrictionModeDiagonal": "설치 제한 모드 대각", "tweakeroo.config.hotkey.name.placementRestrictionModeFace": "설치 제한 모드 면", "tweakeroo.config.hotkey.name.placementRestrictionModeLayer": "설치 제한 모드 레이어", "tweakeroo.config.hotkey.name.placementRestrictionModeLine": "설치 제한 모드 라인", "tweakeroo.config.hotkey.name.placementRestrictionModePlane": "설치 제한 모드 평면", "tweakeroo.config.hotkey.name.placementYMirror": "설치 Y 대칭", "tweakeroo.config.hotkey.name.playerInventoryPeek": "플레이어 보관함 미리보기", "tweakeroo.config.hotkey.name.sitDownNearbyPets": "근처 반려동물 앉히기", "tweakeroo.config.hotkey.name.skipAllRendering": "스킵 모든 렌더링", "tweakeroo.config.hotkey.name.skipWorldRendering": "스킵 월드 렌더링", "tweakeroo.config.hotkey.name.standUpNearbyPets": "근처 반려동물 일으키기", "tweakeroo.config.hotkey.name.swapElytraChestplate": "스왑 겉날개 흉갑", "tweakeroo.config.hotkey.name.toggleAccuratePlacementProtocol": "토글 정확한 설치 프로토콜", "tweakeroo.config.hotkey.name.toggleGrabCursor": "토글 그랩 커서", "tweakeroo.config.hotkey.name.toolPick": "도구 선택", "tweakeroo.config.hotkey.name.writeMapsAsImages": "지도를 이미지로 저장", "tweakeroo.config.hotkey.name.zoomActivate": "확대 활성화", "tweakeroo.config.internal.comment.darknessScaleValueOriginal": "The original darkness scale value, before the 'tweak darkness visibility' was enabled", "tweakeroo.config.internal.comment.flySpeedPreset": "이것은 모드가 현재 선택된 비행 속도 프리셋을\n확인하기 위해 내부적으로 사용하는 값입니다.", "tweakeroo.config.internal.comment.gammaValueOriginal": "감마 오버라이드가 활성화되기 전의 원래 감마 값입니다.", "tweakeroo.config.internal.comment.hotbarScrollCurrentRow": "이것은 모드가 단축바 스크롤 기능에 대해\n\"현재 단축바 행\"을 확인하기 위해 내부적으로\n사용하는 값입니다.", "tweakeroo.config.internal.comment.slimeBlockSlipperinessOriginal": "슬라임 블록의 원래 미끄러짐 값입니다.", "tweakeroo.config.internal.comment.snapAimLastPitch": "마지막으로 스냅 된 피치 값입니다.", "tweakeroo.config.internal.comment.snapAimLastYaw": "마지막으로 스냅 된 요 값입니다.", "tweakeroo.config.internal.name.darknessScaleValueOriginal": "darknessScaleValueOriginal", "tweakeroo.config.internal.name.flySpeedPreset": "비행 속도 프리셋", "tweakeroo.config.internal.name.gammaValueOriginal": "원래 감마 값", "tweakeroo.config.internal.name.hotbarScrollCurrentRow": "단축바 스크롤 현재 행", "tweakeroo.config.internal.name.slimeBlockSlipperinessOriginal": "슬라임 블록 미끄러짐 원래", "tweakeroo.config.internal.name.snapAimLastPitch": "스냅 조준 마지막 피치", "tweakeroo.config.internal.name.snapAimLastYaw": "스냅 조준 마지막 요", "tweakeroo.config.lists.comment.blockTypeBreakRestrictionBlackList": "블록 파괴 제한 리스트 타입이 블랙리스트로 설정되고, 블록 파괴 제한 트윅이 활성화된 경우,\n파괴가 허용되지 않는 블록들입니다.", "tweakeroo.config.lists.comment.blockTypeBreakRestrictionListType": "블록 타입 파괴 제한 트윅을 위한 제한 리스트 타입입니다.", "tweakeroo.config.lists.comment.blockTypeBreakRestrictionWhiteList": "블록 파괴 제한 리스트 타입이 화이트리스트로 설정되고, 블록 파괴 제한 트윅이 활성화된 경우,\n파괴가 허용되는 블록들입니다.", "tweakeroo.config.lists.comment.creativeExtraItems": "크리에이티브 모드 보관함에 추가되어야 하는 추가 아이템입니다.\n현재 이것들은 운송 카테고리에 포함됩니다.\n미래에는 추가된 아이템의 그룹을 사용자가 지정할 수 있을 것입니다.", "tweakeroo.config.lists.comment.entityTypeAttackRestrictionBlackList": "엔티티 공격 제한 리스트 타입이 블랙리스트로 설정되고, 엔티티 공격 제한 트윅이 활성화된 경우,\n공격이 허용되지 않는 엔티티들입니다.", "tweakeroo.config.lists.comment.entityTypeAttackRestrictionListType": "엔티티 타입 공격 제한 트윅을 위한 제한 리스트 타입입니다.", "tweakeroo.config.lists.comment.entityTypeAttackRestrictionWhiteList": "엔티티 공격 제한 리스트 타입이 화이트리스트로 설정되고, 엔티티 공격 제한 트윅이 활성화된 경우,\n공격이 허용되는 엔티티들입니다.", "tweakeroo.config.lists.comment.entityWeaponMapping": "'트윅 무기 전환' 트윅으로 어떤 무기를\n사용해야 하는지에 대한 매핑입니다.\n따로 매핑이 정의되지 않은 경우 '<default>'가 사용됩니다.\n'<ignore>'는 무기 전환을 작동시키지 않습니다.", "tweakeroo.config.lists.comment.fastPlacementItemBlackList": "빠른 설치 아이템 리스트 타입이 블랙리스트로 설정된 경우,\n빠른 블록 설치 트윅에서 사용이 허용되지 않는 아이템들입니다.", "tweakeroo.config.lists.comment.fastPlacementItemListType": "빠른 블록 설치 트윅을 위한 아이템 제한 타입입니다.", "tweakeroo.config.lists.comment.fastPlacementItemWhiteList": "빠른 설치 아이템 리스트 타입이 화이트리스트로 설정된 경우,\n빠른 블록 설치 트윅에서 사용이 허용되는 아이템들입니다.", "tweakeroo.config.lists.comment.fastRightClickBlackList": "빠른 오른쪽 클릭 리스트 타입이 블랙리스트로 설정된 경우,\n빠른 오른쪽 클릭 트윅에서 사용이 허용되지 않는 아이템들입니다.", "tweakeroo.config.lists.comment.fastRightClickBlockBlackList": "빠른 오른쪽 클릭 블록 리스트 타입이 블랙리스트로 설정된 경우,\n빠른 오른쪽 클릭 트윅에서 오른쪽 클릭이 허용되지 않는 블록들입니다.", "tweakeroo.config.lists.comment.fastRightClickBlockListType": "빠른 오른쪽 클릭 트윅을 위한 목표 블록 제한 타입입니다.", "tweakeroo.config.lists.comment.fastRightClickBlockWhiteList": "빠른 오른쪽 클릭 블록 리스트 타입이 화이트리스트로 설정된 경우,\n빠른 오른쪽 클릭 트윅에서 오른쪽 클릭이 허용되는 블록들입니다.", "tweakeroo.config.lists.comment.fastRightClickListType": "빠른 오른쪽 클릭 트윅을 위한 아이템 제한 타입입니다.", "tweakeroo.config.lists.comment.fastRightClickWhiteList": "빠른 오른쪽 클릭 리스트 타입이 화이트리스트로 설정된 경우,\n빠른 오른쪽 클릭 트윅에서 사용이 허용되지 않는 아이템들입니다.", "tweakeroo.config.lists.comment.flatWorldPresets": "사용자 지정 평지 월드 프리셋 문자열입니다.\n형식은 다음과 같습니다: name;blocks_string;biome;generation_features;icon_item\n블록 문자열 형식은 바닐라 형식입니다. 예시: 62*minecraft:dirt,minecraft:grass\n생물군계는 레지스트리 이름 또는 int ID를 사용할 수 있습니다.\n아이콘 아이템 이름 형식은 minecraft:iron_nugget입니다.", "tweakeroo.config.lists.comment.handRestockBlackList": "손 보충 리스트 타입이 블랙리스트로 설정된 경우,\n손 보충 트윅에서 보충이 허용되지 않는 아이템들입니다.", "tweakeroo.config.lists.comment.handRestockListType": "손 보충 트윅을 위한 제한 리스트 타입입니다.", "tweakeroo.config.lists.comment.handRestockWhiteList": "손 보충 리스트 타입이 화이트리스트로 설정된 경우,\n손 보충 트윅에서 보충이 허용되는 아이템들입니다.", "tweakeroo.config.lists.comment.potionWarningBlackList": "효과 소멸이 경고 되지 않는 물약 효과들입니다.", "tweakeroo.config.lists.comment.potionWarningListType": "물약 경고 효과를 위한 리스트 타입입니다.", "tweakeroo.config.lists.comment.potionWarningWhiteList": "효과 소멸이 경고 되는 물약 효과들입니다.", "tweakeroo.config.lists.comment.silkTouchOverride": "When using tweak tool switch, add blocks or\nblock tags to this list to apply SilkTouchFirst to,\nin addition to using the '§b#malilib:needs_silk_touch§r' Block Tag\nbased 'toolSwapSilkTouchFirst' method when\n'toolSwapSilkTouchOverride' is enabled.\n\nA useful example, would be adding\n'§bminecraft:stone§r' to this list.\n\nThe Block Tag exists, so that you don't need\nto add dozens of blocks to a list like this.\nBut; You can configure this list to work this way\nby disabling 'toolSwapSilkTouchFirst' and only enabling\n`toolSwapSilkTouchOverride` instead.\nYou may then want to make use of some of\nthe existing `§b#malilib:§r` or '§b#minecraft:§r' block tags here;\nsuch as adding '§b#malilib:glass_blocks§r', for example\ninstead of typing in all 18 glass blocks one by one.", "tweakeroo.config.lists.comment.repairModeSlots": "수리 모드가 사용해야 하는 슬롯입니다.\n유효한 값: mainhand, offhand, head, chest, legs, feet", "tweakeroo.config.lists.comment.selectiveBlocksBlacklist": "블랙리스트 할 블록 위치들입니다.\nAndrew54757가 TweakFork에서 작성했습니다.", "tweakeroo.config.lists.comment.selectiveBlocksListType": "선택적 블록 트윅을 위한 리스트 타입입니다.\nAndrew54757가 TweakFork에서 작성했습니다.", "tweakeroo.config.lists.comment.selectiveBlocksWhitelist": "화이트리스트 할 블록 위치들입니다.\nAndrew54757가 TweakFork에서 작성했습니다.", "tweakeroo.config.lists.comment.unstackingItems": "트윅 아이템 언스택 보호' 트윅에\n적용되는 아이템들입니다.", "tweakeroo.config.lists.name.blockTypeBreakRestrictionBlackList": "블록 타입 파괴 제한 블랙리스트", "tweakeroo.config.lists.name.blockTypeBreakRestrictionListType": "블록 타입 파괴 제한 리스트 타입", "tweakeroo.config.lists.name.blockTypeBreakRestrictionWhiteList": "블록 타입 파괴 제한 화이트리스트", "tweakeroo.config.lists.name.creativeExtraItems": "크리에이티브 추가 아이템", "tweakeroo.config.lists.name.entityTypeAttackRestrictionBlackList": "엔티티 타입 공격 제한 블랙리스트", "tweakeroo.config.lists.name.entityTypeAttackRestrictionListType": "엔티티 타입 공격 제한 리스트 타입", "tweakeroo.config.lists.name.entityTypeAttackRestrictionWhiteList": "엔티티 타입 공격 제한 화이트리스트", "tweakeroo.config.lists.name.entityWeaponMapping": "엔티티 무기 매핑", "tweakeroo.config.lists.name.fastPlacementItemBlackList": "빠른 설치 아이템 블랙리스트", "tweakeroo.config.lists.name.fastPlacementItemListType": "빠른 설치 아이템 리스트 타입", "tweakeroo.config.lists.name.fastPlacementItemWhiteList": "빠른 설치 아이템 화이트리스트", "tweakeroo.config.lists.name.fastRightClickBlackList": "빠른 오른쪽 클릭 블랙리스트", "tweakeroo.config.lists.name.fastRightClickBlockBlackList": "빠른 오른쪽 클릭 블록 블랙리스트", "tweakeroo.config.lists.name.fastRightClickBlockListType": "빠른 오른쪽 클릭 블록 리스트 타입", "tweakeroo.config.lists.name.fastRightClickBlockWhiteList": "빠른 오른쪽 클릭 블록 화이트리스트", "tweakeroo.config.lists.name.fastRightClickListType": "빠른 오른쪽 클릭 리스트 타입", "tweakeroo.config.lists.name.fastRightClickWhiteList": "빠른 오른쪽 클릭 화이트리스트", "tweakeroo.config.lists.name.flatWorldPresets": "평지 월드 프리셋", "tweakeroo.config.lists.name.handRestockBlackList": "손 보충 블랙리스트", "tweakeroo.config.lists.name.handRestockListType": "손 보충 리스트 타입", "tweakeroo.config.lists.name.handRestockWhiteList": "손 보충 화이트리스트", "tweakeroo.config.lists.name.potionWarningBlackList": "물약 경고 블랙리스트", "tweakeroo.config.lists.name.potionWarningListType": "물약 경고 리스트 타입", "tweakeroo.config.lists.name.potionWarningWhiteList": "물약 경고 화이트리스트", "tweakeroo.config.lists.name.silkTouchOverride": "silkTouchOverride", "tweakeroo.config.lists.name.repairModeSlots": "수리 모드 슬롯", "tweakeroo.config.lists.name.selectiveBlocksBlacklist": "선택적 블록 블랙리스트", "tweakeroo.config.lists.name.selectiveBlocksListType": "선택적 블록 리스트 타입", "tweakeroo.config.lists.name.selectiveBlocksWhitelist": "선택적 블록 화이트리스트", "tweakeroo.config.lists.name.unstackingItems": "아이템 언스택", "tweakeroo.gui.button.config_gui.disables": "이츠", "tweakeroo.gui.button.config_gui.fixes": "개선", "tweakeroo.gui.button.config_gui.generic": "일반", "tweakeroo.gui.button.config_gui.generic_hotkeys": "단축키", "tweakeroo.gui.button.config_gui.generic_config_hotkeys": "일반 단축키", "tweakeroo.gui.button.config_gui.lists": "목록", "tweakeroo.gui.button.config_gui.placement": "설치 관련", "tweakeroo.gui.button.config_gui.tweaks": "트윅", "tweakeroo.gui.button.misc.command_block.hover.update_execution": "게임 틱당 여러 트리거가 허용되는지 여부", "tweakeroo.gui.button.misc.command_block.set_name": "이름 설정", "tweakeroo.gui.button.misc.command_block.update_execution.looping": "루프 중", "tweakeroo.gui.button.misc.command_block.update_execution.off": "루프: §cOFF", "tweakeroo.gui.button.misc.command_block.update_execution.on": "루프: §aON", "tweakeroo.gui.label.easy_place_protocol.auto": "자동", "tweakeroo.gui.label.easy_place_protocol.none": "없음", "tweakeroo.gui.label.easy_place_protocol.slabs_only": "반블록 전용", "tweakeroo.gui.label.easy_place_protocol.v2": "버전 2", "tweakeroo.gui.label.easy_place_protocol.v3": "버전 3", "tweakeroo.gui.title.configs": "트위커루 설정 - %s", "tweakeroo.hotkeys.category.disable_toggle_hotkeys": "토글 단축키 비활성화", "tweakeroo.hotkeys.category.generic_hotkeys": "일반 단축키", "tweakeroo.hotkeys.category.tweak_toggle_hotkeys": "토글 단축키 트윅", "tweakeroo.label.config_comment.single_player_only": "참고: 이 기능은 싱글 플레이어에서만 완전히 작동합니다.", "tweakeroo.label.placement_restriction_mode.column": "열", "tweakeroo.label.placement_restriction_mode.diagonal": "대각", "tweakeroo.label.placement_restriction_mode.face": "면", "tweakeroo.label.placement_restriction_mode.layer": "레이어", "tweakeroo.label.placement_restriction_mode.line": "라인", "tweakeroo.label.placement_restriction_mode.plane": "평면", "tweakeroo.label.snap_aim_mode.both": "요 & 피치", "tweakeroo.label.snap_aim_mode.pitch": "피치", "tweakeroo.label.snap_aim_mode.yaw": "요", "tweakeroo.message.death_coordinates": "§6당신은 죽었습니다.§r @ [§b%d, %d, %d§r] in §a%s", "tweakeroo.message.focusing_game": "게임에 §6포커싱§f 합니다. (커서 그랩)", "tweakeroo.message.potion_effects_running_out": "§6!! 경고 - %s 포션 효과(들)이 %s 초 후에 소멸됩니다 !!§r", "tweakeroo.message.repair_mode.swapped_repairable_item_to_slot": "수리 가능한 아이템이 %s 슬롯으로 스왑되었습니다.", "tweakeroo.message.set_after_clicker_count_to": "클리커 이후 카운트를 %s(으)로 설정합니다.", "tweakeroo.message.set_breaking_grid_size_to": "파괴 격자 크기를 %s(으)로 설정합니다.", "tweakeroo.message.set_breaking_restriction_mode_to": "파괴 제한 모드를 %s(으)로 설정합니다.", "tweakeroo.message.set_fly_speed_preset_to": "비행 속도 프리셋 %s(으)로 전환합니다. (속도: %s)", "tweakeroo.message.set_fly_speed_to": "비행 속도 프리셋 %s를 %s(으)로 설정합니다.", "tweakeroo.message.set_hotbar_slot_cycle_max_to": "단축바 슬롯 사이클 최대 슬롯을 %s(으)로 설정합니다.", "tweakeroo.message.set_hotbar_slot_randomizer_max_to": "단축바 슬롯 랜덤화 최대 슬롯을 %s(으)로 설정합니다.", "tweakeroo.message.set_periodic_attack_interval_to": "반복 공격 간격을 %s(으)로 설정합니다.", "tweakeroo.message.set_periodic_hold_attack_interval_to": "반복 공격 누르기 간격을 %s(으)로 설정합니다.", "tweakeroo.message.set_periodic_hold_use_interval_to": "반복 사용 누르기 간격을 %s(으)로 설정합니다.", "tweakeroo.message.set_periodic_use_interval_to": "반복 사용 간격을 %s(으)로 설정합니다.", "tweakeroo.message.set_placement_grid_size_to": "설치 격자 크기를 %s(으)로 설정합니다.", "tweakeroo.message.set_placement_limit_to": "설치 한계를 %s(으)로 설정합니다.", "tweakeroo.message.set_placement_restriction_mode_to": "설치 제한 모드를 %s(으)로 설정합니다.", "tweakeroo.message.set_snap_aim_pitch_step_to": "스냅 조준 피치 증감폭을 %s(으)로 설정합니다.", "tweakeroo.message.set_snap_aim_yaw_step_to": "스냅 조준 요 증감폭을 %s(으)로 설정합니다.", "tweakeroo.message.set_zoom_fov_to": "카메라 확대 시야 범위를 %s(으)로 설정합니다.", "tweakeroo.message.sign_text_copied": "표지판 텍스트가 복사 되었습니다.", "tweakeroo.message.snapped_to_pitch": "피치가 %s(으)로 스냅 되었습니다.", "tweakeroo.message.snapped_to_yaw": "요가 %s(으)로 스냅 되었습니다.", "tweakeroo.message.swapped_low_durability_item_for_better_durability": "내구도가 낮은 아이템을 새 아이템으로 스왑 했습니다.", "tweakeroo.message.swapped_low_durability_item_for_dummy_item": "내구도가 낮은 아이템을 더미 아이템으로 스왑 했습니다.", "tweakeroo.message.swapped_low_durability_item_off_players_hand": "내구도가 낮은 아이템을 플레이어의 손 밖으로 스왑 했습니다.", "tweakeroo.message.toggled": "%s %s 토글 되었습니다.", "tweakeroo.message.toggled_after_clicker_on": "클리커 이후 트윅이 토글 되었습니다. %s, 클릭: %s", "tweakeroo.message.toggled_breaking_grid_on": "파괴 격자 트윅이 토글 되었습니다. %s, 격자 간격: %s", "tweakeroo.message.toggled_fast_placement_mode_on": "빠른 설치 모드가 토글 되었습니다. %s, 모드: %s", "tweakeroo.message.toggled_fly_speed_on": "비행 속도 트윅이 토글 되었습니다. %s, 프리셋: %s, 속도: %s", "tweakeroo.message.toggled_periodic": "토글 되었습니다. %s %s, 속도: %s", "tweakeroo.message.toggled_placement_grid_on": "설치 격자 트윅이 토글 되었습니다. %s, 격자 간격: %s", "tweakeroo.message.toggled_placement_limit_on": "설치 한계 트윅이 토글 되었습니다. %s, 한계: %s", "tweakeroo.message.toggled_slot_cycle_on": "단축바 슬롯 사이클 트윅이 토글 되었습니다. %s, 최대 슬롯: %s", "tweakeroo.message.toggled_slot_randomizer_on": "단축바 슬롯 랜덤화 트윅이 토글 되었습니다. %s, 최대 슬롯: %s", "tweakeroo.message.toggled_snap_aim_on_both": "요 & 피치 스냅 조준이 토글 되었습니다. %s, 요 증감폭: %s, 피치 증감폭:%s", "tweakeroo.message.toggled_snap_aim_on_pitch": "피치 스냅 조준이 토글 되었습니다. %s, 피치 증감폭 크기: %s도", "tweakeroo.message.toggled_snap_aim_on_yaw": "요 스냅 조준이 토글 되었습니다. %s, 요 증감폭 크기: %s도", "tweakeroo.message.toggled_zoom_activate_off": "카메라 확대가 비활성화 되었습니다. 시야 범위: %s", "tweakeroo.message.toggled_zoom_activate_on": "카메라 확대가 활성화 되었습니다. 시야 범위: %s", "tweakeroo.message.toggled_zoom_on": "카메라 확대가 토글 되었습니다. %s, 시야 범위: %s", "tweakeroo.message.unfocusing_game": "게임에 §6포커싱 해제§f합니다. (커서 그랩 해제)", "tweakeroo.message.value.off": "OFF", "tweakeroo.message.value.on": "ON", "tweakeroo.message.warning.block_type_break_restriction": "§6블록 파괴가 블록 타입 파괴 제한 트윅에 의해 방지되었습니다.", "tweakeroo.message.warning.entity_type_attack_restriction": "§6엔티티 공격이 엔티티 타입 공격 제한 트윅에 의해 방지되었습니다."}