{"modmenu.descriptionTranslation.tweakeroo": "添加了一些可配置的客户端开关", "tweakeroo.config.disable.comment.disableArmorStandRendering": "禁用所有盔甲架的渲染", "tweakeroo.config.disable.comment.disableAtmosphericFog": "禁用玩家与渲染距离雾效之间存在的所有大气雾效。", "tweakeroo.config.disable.comment.disableAxeStripping": "禁用使用斧头剥离原木等方块", "tweakeroo.config.disable.comment.disableBatSpawning": "在单人游戏中禁用蝙蝠生成", "tweakeroo.config.disable.comment.disableBeaconBeamRendering": "移除信标的光束渲染效果", "tweakeroo.config.disable.comment.disableBlockBreakCooldown": "禁用方块破坏间隔冷却时间", "tweakeroo.config.disable.comment.disableBlockBreakingParticles": "移除方块破坏时的粒子效果\n（此功能最初来自Nessie的usefulmod）", "tweakeroo.config.disable.comment.disableBossBar": "禁用Boss血条显示", "tweakeroo.config.disable.comment.disableBossFog": "移除Boss生物生成的雾效", "tweakeroo.config.disable.comment.disableChunkRendering": "停止区块的（重）渲染。此功能启用期间任何方块变化将不可见，\n需关闭后使用F3 + A刷新世界渲染。\n在存在大量无关紧要的方块变化场景中，可能有助于提升帧率。", "tweakeroo.config.disable.comment.disableClientEntityUpdates": "在客户端禁用除玩家外的所有实体更新。\n主要用于需要处理大量实体导致性能问题时临时修复。", "tweakeroo.config.disable.comment.disableClientLightUpdates": "禁用所有客户端光照更新", "tweakeroo.config.disable.comment.disableConstantChunkSaving": "禁用游戏每刻自动保存最多20个区块的功能\n（不影响常规自动保存周期）", "tweakeroo.config.disable.comment.disableCreativeMenuInfestedBlocks": "从创造模式搜索栏移除虫蚀方块", "tweakeroo.config.disable.comment.disableDeadMobRendering": "阻止渲染已死亡生物（生命值为0的实体）", "tweakeroo.config.disable.comment.disableDeadMobTargeting": "阻止目标选择到已死亡实体\n避免攻击已死亡生物的问题", "tweakeroo.config.disable.comment.disableDoubleTapSprint": "禁用双击前进键触发疾跑", "tweakeroo.config.disable.comment.disableEntityRendering": "禁用除玩家外的所有实体渲染\n主要用于需要处理大量实体导致性能问题时临时修复", "tweakeroo.config.disable.comment.disableEntityTicking": "阻止除玩家外所有实体的更新", "tweakeroo.config.disable.comment.disableFallingBlockEntityRendering": "启用后，下落方块实体将完全不可见", "tweakeroo.config.disable.comment.disableFirstPersonEffectParticles": "移除第一人称视角的药水效果粒子/漩涡特效\n（来自玩家自身的效果）", "tweakeroo.config.disable.comment.disableInventoryEffectRendering": "移除背包界面中的药水效果显示", "tweakeroo.config.disable.comment.disableItemSwitchRenderCooldown": "启用后，切换手持物品或使用物品时\n将不显示冷却/装备动画", "tweakeroo.config.disable.comment.disableMobSpawnerMobRendering": "移除刷怪笼中的生物模型渲染", "tweakeroo.config.disable.comment.disableNauseaEffect": "禁用恶心效果的视觉扭曲", "tweakeroo.config.disable.comment.disableNetherFog": "移除下界维度中的雾效", "tweakeroo.config.disable.comment.disableNetherPortalSound": "禁用下界传送门的嗡鸣声", "tweakeroo.config.disable.comment.disableObserver": "完全禁用侦测器的触发功能", "tweakeroo.config.disable.comment.disableOffhandRendering": "禁用副手物品的渲染显示", "tweakeroo.config.disable.comment.disableParticles": "禁用所有粒子效果显示", "tweakeroo.config.disable.comment.disablePortalGuiClosing": "启用后，身处下界传送门时仍可打开各类界面", "tweakeroo.config.disable.comment.disableRainEffects": "禁用降雨的视觉效果和音效", "tweakeroo.config.disable.comment.disableRenderDistanceFog": "禁用渲染距离边缘的雾效渐变", "tweakeroo.config.disable.comment.disableRenderingScaffolding": "禁用脚手架方块的渲染", "tweakeroo.config.disable.comment.disableScoreboardRendering": "移除侧边栏计分板的显示", "tweakeroo.config.disable.comment.disableShovelPathing": "禁用使用铲子将草方块转换为小径方块", "tweakeroo.config.disable.comment.disableShulkerBoxTooltip": "禁用潜影盒的原版物品栏提示文本", "tweakeroo.config.disable.comment.disableSignGui": "阻止打开告示牌编辑界面", "tweakeroo.config.disable.comment.disableSkyDarkness": "禁用y=63以下的天空暗化效果\n（通过将阈值调整为世界底部以下2格实现）", "tweakeroo.config.disable.comment.disableSlimeBlockSlowdown": "移除在粘液块上行走的减速效果\n（此功能最初来自Nessie的usefulmod）", "tweakeroo.config.disable.comment.disableStatusEffectHud": "禁用状态效果HUD显示（通常位于屏幕右上角）", "tweakeroo.config.disable.comment.disableTileEntityRendering": "阻止所有方块实体的渲染器工作", "tweakeroo.config.disable.comment.disableTileEntityTicking": "阻止所有方块实体的更新", "tweakeroo.config.disable.comment.disableVillagerTradeLocking": "通过同步增加配方使用次数与最大使用次数，\n防止村民交易被永久锁定", "tweakeroo.config.disable.comment.disableWallUnsprint": "触碰墙壁时不会退出疾跑状态", "tweakeroo.config.disable.comment.disableWorldViewBob": "禁用世界视角的晃动效果（不影响手持物品晃动）\n注意：安装Iris模组时此设置可能失效", "tweakeroo.config.disable.name.disableArmorStandRendering": "禁用 - 盔甲架渲染", "tweakeroo.config.disable.name.disableAtmosphericFog": "禁用 - 大气雾效", "tweakeroo.config.disable.name.disableAxeStripping": "禁用 - 斧头去皮", "tweakeroo.config.disable.name.disableBatSpawning": "禁用 - 蝙蝠生成", "tweakeroo.config.disable.name.disableBeaconBeamRendering": "禁用 - 信标光束渲染", "tweakeroo.config.disable.name.disableBlockBreakCooldown": "禁用 - 方块破坏冷却", "tweakeroo.config.disable.name.disableBlockBreakingParticles": "禁用 - 方块破坏粒子", "tweakeroo.config.disable.name.disableBossBar": "禁用 - Boss血条", "tweakeroo.config.disable.name.disableBossFog": "禁用 - Boss雾效", "tweakeroo.config.disable.name.disableChunkRendering": "禁用 - 区块渲染", "tweakeroo.config.disable.name.disableClientEntityUpdates": "禁用 - 客户端实体更新", "tweakeroo.config.disable.name.disableClientLightUpdates": "禁用 - 客户端光照更新", "tweakeroo.config.disable.name.disableConstantChunkSaving": "禁用 - 持续区块保存", "tweakeroo.config.disable.name.disableCreativeMenuInfestedBlocks": "创造菜单移除虫蚀方块", "tweakeroo.config.disable.name.disableDeadMobRendering": "禁用 - 死亡生物渲染", "tweakeroo.config.disable.name.disableDeadMobTargeting": "禁用 - 死亡生物目标选择", "tweakeroo.config.disable.name.disableDoubleTapSprint": "禁用 - 双击前进疾跑", "tweakeroo.config.disable.name.disableEntityRendering": "禁用 - 实体渲染", "tweakeroo.config.disable.name.disableEntityTicking": "禁用 - 实体更新", "tweakeroo.config.disable.name.disableFallingBlockEntityRendering": "禁用 - 下落方块渲染", "tweakeroo.config.disable.name.disableFirstPersonEffectParticles": "禁用 - 第一人称药水粒子", "tweakeroo.config.disable.name.disableInventoryEffectRendering": "禁用 - 背包药水效果显示", "tweakeroo.config.disable.name.disableItemSwitchRenderCooldown": "禁用 - 物品切换动画", "tweakeroo.config.disable.name.disableMobSpawnerMobRendering": "禁用 - 刷怪笼生物渲染", "tweakeroo.config.disable.name.disableNauseaEffect": "禁用 - 恶心效果", "tweakeroo.config.disable.name.disableNetherFog": "禁用 - 下界雾效", "tweakeroo.config.disable.name.disableNetherPortalSound": "禁用 - 下界传送门音效", "tweakeroo.config.disable.name.disableObserver": "禁用 - 侦测器触发", "tweakeroo.config.disable.name.disableOffhandRendering": "禁用 - 副手物品渲染", "tweakeroo.config.disable.name.disableParticles": "禁用 - 所有粒子效果", "tweakeroo.config.disable.name.disablePortalGuiClosing": "禁用 - 传送门关闭界面", "tweakeroo.config.disable.name.disableRainEffects": "禁用 - 降雨效果", "tweakeroo.config.disable.name.disableRenderDistanceFog": "禁用 - 渲染距离雾效", "tweakeroo.config.disable.name.disableRenderingScaffolding": "禁用 - 脚手架渲染", "tweakeroo.config.disable.name.disableScoreboardRendering": "禁用 - 计分板显示", "tweakeroo.config.disable.name.disableShovelPathing": "禁用 - 铲子铺路", "tweakeroo.config.disable.name.disableShulkerBoxTooltip": "禁用 - 潜影盒原版提示", "tweakeroo.config.disable.name.disableSignGui": "禁用 - 告示牌界面", "tweakeroo.config.disable.name.disableSkyDarkness": "禁用 - 天空暗化", "tweakeroo.config.disable.name.disableSlimeBlockSlowdown": "禁用 - 粘液块减速", "tweakeroo.config.disable.name.disableStatusEffectHud": "禁用 - 状态效果HUD", "tweakeroo.config.disable.name.disableTileEntityRendering": "禁用 - 方块实体渲染", "tweakeroo.config.disable.name.disableTileEntityTicking": "禁用 - 方块实体更新", "tweakeroo.config.disable.name.disableVillagerTradeLocking": "禁用 - 村民交易锁定", "tweakeroo.config.disable.name.disableWallUnsprint": "禁用 - 碰墙停止疾跑", "tweakeroo.config.disable.name.disableWorldViewBob": "禁用 - 视角晃动", "tweakeroo.config.disable.prettyName.disableArmorStandRendering": "禁用 - 盔甲架渲染", "tweakeroo.config.disable.prettyName.disableAtmosphericFog": "禁用 - 大气雾效", "tweakeroo.config.disable.prettyName.disableAxeStripping": "禁用 - 斧头去皮", "tweakeroo.config.disable.prettyName.disableBatSpawning": "禁用 - 蝙蝠生成", "tweakeroo.config.disable.prettyName.disableBeaconBeamRendering": "禁用 - 信标光束渲染", "tweakeroo.config.disable.prettyName.disableBlockBreakCooldown": "禁用 - 方块破坏冷却", "tweakeroo.config.disable.prettyName.disableBlockBreakingParticles": "禁用 - 方块破坏粒子", "tweakeroo.config.disable.prettyName.disableBossBar": "禁用 - Boss血条", "tweakeroo.config.disable.prettyName.disableBossFog": "禁用 - Boss雾效", "tweakeroo.config.disable.prettyName.disableChunkRendering": "禁用 - 区块渲染", "tweakeroo.config.disable.prettyName.disableClientEntityUpdates": "禁用 - 客户端实体更新", "tweakeroo.config.disable.prettyName.disableClientLightUpdates": "禁用 - 客户端光照更新", "tweakeroo.config.disable.prettyName.disableConstantChunkSaving": "禁用 - 持续区块保存", "tweakeroo.config.disable.prettyName.disableCreativeMenuInfestedBlocks": "创造菜单移除虫蚀方块", "tweakeroo.config.disable.prettyName.disableDeadMobRendering": "禁用 - 死亡生物渲染", "tweakeroo.config.disable.prettyName.disableDeadMobTargeting": "禁用 - 死亡生物目标选择", "tweakeroo.config.disable.prettyName.disableDoubleTapSprint": "禁用 - 双击疾跑", "tweakeroo.config.disable.prettyName.disableEntityRendering": "禁用 - 实体渲染", "tweakeroo.config.disable.prettyName.disableEntityTicking": "禁用 - 实体更新", "tweakeroo.config.disable.prettyName.disableFallingBlockEntityRendering": "禁用 - 下落方块渲染", "tweakeroo.config.disable.prettyName.disableFirstPersonEffectParticles": "禁用 - 第一人称药水粒子", "tweakeroo.config.disable.prettyName.disableInventoryEffectRendering": "禁用 - 背包药水效果显示", "tweakeroo.config.disable.prettyName.disableItemSwitchRenderCooldown": "禁用 - 物品切换动画", "tweakeroo.config.disable.prettyName.disableMobSpawnerMobRendering": "禁用 - 刷怪笼生物渲染", "tweakeroo.config.disable.prettyName.disableNauseaEffect": "禁用 - 恶心效果", "tweakeroo.config.disable.prettyName.disableNetherFog": "禁用 - 下界雾效", "tweakeroo.config.disable.prettyName.disableNetherPortalSound": "禁用 - 下界传送门音效", "tweakeroo.config.disable.prettyName.disableObserver": "禁用 - 侦测器触发", "tweakeroo.config.disable.prettyName.disableOffhandRendering": "禁用 - 副手物品渲染", "tweakeroo.config.disable.prettyName.disableParticles": "禁用 - 所有粒子效果", "tweakeroo.config.disable.prettyName.disablePortalGuiClosing": "禁用 - 传送门关闭界面", "tweakeroo.config.disable.prettyName.disableRainEffects": "禁用 - 降雨效果", "tweakeroo.config.disable.prettyName.disableRenderDistanceFog": "禁用 - 渲染距离雾效", "tweakeroo.config.disable.prettyName.disableRenderingScaffolding": "禁用 - 脚手架渲染", "tweakeroo.config.disable.prettyName.disableScoreboardRendering": "禁用 - 计分板显示", "tweakeroo.config.disable.prettyName.disableShovelPathing": "禁用 - 铲子铺路", "tweakeroo.config.disable.prettyName.disableShulkerBoxTooltip": "禁用 - 潜影盒原版提示", "tweakeroo.config.disable.prettyName.disableSignGui": "禁用 - 告示牌界面", "tweakeroo.config.disable.prettyName.disableSkyDarkness": "禁用 - 天空暗化", "tweakeroo.config.disable.prettyName.disableSlimeBlockSlowdown": "禁用 - 粘液块减速", "tweakeroo.config.disable.prettyName.disableStatusEffectHud": "禁用 - 状态效果HUD", "tweakeroo.config.disable.prettyName.disableTileEntityRendering": "禁用 - 方块实体渲染", "tweakeroo.config.disable.prettyName.disableTileEntityTicking": "禁用 - 方块实体更新", "tweakeroo.config.disable.prettyName.disableVillagerTradeLocking": "禁用 - 村民交易锁定", "tweakeroo.config.disable.prettyName.disableWallUnsprint": "禁用 - 碰墙停止疾跑", "tweakeroo.config.disable.prettyName.disableWorldViewBob": "禁用 - 视角晃动", "tweakeroo.config.feature_toggle.comment.tweakAccurateBlockPlacement": "启用类似Carpet模组的简化版灵活放置功能\n允许将方块朝向点击面的内部或外部放置", "tweakeroo.config.feature_toggle.comment.tweakAfterClicker": "启用「后置点击器」功能，在放置方块后自动执行右键点击\n适用于快速调整中继器等方块属性\n按住功能切换键时滚动鼠标可快速调整参数", "tweakeroo.config.feature_toggle.comment.tweakAimLock": "锁定当前视角的偏航角与俯仰角\n与精准视角锁定不同，此功能可自由锁定任意角度", "tweakeroo.config.feature_toggle.comment.tweakAngelBlock": "启用“浮空放置方块(Angel<PERSON>lock)”功能，允许在创造模式中空中放置方块\n基于「Flotato」技术实现", "tweakeroo.config.feature_toggle.comment.tweakAreaSelector": "启用区域选择器\n由 Andrew54757 在 TweakFork 下编写", "tweakeroo.config.feature_toggle.comment.tweakAutoSwitchElytra": "坠落时自动装备鞘翅，着陆后恢复原胸甲装备", "tweakeroo.config.feature_toggle.comment.tweakBlockReachOverride": "修改原版方块触及距离为「通用」中的“方块触及距离(blockReachDistance)”数值", "tweakeroo.config.feature_toggle.comment.tweakBlockTypeBreakRestriction": "限制可破坏的方块类型\n具体配置见「列表」的“方块破坏限制(blockBreakRestriction)”设置项", "tweakeroo.config.feature_toggle.comment.tweakBreakingGrid": "启用网格化限制破坏模式，按设定间隔破坏方块\n按住功能切换键时滚动鼠标可快速调整网格间隔", "tweakeroo.config.feature_toggle.comment.tweakBreakingRestriction": "启用破坏限制模式（平面/层状/点击面/列/十字/对角线）\n在按住攻击键时仅允许按指定模式破坏方块", "tweakeroo.config.feature_toggle.comment.tweakBundleDisplay": "按住Shift键查看收纳袋时，显示其中存放物品的实时预览（支持潜影盒、收纳袋等容器）", "tweakeroo.config.feature_toggle.comment.tweakChatBackgroundColor": "使用「通用」中的chatBackgroundColor值修改聊天背景色", "tweakeroo.config.feature_toggle.comment.tweakChatPersistentText": "保存聊天输入框内容并在下次打开时自动恢复", "tweakeroo.config.feature_toggle.comment.tweakChatTimestamp": "为聊天消息添加时间戳标记", "tweakeroo.config.feature_toggle.comment.tweakCommandBlockExtraFields": "在命令方块界面添加额外字段\n可设置命令方块名称及查看统计结果", "tweakeroo.config.feature_toggle.comment.tweakCreativeExtraItems": "向创造模式物品栏添加自定义物品\n通过「列表」-> 创造模式额外物品(creativeExtraItems) 配置添加项\n注：当前物品将添加至运输类（物品最少的分组）\n未来将支持按物品自定义分组", "tweakeroo.config.feature_toggle.comment.tweakCustomFlatPresets": "允许添加自定义超平坦世界预设\n预设定义见「列表」-> flatWorldPresets 配置项", "tweakeroo.config.feature_toggle.comment.tweakCustomFlyDeceleration": "自定义创造/旁观模式的飞行减速参数\n主要用于实现更快的减速响应（减少滑行）\n详见「通用」 -> 自定义飞行减速(flyDeceleration)", "tweakeroo.config.feature_toggle.comment.tweakCustomInventoryScreenScale": "为所有容器界面使用自定义缩放比例\n缩放值见「通用」-> 自定义背包界面缩放(customInventoryGuiScale)", "tweakeroo.config.feature_toggle.comment.tweakDarknessVisibility": "如果启用，此选项将在受到黑暗状态效果影响时提高能见度。", "tweakeroo.config.feature_toggle.comment.tweakElytraCamera": "启用鞘翅视角分离功能，按住激活键时锁定实际视角\n仅通过输入设备控制独立渲染视角\n专为鞘翅飞行时自由观察设计", "tweakeroo.config.feature_toggle.comment.tweakEmptyShulkerBoxesStack": "允许空潜影盒堆叠至64个\n§c警告：会导致服务器不同步问题（需服务端同步支持）\n§c单机模式将永久改变潜影盒系统行为", "tweakeroo.config.feature_toggle.comment.tweakEntityReachOverride": "修改原版实体触及距离为「通用」中的“实体触及距离(blockReachDistance)”值", "tweakeroo.config.feature_toggle.comment.tweakEntityTypeAttackRestriction": "限制可攻击的实体类型\n具体配置见「列表」的“实体攻击限制(EntityTypeAttackRestriction)”相关的设置项", "tweakeroo.config.feature_toggle.comment.tweakExplosionReducedParticles": "所有爆炸粒子将统一使用'EXPLOSION_NORMAL'类型", "tweakeroo.config.feature_toggle.comment.tweakF3Cursor": "始终显示F3调试界面的光标坐标", "tweakeroo.config.feature_toggle.comment.tweakFakeSneakPlacement": "此调整将点击位置偏移至实际点击方块的相邻空气方块。\n这使您无需潜行即可在具有点击操作的方块（如打开物品栏GUI）旁放置方块。\n请注意这并非真正模拟潜行，只是视觉效果类似。", "tweakeroo.config.feature_toggle.comment.tweakFakeSneaking": "模拟潜行状态边缘防坠落\n不降低移动速度", "tweakeroo.config.feature_toggle.comment.tweakFastBlockPlacement": "启用快速方块放置功能\n光标移动至新方块时自动快速放置", "tweakeroo.config.feature_toggle.comment.tweakFastLeftClick": "启用自动左键连点功能\n连点频率见「通用」", "tweakeroo.config.feature_toggle.comment.tweakFastRightClick": "启用自动右键连点功能\n连点频率见「通用」", "tweakeroo.config.feature_toggle.comment.tweakFillCloneLimit": "在单机模式覆盖 fill 和 clone 命令的方块数量限制\n限制值见「通用」-> fillCloneLimit", "tweakeroo.config.feature_toggle.comment.tweakFlexibleBlockPlacement": "启用灵活方块放置功能\n配合快捷键调整朝向与偏移位置", "tweakeroo.config.feature_toggle.comment.tweakFlySpeed": "覆盖创造/旁观模式的飞行速度\n支持使用预设方案", "tweakeroo.config.feature_toggle.comment.tweakFreeCamera": "启用类旁观模式的自由视角，玩家本体保持在激活位置，像是灵魂出窍了一样", "tweakeroo.config.feature_toggle.comment.tweakGammaOverride": "使用「通用」中的值修改原版设置的伽马值", "tweakeroo.config.feature_toggle.comment.tweakHandRestock": "主/副手持物品耗尽时自动补充新堆叠", "tweakeroo.config.feature_toggle.comment.tweakHangableEntityBypass": "绕过悬挂实体（物品展示框/画）的交互限制\n通过「通用」-> hangableEntityBypassInverse控制潜行状态条件", "tweakeroo.config.feature_toggle.comment.tweakHoldAttack": "模拟持续按住攻击键状态", "tweakeroo.config.feature_toggle.comment.tweakHoldUse": "模拟持续按住使用键状态", "tweakeroo.config.feature_toggle.comment.tweakHotbarScroll": "启用通过滚轮切换快捷栏功能", "tweakeroo.config.feature_toggle.comment.tweakHotbarSlotCycle": "启用快捷栏循环切换功能\n放置方块后按设定最大槽位数循环\n按住功能切换键时滚动鼠标可调整参数", "tweakeroo.config.feature_toggle.comment.tweakHotbarSlotRandomizer": "启用快捷栏随机切换功能\n放置方块后随机切换至设定最大槽位\n按住功能切换键时滚动鼠标可调整参数", "tweakeroo.config.feature_toggle.comment.tweakHotbarSwap": "启用快捷栏快捷键交换功能", "tweakeroo.config.feature_toggle.comment.tweakInventoryPreview": "将光标放在具有容器的方块或实体上并按住配置的快捷键时，启用容器预览。\n§6建议使用1.21+ MiniHUD版本替代", "tweakeroo.config.feature_toggle.comment.tweakItemUnstackingProtection": "阻止「列表」 -> unstackingItems 中配置的物品发生拆堆\n用于防止意外丢弃物品（如岩浆桶）", "tweakeroo.config.feature_toggle.comment.tweakLavaVisibility": "水下呼吸/深海探索者附魔与抗火效果\n可大幅提升熔岩中的可视度", "tweakeroo.config.feature_toggle.comment.tweakMapPreview": "悬停地图时按住Shift显示地图预览", "tweakeroo.config.feature_toggle.comment.tweakMovementKeysLast": "启用末次按键优先机制\n相反方向移动键不会互相取消", "tweakeroo.config.feature_toggle.comment.tweakPeriodicAttack": "启用周期性攻击功能\n间隔时间见「通用」-> periodicAttackInterval", "tweakeroo.config.feature_toggle.comment.tweakPeriodicHoldAttack": "启用周期性长按攻击功能\n间隔与持续时间见「通用」相关参数\n§6注意：请勿与普通持续攻击或周期攻击同时使用", "tweakeroo.config.feature_toggle.comment.tweakPeriodicHoldUse": "启用周期性长按使用功能\n间隔与持续时间见「通用」相关参数\n§6注意：请勿与普通持续使用或周期使用同时使用", "tweakeroo.config.feature_toggle.comment.tweakPeriodicUse": "启用周期性使用功能\n间隔时间见「通用」-> periodicUseInterval", "tweakeroo.config.feature_toggle.comment.tweakPermanentSneak": "使玩家始终保持潜行状态", "tweakeroo.config.feature_toggle.comment.tweakPermanentSprint": "使玩家前进时始终保持疾跑状态", "tweakeroo.config.feature_toggle.comment.tweakPickBeforePlace": "放置前自动切换与目标方块相同的物品", "tweakeroo.config.feature_toggle.comment.tweakPlacementGrid": "启用网格化限制放置模式\n按住功能切换键时滚动鼠标可调整网格间隔", "tweakeroo.config.feature_toggle.comment.tweakPlacementLimit": "限制每次右键点击可放置的方块数量\n按住功能切换键时滚动鼠标可调整数量", "tweakeroo.config.feature_toggle.comment.tweakPlacementRestriction": "启用放置限制模式（平面/层状/点击面/列/十字/对角线）", "tweakeroo.config.feature_toggle.comment.tweakPlacementRestrictionFirst": "限制只能在与首次点击方块类型相同的方块旁放置", "tweakeroo.config.feature_toggle.comment.tweakPlacementRestrictionHand": "限制只能放置与手持方块类型相同的方块", "tweakeroo.config.feature_toggle.comment.tweakPlayerInventoryPeek": "启用玩家背包预览功能\n需按住配置的快捷键激活", "tweakeroo.config.feature_toggle.comment.tweakPlayerListAlwaysVisible": "无需按住Tab键即可常显玩家列表", "tweakeroo.config.feature_toggle.comment.tweakPotionWarning": "当非环境类药水效果（如速度、力量等）剩余时间不足时，在快捷栏上方显示警告提示", "tweakeroo.config.feature_toggle.comment.tweakPrintDeathCoordinates": "死亡时在聊天栏输出坐标信息\n源自nessie的usefulmod模组", "tweakeroo.config.feature_toggle.comment.tweakRenderEdgeChunks": "允许渲染客户端加载的边缘区块\n原版会因相邻区块未加载而禁用边缘渲染\n§l此功能对灵魂出窍(自由视角)模式非常有用！§r", "tweakeroo.config.feature_toggle.comment.tweakRenderInvisibleEntities": "以旁观模式样式渲染隐形实体", "tweakeroo.config.feature_toggle.comment.tweakRenderLimitEntities": "限制每帧渲染的特定实体数量\n当前支持经验球和物品实体\n详见「通用」中的限制参数", "tweakeroo.config.feature_toggle.comment.tweakRepairMode": "自动将手持的完好物品\n替换为带有经验修补的受损物品", "tweakeroo.config.feature_toggle.comment.tweakSculkPulseLength": "自定义幽匿感测体脉冲时长\n设置见通用 -> sculkSensorPulseLength", "tweakeroo.config.feature_toggle.comment.tweakSelectiveBlocksRenderOutline": "在指定方块上渲染轮廓线\n由 Andrew54757 在 TweakFork 下编写", "tweakeroo.config.feature_toggle.comment.tweakSelectiveBlocksRendering": "启用选择性可见方块渲染\n由 Andrew54757 在 TweakFork 下编写", "tweakeroo.config.feature_toggle.comment.tweakServerDataSync": "使用服务器数据同步器实现潜影盒预览等功能\n需服务器安装配套模组或拥有管理员权限", "tweakeroo.config.feature_toggle.comment.tweakServerDataSyncBackup": "当Servux不可用时，使用原版NbtQueryRequest数据包同步", "tweakeroo.config.feature_toggle.comment.tweakShulkerBoxDisplay": "按住Shift悬停潜影盒时显示内容物预览", "tweakeroo.config.feature_toggle.comment.tweakSignCopy": "新放置的告示牌自动复制前次文本\n配合tweakNoSignGui可快速批量复制告示牌", "tweakeroo.config.feature_toggle.comment.tweakSnapAim": "启用预设角度的精准视角锁定功能", "tweakeroo.config.feature_toggle.comment.tweakSnapAimLock": "将视角锁定至当前精准角度\n可单独锁定偏航角/俯仰角", "tweakeroo.config.feature_toggle.comment.tweakSneak_1_15_2": "还原1.15.2版本的潜行机制", "tweakeroo.config.feature_toggle.comment.tweakSpectatorTeleport": "允许旁观者传送到其他旁观者玩家\n此功能源自nessie开发的usefulmod模组", "tweakeroo.config.feature_toggle.comment.tweakStructureBlockLimit": "允许覆盖结构方块的大小限制\n新限制值在「通用」-> structureBlockMaxSize中配置", "tweakeroo.config.feature_toggle.comment.tweakSwapAlmostBrokenTools": "当手持的耐久工具即将损坏时\n自动切换为完好的同类工具", "tweakeroo.config.feature_toggle.comment.tweakTabCompleteCoordinate": "启用后，当未注视方块时进行坐标补全\n将直接使用玩家当前位置而非添加~符号", "tweakeroo.config.feature_toggle.comment.tweakToolSwitch": "自动切换为适合破坏目标方块的有效工具", "tweakeroo.config.feature_toggle.comment.tweakWaterVisibility": "如果启用，水下呼吸和水下速掘附魔的等级将显著提升水下能见度。", "tweakeroo.config.feature_toggle.comment.tweakWeaponSwitch": "自动切换为适合攻击目标实体的有效武器", "tweakeroo.config.feature_toggle.comment.tweakYMirror": "在方块边界内镜像Y轴坐标进行放置\n主要用于台阶、楼梯类方块\n在相邻同类方块旁放置时反转顶部/底部状态", "tweakeroo.config.feature_toggle.comment.tweakZoom": "启用视角缩放快捷键功能", "tweakeroo.config.feature_toggle.name.tweakAccurateBlockPlacement": "精准方块放置", "tweakeroo.config.feature_toggle.name.tweakAfterClicker": "后置点击器", "tweakeroo.config.feature_toggle.name.tweakAimLock": "视线锁定", "tweakeroo.config.feature_toggle.name.tweakAngelBlock": "浮空放置方块(AngelBlock)", "tweakeroo.config.feature_toggle.name.tweakAreaSelector": "区域选择器", "tweakeroo.config.feature_toggle.name.tweakAutoSwitchElytra": "自动切换鞘翅", "tweakeroo.config.feature_toggle.name.tweakBlockReachOverride": "§6手长 - 方块触及距离修改§r", "tweakeroo.config.feature_toggle.name.tweakBlockTypeBreakRestriction": "破坏限制 - 方块类型", "tweakeroo.config.feature_toggle.name.tweakBreakingGrid": "破坏限制 - 网格化", "tweakeroo.config.feature_toggle.name.tweakBreakingRestriction": "破坏限制 - 范围", "tweakeroo.config.feature_toggle.name.tweakBundleDisplay": "收纳袋内容显示", "tweakeroo.config.feature_toggle.name.tweakChatBackgroundColor": "聊天背景色修改", "tweakeroo.config.feature_toggle.name.tweakChatPersistentText": "聊天文本持久化", "tweakeroo.config.feature_toggle.name.tweakChatTimestamp": "聊天时间戳", "tweakeroo.config.feature_toggle.name.tweakCommandBlockExtraFields": "命令方块扩展字段", "tweakeroo.config.feature_toggle.name.tweakCreativeExtraItems": "创造模式扩展物品", "tweakeroo.config.feature_toggle.name.tweakCustomFlatPresets": "自定义超平坦预设", "tweakeroo.config.feature_toggle.name.tweakCustomFlyDeceleration": "自定义飞行减速", "tweakeroo.config.feature_toggle.name.tweakCustomInventoryScreenScale": "自定义背包界面缩放", "tweakeroo.config.feature_toggle.name.tweakDarknessVisibility": "黑暗效果能见度提高", "tweakeroo.config.feature_toggle.name.tweakElytraCamera": "鞘翅视角分离", "tweakeroo.config.feature_toggle.name.tweakEmptyShulkerBoxesStack": "§6空潜影盒堆叠§r", "tweakeroo.config.feature_toggle.name.tweakEntityReachOverride": "§6手长 - 实体触及距离修改§r", "tweakeroo.config.feature_toggle.name.tweakEntityTypeAttackRestriction": "实体类型攻击限制", "tweakeroo.config.feature_toggle.name.tweakExplosionReducedParticles": "爆炸粒子简化", "tweakeroo.config.feature_toggle.name.tweakF3Cursor": "F3光标显示", "tweakeroo.config.feature_toggle.name.tweakFakeSneakPlacement": "伪潜行放置", "tweakeroo.config.feature_toggle.name.tweakFakeSneaking": "伪潜行", "tweakeroo.config.feature_toggle.name.tweakFastBlockPlacement": "快速方块放置", "tweakeroo.config.feature_toggle.name.tweakFastLeftClick": "快速左键连点", "tweakeroo.config.feature_toggle.name.tweakFastRightClick": "快速右键连点", "tweakeroo.config.feature_toggle.name.tweakFillCloneLimit": "§6fill clone 指令限制§r", "tweakeroo.config.feature_toggle.name.tweakFlexibleBlockPlacement": "灵活方块放置", "tweakeroo.config.feature_toggle.name.tweakFlySpeed": "飞行速度调节", "tweakeroo.config.feature_toggle.name.tweakFreeCamera": "灵魂出窍(自由视角)", "tweakeroo.config.feature_toggle.name.tweakGammaOverride": "伽马值修改", "tweakeroo.config.feature_toggle.name.tweakHandRestock": "手持物品自动补货", "tweakeroo.config.feature_toggle.name.tweakHangableEntityBypass": "悬挂实体绕过", "tweakeroo.config.feature_toggle.name.tweakHoldAttack": "持续攻击", "tweakeroo.config.feature_toggle.name.tweakHoldUse": "持续使用", "tweakeroo.config.feature_toggle.name.tweakHotbarScroll": "快捷栏滚动", "tweakeroo.config.feature_toggle.name.tweakHotbarSlotCycle": "快捷栏循环切换", "tweakeroo.config.feature_toggle.name.tweakHotbarSlotRandomizer": "快捷栏随机切换", "tweakeroo.config.feature_toggle.name.tweakHotbarSwap": "快捷栏交换", "tweakeroo.config.feature_toggle.name.tweakInventoryPreview": "§6容器预览 - 开关§r", "tweakeroo.config.feature_toggle.name.tweakItemUnstackingProtection": "物品防拆堆保护", "tweakeroo.config.feature_toggle.name.tweakLavaVisibility": "熔岩视野优化", "tweakeroo.config.feature_toggle.name.tweakMapPreview": "地图预览", "tweakeroo.config.feature_toggle.name.tweakMovementKeysLast": "末次移动按键优先", "tweakeroo.config.feature_toggle.name.tweakPeriodicAttack": "周期性攻击", "tweakeroo.config.feature_toggle.name.tweakPeriodicHoldAttack": "周期性长按攻击", "tweakeroo.config.feature_toggle.name.tweakPeriodicHoldUse": "周期性长按使用", "tweakeroo.config.feature_toggle.name.tweakPeriodicUse": "周期性使用", "tweakeroo.config.feature_toggle.name.tweakPermanentSneak": "永久潜行", "tweakeroo.config.feature_toggle.name.tweakPermanentSprint": "永久疾跑", "tweakeroo.config.feature_toggle.name.tweakPickBeforePlace": "放置前自动选材", "tweakeroo.config.feature_toggle.name.tweakPlacementGrid": "放置限制 - 网格化", "tweakeroo.config.feature_toggle.name.tweakPlacementLimit": "放置限制 - 单次数量", "tweakeroo.config.feature_toggle.name.tweakPlacementRestriction": "放置限制 - 范围", "tweakeroo.config.feature_toggle.name.tweakPlacementRestrictionFirst": "放置限制 - 首次", "tweakeroo.config.feature_toggle.name.tweakPlacementRestrictionHand": "放置限制 - 手持物品", "tweakeroo.config.feature_toggle.name.tweakPlayerInventoryPeek": "玩家背包预览", "tweakeroo.config.feature_toggle.name.tweakPlayerListAlwaysVisible": "常显玩家列表", "tweakeroo.config.feature_toggle.name.tweakPotionWarning": "药水效果预警", "tweakeroo.config.feature_toggle.name.tweakPrintDeathCoordinates": "死亡坐标打印", "tweakeroo.config.feature_toggle.name.tweakRenderEdgeChunks": "边缘区块渲染", "tweakeroo.config.feature_toggle.name.tweakRenderInvisibleEntities": "渲染隐形实体", "tweakeroo.config.feature_toggle.name.tweakRenderLimitEntities": "实体渲染数量限制", "tweakeroo.config.feature_toggle.name.tweakRepairMode": "自动修复模式", "tweakeroo.config.feature_toggle.name.tweakSculkPulseLength": "§6幽匿脉冲时长§r", "tweakeroo.config.feature_toggle.name.tweakSelectiveBlocksRenderOutline": "选择性方块轮廓渲染", "tweakeroo.config.feature_toggle.name.tweakSelectiveBlocksRendering": "选择性方块渲染", "tweakeroo.config.feature_toggle.name.tweakServerDataSync": "服务器数据同步", "tweakeroo.config.feature_toggle.name.tweakServerDataSyncBackup": "服务器数据同步备用方案", "tweakeroo.config.feature_toggle.name.tweakShulkerBoxDisplay": "潜影盒内容显示", "tweakeroo.config.feature_toggle.name.tweakSignCopy": "告示牌文本复制", "tweakeroo.config.feature_toggle.name.tweakSnapAim": "对齐视线", "tweakeroo.config.feature_toggle.name.tweakSnapAimLock": "对齐视线并锁定", "tweakeroo.config.feature_toggle.name.tweakSneak_1_15_2": "1.15.2潜行机制", "tweakeroo.config.feature_toggle.name.tweakSpectatorTeleport": "旁观者传送", "tweakeroo.config.feature_toggle.name.tweakStructureBlockLimit": "§6结构方块限制§r", "tweakeroo.config.feature_toggle.name.tweakSwapAlmostBrokenTools": "濒损工具自动切换", "tweakeroo.config.feature_toggle.name.tweakTabCompleteCoordinate": "坐标补全优化", "tweakeroo.config.feature_toggle.name.tweakToolSwitch": "自动切换工具", "tweakeroo.config.feature_toggle.name.tweakWaterVisibility": "水中能见度提高", "tweakeroo.config.feature_toggle.name.tweakWeaponSwitch": "自动切换武器", "tweakeroo.config.feature_toggle.name.tweakYMirror": "Y轴镜像放置(台阶、楼梯等)", "tweakeroo.config.feature_toggle.name.tweakZoom": "视角缩放", "tweakeroo.config.feature_toggle.prettyName.tweakAccurateBlockPlacement": "精准方块放置", "tweakeroo.config.feature_toggle.prettyName.tweakAfterClicker": "后置点击器", "tweakeroo.config.feature_toggle.prettyName.tweakAimLock": "任意视线锁定", "tweakeroo.config.feature_toggle.prettyName.tweakAngelBlock": "浮空放置方块(AngelBlock)", "tweakeroo.config.feature_toggle.prettyName.tweakAreaSelector": "区域选择器", "tweakeroo.config.feature_toggle.prettyName.tweakAutoSwitchElytra": "自动切换鞘翅", "tweakeroo.config.feature_toggle.prettyName.tweakBlockReachOverride": "手长 - 方块触及距离修改", "tweakeroo.config.feature_toggle.prettyName.tweakBlockTypeBreakRestriction": "破坏限制 - 方块类型", "tweakeroo.config.feature_toggle.prettyName.tweakBreakingGrid": "破坏限制 - 网格化", "tweakeroo.config.feature_toggle.prettyName.tweakBreakingRestriction": "破坏限制 - 范围", "tweakeroo.config.feature_toggle.prettyName.tweakBundleDisplay": "收纳袋内容显示", "tweakeroo.config.feature_toggle.prettyName.tweakChatBackgroundColor": "聊天背景色修改", "tweakeroo.config.feature_toggle.prettyName.tweakChatPersistentText": "聊天文本持久化", "tweakeroo.config.feature_toggle.prettyName.tweakChatTimestamp": "聊天时间戳", "tweakeroo.config.feature_toggle.prettyName.tweakCommandBlockExtraFields": "命令方块扩展字段", "tweakeroo.config.feature_toggle.prettyName.tweakCreativeExtraItems": "创造模式扩展物品", "tweakeroo.config.feature_toggle.prettyName.tweakCustomFlatPresets": "自定义超平坦预设", "tweakeroo.config.feature_toggle.prettyName.tweakCustomFlyDeceleration": "自定义飞行减速", "tweakeroo.config.feature_toggle.prettyName.tweakCustomInventoryScreenScale": "自定义背包界面缩放", "tweakeroo.config.feature_toggle.prettyName.tweakDarknessVisibility": "黑暗效果能见度提高", "tweakeroo.config.feature_toggle.prettyName.tweakElytraCamera": "鞘翅视角分离", "tweakeroo.config.feature_toggle.prettyName.tweakEmptyShulkerBoxesStack": "空潜影盒堆叠", "tweakeroo.config.feature_toggle.prettyName.tweakEntityReachOverride": "手长 - 实体触及距离修改", "tweakeroo.config.feature_toggle.prettyName.tweakEntityTypeAttackRestriction": "实体类型攻击限制", "tweakeroo.config.feature_toggle.prettyName.tweakExplosionReducedParticles": "爆炸粒子简化", "tweakeroo.config.feature_toggle.prettyName.tweakF3Cursor": "F3光标显示", "tweakeroo.config.feature_toggle.prettyName.tweakFakeSneakPlacement": "伪潜行放置", "tweakeroo.config.feature_toggle.prettyName.tweakFakeSneaking": "伪潜行", "tweakeroo.config.feature_toggle.prettyName.tweakFastBlockPlacement": "快速方块放置", "tweakeroo.config.feature_toggle.prettyName.tweakFastLeftClick": "快速左键连点", "tweakeroo.config.feature_toggle.prettyName.tweakFastRightClick": "快速右键连点", "tweakeroo.config.feature_toggle.prettyName.tweakFillCloneLimit": "fill clone 指令限制", "tweakeroo.config.feature_toggle.prettyName.tweakFlexibleBlockPlacement": "灵活方块放置", "tweakeroo.config.feature_toggle.prettyName.tweakFlySpeed": "飞行速度调节", "tweakeroo.config.feature_toggle.prettyName.tweakFreeCamera": "灵魂出窍(自由视角)", "tweakeroo.config.feature_toggle.prettyName.tweakGammaOverride": "伽马值修改", "tweakeroo.config.feature_toggle.prettyName.tweakHandRestock": "手持物品自动补货", "tweakeroo.config.feature_toggle.prettyName.tweakHangableEntityBypass": "悬挂实体绕过", "tweakeroo.config.feature_toggle.prettyName.tweakHoldAttack": "持续攻击", "tweakeroo.config.feature_toggle.prettyName.tweakHoldUse": "持续使用", "tweakeroo.config.feature_toggle.prettyName.tweakHotbarScroll": "快捷栏滚动", "tweakeroo.config.feature_toggle.prettyName.tweakHotbarSlotCycle": "快捷栏循环切换", "tweakeroo.config.feature_toggle.prettyName.tweakHotbarSlotRandomizer": "快捷栏随机切换", "tweakeroo.config.feature_toggle.prettyName.tweakHotbarSwap": "快捷栏交换", "tweakeroo.config.feature_toggle.prettyName.tweakInventoryPreview": "容器预览", "tweakeroo.config.feature_toggle.prettyName.tweakItemUnstackingProtection": "物品防拆堆保护", "tweakeroo.config.feature_toggle.prettyName.tweakLavaVisibility": "熔岩视野优化", "tweakeroo.config.feature_toggle.prettyName.tweakMapPreview": "地图预览", "tweakeroo.config.feature_toggle.prettyName.tweakMovementKeysLast": "末次移动按键优先", "tweakeroo.config.feature_toggle.prettyName.tweakPeriodicAttack": "周期性攻击", "tweakeroo.config.feature_toggle.prettyName.tweakPeriodicHoldAttack": "周期性长按攻击", "tweakeroo.config.feature_toggle.prettyName.tweakPeriodicHoldUse": "周期性长按使用", "tweakeroo.config.feature_toggle.prettyName.tweakPeriodicUse": "周期性使用", "tweakeroo.config.feature_toggle.prettyName.tweakPermanentSneak": "永久潜行", "tweakeroo.config.feature_toggle.prettyName.tweakPermanentSprint": "永久疾跑", "tweakeroo.config.feature_toggle.prettyName.tweakPickBeforePlace": "放置前自动选材", "tweakeroo.config.feature_toggle.prettyName.tweakPlacementGrid": "放置限制 - 网格化", "tweakeroo.config.feature_toggle.prettyName.tweakPlacementLimit": "放置限制 - 单次数量", "tweakeroo.config.feature_toggle.prettyName.tweakPlacementRestriction": "放置限制 - 范围", "tweakeroo.config.feature_toggle.prettyName.tweakPlacementRestrictionFirst": "放置限制 - 首次", "tweakeroo.config.feature_toggle.prettyName.tweakPlacementRestrictionHand": "放置限制 - 手持物品", "tweakeroo.config.feature_toggle.prettyName.tweakPlayerInventoryPeek": "玩家背包预览", "tweakeroo.config.feature_toggle.prettyName.tweakPlayerListAlwaysVisible": "常显玩家列表", "tweakeroo.config.feature_toggle.prettyName.tweakPotionWarning": "药水效果预警", "tweakeroo.config.feature_toggle.prettyName.tweakPrintDeathCoordinates": "死亡坐标打印", "tweakeroo.config.feature_toggle.prettyName.tweakRenderEdgeChunks": "边缘区块渲染", "tweakeroo.config.feature_toggle.prettyName.tweakRenderInvisibleEntities": "渲染隐形实体", "tweakeroo.config.feature_toggle.prettyName.tweakRenderLimitEntities": "实体渲染数量限制", "tweakeroo.config.feature_toggle.prettyName.tweakRepairMode": "自动修复模式", "tweakeroo.config.feature_toggle.prettyName.tweakSculkPulseLength": "幽匿脉冲时长", "tweakeroo.config.feature_toggle.prettyName.tweakSelectiveBlocksRenderOutline": "选择性方块轮廓渲染", "tweakeroo.config.feature_toggle.prettyName.tweakSelectiveBlocksRendering": "选择性方块渲染", "tweakeroo.config.feature_toggle.prettyName.tweakServerDataSync": "服务器数据同步", "tweakeroo.config.feature_toggle.prettyName.tweakServerDataSyncBackup": "服务器数据同步备用方案", "tweakeroo.config.feature_toggle.prettyName.tweakShulkerBoxDisplay": "潜影盒内容显示", "tweakeroo.config.feature_toggle.prettyName.tweakSignCopy": "告示牌文本复制", "tweakeroo.config.feature_toggle.prettyName.tweakSnapAim": "精准视角锁定", "tweakeroo.config.feature_toggle.prettyName.tweakSnapAimLock": "精准视角锁定模式", "tweakeroo.config.feature_toggle.prettyName.tweakSneak_1_15_2": "1.15.2潜行机制", "tweakeroo.config.feature_toggle.prettyName.tweakSpectatorTeleport": "旁观者传送", "tweakeroo.config.feature_toggle.prettyName.tweakStructureBlockLimit": "结构方块限制", "tweakeroo.config.feature_toggle.prettyName.tweakSwapAlmostBrokenTools": "濒损工具自动切换", "tweakeroo.config.feature_toggle.prettyName.tweakTabCompleteCoordinate": "坐标补全优化", "tweakeroo.config.feature_toggle.prettyName.tweakToolSwitch": "自动切换工具", "tweakeroo.config.feature_toggle.prettyName.tweakWaterVisibility": "水中能见度提高", "tweakeroo.config.feature_toggle.prettyName.tweakWeaponSwitch": "自动切换武器", "tweakeroo.config.feature_toggle.prettyName.tweakYMirror": "Y轴镜像放置(台阶、楼梯等)", "tweakeroo.config.feature_toggle.prettyName.tweakZoom": "视角缩放", "tweakeroo.config.fixes.comment.elytraFix": "由Earthcomputer和N<PERSON>ie提供的鞘翅着陆修复。\n部署修复现已整合至原版，此功能仅影响着陆逻辑。", "tweakeroo.config.fixes.comment.elytraSprintCancel": "修复原版漏洞（MC-279688）：使用鞘翅时取消疾跑状态\n此设置仅需用于 1.21.4 版本，1.21.5 已修复该问题", "tweakeroo.config.fixes.comment.macHorizontalScroll": "Mac/OSX系统专用修复，应用与hscroll模组相同的调整，\n同时避免破坏基于malilib框架模组的滚动功能。", "tweakeroo.config.fixes.comment.ravagerClientBlockBreakFix": "修复劫掠兽客户端破坏方块导致的\n幽灵方块/方块同步异常问题", "tweakeroo.config.fixes.name.elytraFix": "鞘翅修复", "tweakeroo.config.fixes.name.elytraSprintCancel": "鞘翅疾跑取消修复", "tweakeroo.config.fixes.name.macHorizontalScroll": "<PERSON>水平滚动修复", "tweakeroo.config.fixes.name.ravagerClientBlockBreakFix": "劫掠兽客户端破块修复", "tweakeroo.config.generic.comment.accuratePlacementProtocol": "启用后，灵活方块放置和精准方块放置将使用Carpet模组实现的协议。\n§6注意：此功能是实现除点击面相关方块（漏斗、原木等）外\n§6其他方块的旋转功能所必需的。", "tweakeroo.config.generic.comment.accuratePlacementProtocolMode": "使用的“精准放置协议”类型。\n- 自动：在单人游戏中使用v3(单人或Servux支持)，多人模式默认使用“仅限半砖”，\n  除非服务器安装了Carpet模组并发送了'carpet:hello'数据包，\n  此时在该服务器上将使用v2(Carpet支持)版本。\n- v3(单人或Servux支持)：在单人模式由Tweakeroo自身支持，或服务器安装Servux支持。\n- v2(Carpet支持)：兼容安装了Carpet模组的服务器\n  （skyrising和DeadlyMC的QuickCarpet，或FabricCarpet的CarpetExtra扩展。\n    两种情况均需在服务器启用'accurateBlockPlacement'规则）。\n- 仅限半砖(Paper服)：仅修复上半砖。兼容Paper服务器。\n- 无：不修改坐标。", "tweakeroo.config.generic.comment.afterClickerClickCount": "启用后置点击器时，每个放置方块所需的右键点击次数", "tweakeroo.config.generic.comment.angelBlockPlacementDistance": "启用「浮空放置方块(Angel<PERSON><PERSON>)」时，玩家可在空中放置方块的最大距离。\n服务器允许的最大值为5。", "tweakeroo.config.generic.comment.areaSelectionUseAll": "是否在选区中包含空气方块\n由 Andrew54757 在 TweakFork 下编写", "tweakeroo.config.generic.comment.blockReachDistance": "若启用修改功能时，修改后的方块触及距离。\n游戏允许的最大值为64。\n§6请勿尝试将此值设置为超过服务器规则定义值的\n§6[0.5 - 1.0]范围。", "tweakeroo.config.generic.comment.blockTypeBreakRestrictionWarn": "当方块类型破坏限制功能阻止破坏方块时，选择显示的警告类型（若有）", "tweakeroo.config.generic.comment.breakingGridSize": "网格破坏模式的网格间隔尺寸。\n按住功能切换键时滚动可快速调整此值。", "tweakeroo.config.generic.comment.breakingRestrictionMode": "使用的破坏限制模式（可通过快捷键选择）", "tweakeroo.config.generic.comment.bundleDisplayBgColor": "启用后，将根据收纳袋的染料颜色为其预览界面背景着色", "tweakeroo.config.generic.comment.bundleDisplayRequireShift": "是否需按住Shift键显示收纳袋预览\n§6注意：禁用此功能将完全关闭原版收纳袋工具提示。", "tweakeroo.config.generic.comment.bundleDisplayRowWidth": "调整收纳袋预览的行宽尺寸，\n过小或过大将导致纹理显示异常。", "tweakeroo.config.generic.comment.chatBackgroundColor": "启用'tweakChatBackgroundColor'时聊天信息的背景颜色", "tweakeroo.config.generic.comment.chatTimeFormat": "启用聊天时间戳时的日期格式，使用Java SimpleDateFormat格式规范。", "tweakeroo.config.generic.comment.clientPlacementRotation": "在单人游戏或客户端启用放置旋转功能\n（例如无需Carpet模组即可在单人游戏中使用精准放置）", "tweakeroo.config.generic.comment.customInventoryGuiScale": "启用§etweakCustomInventoryScreenScale§r时，背包界面的缩放值。", "tweakeroo.config.generic.comment.debugLogging": "在游戏控制台启用调试日志，用于排查特定问题或崩溃。", "tweakeroo.config.generic.comment.darknessScaleOverrideValue": "The override value used by 'tweakDarknessVisibility'\nto automatically weaken the 'darkening' screen effect.\nThis is a percentage value for the amount\nthat the screen darkens for each effect pulse.", "tweakeroo.config.generic.comment.elytraCameraIndicator": "鞘翅视角模式激活时是否显示真实俯仰角指示器", "tweakeroo.config.generic.comment.entityReachDistance": "若启用修改功能时，修改后的实体触及距离。\n游戏允许的最大值为64。\n§6请勿尝试将此值设置为超过服务器规则定义值的\n§6[0.5 - 1.0]范围。", "tweakeroo.config.generic.comment.entityTypeAttackRestrictionWarn": "当实体类型攻击限制功能阻止攻击实体时，选择显示的警告类型（若有）", "tweakeroo.config.generic.comment.fastBlockPlacementCount": "快速方块放置功能每游戏刻可放置的最大方块数", "tweakeroo.config.generic.comment.fastLeftClickAllowTools": "允许生存模式下持工具时使用快速左键功能。\n§6这会让玩家可以每tick破坏多个方块", "tweakeroo.config.generic.comment.fastLeftClickCount": "启用快速左键且按住攻击键时，每游戏刻的左键点击次数", "tweakeroo.config.generic.comment.fastPlacementRememberOrientation": "启用后，快速方块放置将始终记忆首个放置方块的朝向。\n未启用时，仅当灵活放置功能激活时才会记忆朝向。", "tweakeroo.config.generic.comment.fastRightClickCount": "启用快速右键且按住使用键时，每游戏刻的右键点击次数", "tweakeroo.config.generic.comment.fillCloneLimit": "启用覆盖功能时，单人游戏中/fill和/clone指令的方块上限", "tweakeroo.config.generic.comment.flexibleBlockPlacementOverlayColor": "灵活放置功能中当前指向区域的高亮颜色", "tweakeroo.config.generic.comment.flyDecelerationFactor": "调整'customFlyDeceleration'功能启用时的玩家减速速率", "tweakeroo.config.generic.comment.flySpeedIncrement1": "“增量1”的飞行速度变化量", "tweakeroo.config.generic.comment.flySpeedIncrement2": "“增量2”的飞行速度变化量", "tweakeroo.config.generic.comment.flySpeedPreset1": "预设1的飞行速度", "tweakeroo.config.generic.comment.flySpeedPreset2": "预设2的飞行速度", "tweakeroo.config.generic.comment.flySpeedPreset3": "预设3的飞行速度", "tweakeroo.config.generic.comment.flySpeedPreset4": "预设4的飞行速度", "tweakeroo.config.generic.comment.freeCameraPlayerInputs": "启用后，自由视角模式中的攻击和使用动作（即左右键）\n将传递给实际玩家角色。", "tweakeroo.config.generic.comment.freeCameraPlayerMovement": "启用后，自由视角模式中的移动操作将控制实际玩家而非摄像机", "tweakeroo.config.generic.comment.gammaOverrideValue": "启用伽马覆盖时使用的亮度值", "tweakeroo.config.generic.comment.handRestockPre": "启用后，手部物品将在耗尽前自动补货", "tweakeroo.config.generic.comment.handRestockPreThreshold": "预补货模式下手动补货触发的堆叠数量阈值", "tweakeroo.config.generic.comment.hangableEntityBypassInverse": "若启用悬挂实体目标绕过功能，此选项控制玩家是否需要潜行\n才能选中悬挂实体（物品展示框或画作）。\n > true - 潜行时忽略实体\n > false - 潜行时选中实体", "tweakeroo.config.generic.comment.hotbarSlotCycleMax": "快捷栏循环功能使用的最后一个槽位编号。\n当超过此处设置的最大值时，循环将跳回第一个槽位。", "tweakeroo.config.generic.comment.hotbarSlotRandomizerMax": "快捷栏随机功能使用的最大槽位编号。\n每次使用物品后，将随机选择1至此最大值之间的槽位。", "tweakeroo.config.generic.comment.hotbarSwapOverlayAlignment": "快捷栏切换界面的对齐方式", "tweakeroo.config.generic.comment.hotbarSwapOverlayOffsetX": "快捷栏切换界面的水平偏移量", "tweakeroo.config.generic.comment.hotbarSwapOverlayOffsetY": "快捷栏切换界面的垂直偏移量", "tweakeroo.config.generic.comment.inventoryPreviewVillagerBGColor": "启用/禁用基于村民职业的交易界面背景颜色显示", "tweakeroo.config.generic.comment.itemSwapDurabilityThreshold": "低耐久物品交换功能的耐久阈值（剩余使用次数）。\n注意：总耐久较低的物品会在剩余5%%时被交换。", "tweakeroo.config.generic.comment.itemUsePacketCheckBypass": "绕过1.18.2新增的距离/坐标检查。\n\n该检查会破坏“精准放置协议”并导致\n任何带有旋转（或其他属性）请求的放置方块变为幽灵方块。\n\n基本无需禁用此功能。\n该检查在1.18.2之前从未存在。", "tweakeroo.config.generic.comment.mapPreviewRequireShift": "是否需按住Shift键显示地图预览", "tweakeroo.config.generic.comment.mapPreviewSize": "渲染地图预览的尺寸", "tweakeroo.config.generic.comment.periodicAttackInterval": "自动攻击（左键）间隔的游戏刻数", "tweakeroo.config.generic.comment.periodicAttackResetIntervalOnActivate": "若通过鼠标滚轮调整过间隔，则在停用时重置周期性攻击间隔", "tweakeroo.config.generic.comment.periodicHoldAttackDuration": "持续按住攻击键的游戏刻时长", "tweakeroo.config.generic.comment.periodicHoldAttackInterval": "开始持续攻击（左键）的间隔游戏刻数", "tweakeroo.config.generic.comment.periodicHoldAttackResetIntervalOnActivate": "若通过鼠标滚轮调整过间隔，则在停用时重置持续攻击间隔", "tweakeroo.config.generic.comment.periodicHoldUseDuration": "持续按住使用键的游戏刻时长", "tweakeroo.config.generic.comment.periodicHoldUseInterval": "开始持续使用（右键）的间隔游戏刻数", "tweakeroo.config.generic.comment.periodicHoldUseResetIntervalOnActivate": "若通过鼠标滚轮调整过间隔，则在停用时重置持续使用间隔", "tweakeroo.config.generic.comment.periodicUseInterval": "自动使用（右键）间隔的游戏刻数", "tweakeroo.config.generic.comment.periodicUseResetIntervalOnActivate": "若通过鼠标滚轮调整过间隔，则在停用时重置周期性使用间隔", "tweakeroo.config.generic.comment.permanentSneakAllowInGUIs": "打开GUI界面时，永久潜行依然有效", "tweakeroo.config.generic.comment.placementGridSize": "网格放置模式的网格间隔尺寸。\n按住功能切换键时滚动可快速调整此值。", "tweakeroo.config.generic.comment.placementLimit": "启用tweakPlacementLimit时，每次右键单击最多可放置的方块数。\n按住功能切换键时滚动可快速调整此值。", "tweakeroo.config.generic.comment.placementRestrictionMode": "使用的放置限制模式（可通过快捷键选择）", "tweakeroo.config.generic.comment.placementRestrictionTiedToFast": "启用时，放置限制模式将随快速放置模式的切换而改变其启用/禁用状态。", "tweakeroo.config.generic.comment.potionWarningBeneficialOnly": "仅对标记为'有益'的药水效果显示即将结束的警告", "tweakeroo.config.generic.comment.potionWarningThreshold": "触发药水效果警告的剩余时长（游戏刻）", "tweakeroo.config.generic.comment.rememberFlexibleFromClick": "启用后，只要按住使用键（右键），\n灵活放置状态将从首个放置方块开始保持。\n无需持续按住所有灵活功能激活键即可快速放置同朝向方块。", "tweakeroo.config.generic.comment.renderLimitItem": "每帧渲染的物品实体最大数量。\n-1为默认值（禁用此限制）。", "tweakeroo.config.generic.comment.renderLimitXPOrb": "每帧渲染的经验球实体最大数量。\n-1为默认值（禁用此限制）。", "tweakeroo.config.generic.comment.sculkSensorPulseLength": "启用'tweakSculkPulseLength'时幽匿感测体的脉冲时长。", "tweakeroo.config.generic.comment.selectiveBlocksHideEntities": "是否在选择性方块渲染中隐藏实体\n由 Andrew54757 在 TweakFork 下编写", "tweakeroo.config.generic.comment.selectiveBlocksHideParticles": "是否在选择性方块渲染中隐藏粒子效果\n由 Andrew54757 在 TweakFork 下编写", "tweakeroo.config.generic.comment.selectiveBlocksNoHit": "是否禁用对隐藏方块的选中\n由 Andrew54757 在 TweakFork 下编写", "tweakeroo.config.generic.comment.selectiveBlocksTrackPistons": "是否追踪活塞运动以进行选择性方块渲染\n由 Andrew54757 在 TweakFork 下编写", "tweakeroo.config.generic.comment.serverDataSyncCacheRefresh": "缓存刷新时间（单位：秒的小数部分）。\n该值表示即使实体数据仍存在于缓存中，从服务器请求实体数据的刷新间隔。\n建议设置为\"entityDataSyncCacheTimeout\"值的25%或更低。\n值'0.05f'表示50毫秒或每个游戏刻一次。", "tweakeroo.config.generic.comment.serverDataSyncCacheTimeout": "实体数据缓存记录的保留时间（秒）。\n数值越低更新越频繁。", "tweakeroo.config.generic.comment.serverNbtRequestRate": "服务器实体数据同步的请求频率限制", "tweakeroo.config.generic.comment.shulkerDisplayBgColor": "启用后，将根据潜影盒的染料颜色为其预览界面背景着色", "tweakeroo.config.generic.comment.shulkerDisplayEnderChest": "启用类似潜影盒预览的末影箱显示功能。", "tweakeroo.config.generic.comment.shulkerDisplayRequireShift": "是否需按住Shift键显示潜影盒预览", "tweakeroo.config.generic.comment.slotSyncWorkaround": "防止服务器覆盖快速使用物品（例如通过快速右键功能）的耐久值或堆叠数量。", "tweakeroo.config.generic.comment.slotSyncWorkaroundAlways": "始终在按住使用键时启用槽位同步修复，不仅限于快速右键或快速放置场景。\n主要用于其他需要持续使用物品的模组（如Litematica的轻松放置模式）。", "tweakeroo.config.generic.comment.snapAimIndicator": "是否显示快速瞄准角度指示器", "tweakeroo.config.generic.comment.snapAimIndicatorColor": "快速瞄准指示器的背景颜色", "tweakeroo.config.generic.comment.snapAimMode": "快速瞄准模式：偏航角/俯仰角/两者兼顾", "tweakeroo.config.generic.comment.snapAimOnlyCloseToAngle": "启用后，仅当内部角度接近目标角度时才会触发快速瞄准\n具体阈值可通过snapAimThreshold设置", "tweakeroo.config.generic.comment.snapAimPitchOvershoot": "是否允许俯仰角超出常规的±90度范围至±180度", "tweakeroo.config.generic.comment.snapAimPitchStep": "快速瞄准功能的俯仰角调整步长", "tweakeroo.config.generic.comment.snapAimThresholdPitch": "玩家俯仰角与此阈值的接近度将触发快速瞄准角度对齐", "tweakeroo.config.generic.comment.snapAimThresholdYaw": "玩家偏航角与此阈值的接近度将触发快速瞄准角度对齐", "tweakeroo.config.generic.comment.snapAimYawStep": "快速瞄准功能的偏航角调整步长", "tweakeroo.config.generic.comment.structureBlockMaxSize": "结构方块保存区域的最大尺寸限制", "tweakeroo.config.generic.comment.toolSwapBetterEnchants": "在确认工具适用于目标后，\n进一步根据附魔效果和稀有度进行工具交换决策。", "tweakeroo.config.generic.comment.toolSwapPreferSilkTouch": "在所有其他条件相同的情况下（如挖掘速度、附魔效果、材料等），才会优先考虑带有精准采集的工具。\n这一设置适用于当所挖掘的方块不适用“优先精准采集”时，帮助确定默认选择的镐子。\n\n§6注意：若禁用此选项，模组在比较工具时，只要找到符合所有条件的非精准采集工具，§6将始终优先使用该工具。\n\n提示：\n优先精准采集(SilkTouchFirst)：精准采集绝对优先\n偏好精准采集(PreferSilkTouch)：精准采集是次要的条件，其他条件比较后都一样才会考虑优先精准采集", "tweakeroo.config.generic.comment.toolSwapBambooUsesSwordFirst": "检查注视方块是否为竹子，如果是，则使用剑而不是斧头来挖掘它。", "tweakeroo.config.generic.comment.toolSwapNeedsShearsFirst": "首先检查方块是否需要剪刀，例如羊毛、藤蔓或蜘蛛网。\n方块的选择基于新的“§b#malilib:needs_shears§r”方块标签（因为原版中不存在该标签）。\n§6注意：此功能仅在原版中方块未正确匹配时激活。", "tweakeroo.config.generic.comment.toolSwapSilkTouchFirst": "优先检查方块是否需要精准采集（如玻璃或末影箱）。\n所选方块基于新的'§b#malilib:needs_silk_touch§r'方块标签（因为该标签在原版中不存在）。\n\n提示：\n优先精准采集(SilkTouchFirst)：精准采集绝对优先\n偏好精准采集(PreferSilkTouch)：精准采集是次要的条件，其他条件比较后都一样才会考虑优先精准采集", "tweakeroo.config.generic.comment.toolSwapSilkTouchOres": "通过“§b#malilib:ore_blocks§r”标签检查该方块是否为矿石方块，然后首先对其使用“精准采集”。\n§6注意：禁用此功能即可使用“时运”§r", "tweakeroo.config.generic.comment.toolSwapSilkTouchOverride": "查找要应用“优先精准采集(SilkTouchFirst)”的方块列表。\n§6注意：此功能可以在关闭“工具切换 - 优先精准采集(toolSwapSilkTouchFirst)”的情况下工作，\n§6但它的设计工作方式相同，并且可以与其共存。", "tweakeroo.config.generic.comment.toolSwitchIgnoredSlots": "工具切换功能在指定槽位激活时不生效的槽位列表", "tweakeroo.config.generic.comment.toolSwitchableSlots": "工具切换功能允许存放新工具的槽位列表。\n注意：工具切换也可切换至快捷栏已有对应工具的槽位，\n但仅会将新工具交换到此处指定的槽位。", "tweakeroo.config.generic.comment.weaponSwapBetterEnchants": "在确认武器适用于目标后，\n进一步根据附魔效果和稀有度进行武器交换决策。", "tweakeroo.config.generic.comment.zoomAdjustMouseSensitivity": "启用后，缩放功能激活时将降低鼠标灵敏度", "tweakeroo.config.generic.comment.zoomFov": "缩放功能使用的视野值（FOV）", "tweakeroo.config.generic.comment.zoomResetFovOnActivate": "若通过鼠标滚轮调整过缩放视野，停用时将重置FOV值", "tweakeroo.config.generic.name.accuratePlacementProtocol": "精准放置协议 - 开关", "tweakeroo.config.generic.name.accuratePlacementProtocolMode": "精准放置协议 - 模式", "tweakeroo.config.generic.name.afterClickerClickCount": "后置点击器点击次数", "tweakeroo.config.generic.name.angelBlockPlacementDistance": "「浮空放置方块(AngelBlock)」的距离", "tweakeroo.config.generic.name.areaSelectionUseAll": "选区包含空气设置", "tweakeroo.config.generic.name.blockReachDistance": "手长 - 方块触及距离", "tweakeroo.config.generic.name.blockTypeBreakRestrictionWarn": "方块破坏限制 - 警告提示位置", "tweakeroo.config.generic.name.breakingGridSize": "破坏限制 - 网格尺寸", "tweakeroo.config.generic.name.breakingRestrictionMode": "破坏限制 - 模式", "tweakeroo.config.generic.name.bundleDisplayBgColor": "收纳袋背景颜色", "tweakeroo.config.generic.name.bundleDisplayRequireShift": "收纳袋预览需按住Shift", "tweakeroo.config.generic.name.bundleDisplayRowWidth": "收纳袋预览行宽", "tweakeroo.config.generic.name.chatBackgroundColor": "聊天背景颜色", "tweakeroo.config.generic.name.chatTimeFormat": "聊天时间格式", "tweakeroo.config.generic.name.clientPlacementRotation": "客户端放置旋转", "tweakeroo.config.generic.name.customInventoryGuiScale": "自定义背包界面缩放", "tweakeroo.config.generic.name.debugLogging": "调试日志记录", "tweakeroo.config.generic.name.darknessScaleOverrideValue": "darknessScaleOverrideValue", "tweakeroo.config.generic.name.elytraCameraIndicator": "鞘翅视角指示器", "tweakeroo.config.generic.name.entityReachDistance": "手长 - 实体触及距离", "tweakeroo.config.generic.name.entityTypeAttackRestrictionWarn": "实体攻击限制 - 警告提示位置", "tweakeroo.config.generic.name.fastBlockPlacementCount": "快速方块放置数量", "tweakeroo.config.generic.name.fastLeftClickAllowTools": "快速左键允许工具", "tweakeroo.config.generic.name.fastLeftClickCount": "快速左键点击次数", "tweakeroo.config.generic.name.fastPlacementRememberOrientation": "快速放置记忆朝向", "tweakeroo.config.generic.name.fastRightClickCount": "快速右键点击次数", "tweakeroo.config.generic.name.fillCloneLimit": "fill clone 指令上限", "tweakeroo.config.generic.name.flexibleBlockPlacementOverlayColor": "灵活放置高亮颜色", "tweakeroo.config.generic.name.flyDecelerationFactor": "飞行减速系数", "tweakeroo.config.generic.name.flySpeedIncrement1": "飞行速度 - 增量1", "tweakeroo.config.generic.name.flySpeedIncrement2": "飞行速度 - 增量2", "tweakeroo.config.generic.name.flySpeedPreset1": "飞行速度 - 预设1", "tweakeroo.config.generic.name.flySpeedPreset2": "飞行速度 - 预设2", "tweakeroo.config.generic.name.flySpeedPreset3": "飞行速度 - 预设3", "tweakeroo.config.generic.name.flySpeedPreset4": "飞行速度 - 预设4", "tweakeroo.config.generic.name.freeCameraPlayerInputs": "灵魂出窍(自由视角) - 玩家左右键", "tweakeroo.config.generic.name.freeCameraPlayerMovement": "灵魂出窍(自由视角) - 玩家移动", "tweakeroo.config.generic.name.gammaOverrideValue": "伽马覆盖值", "tweakeroo.config.generic.name.handRestockPre": "预补货模式", "tweakeroo.config.generic.name.handRestockPreThreshold": "预补货阈值", "tweakeroo.config.generic.name.hangableEntityBypassInverse": "悬挂实体绕过反转", "tweakeroo.config.generic.name.hotbarSlotCycleMax": "快捷栏循环上限", "tweakeroo.config.generic.name.hotbarSlotRandomizerMax": "快捷栏随机上限", "tweakeroo.config.generic.name.hotbarSwapOverlayAlignment": "快捷栏切换界面对齐", "tweakeroo.config.generic.name.hotbarSwapOverlayOffsetX": "快捷栏切换界面X偏移", "tweakeroo.config.generic.name.hotbarSwapOverlayOffsetY": "快捷栏切换界面Y偏移", "tweakeroo.config.generic.name.inventoryPreviewVillagerBGColor": "容器预览 - 村民交易背景色", "tweakeroo.config.generic.name.itemSwapDurabilityThreshold": "物品交换耐久阈值", "tweakeroo.config.generic.name.itemUsePacketCheckBypass": "物品使用数据包检查绕过", "tweakeroo.config.generic.name.mapPreviewRequireShift": "地图预览需按住Shift", "tweakeroo.config.generic.name.mapPreviewSize": "地图预览尺寸", "tweakeroo.config.generic.name.periodicAttackInterval": "周期性攻击间隔", "tweakeroo.config.generic.name.periodicAttackResetIntervalOnActivate": "激活时重置攻击间隔", "tweakeroo.config.generic.name.periodicHoldAttackDuration": "持续攻击 - 时长", "tweakeroo.config.generic.name.periodicHoldAttackInterval": "持续攻击 - 间隔", "tweakeroo.config.generic.name.periodicHoldAttackResetIntervalOnActivate": "攻击间隔 - 激活时重置持续", "tweakeroo.config.generic.name.periodicHoldUseDuration": "持续使用 - 时长", "tweakeroo.config.generic.name.periodicHoldUseInterval": "持续使用 - 间隔", "tweakeroo.config.generic.name.periodicHoldUseResetIntervalOnActivate": "使用间隔 - 激活时重置持续", "tweakeroo.config.generic.name.periodicUseInterval": "周期性使用间隔", "tweakeroo.config.generic.name.periodicUseResetIntervalOnActivate": "激活时重置使用间隔", "tweakeroo.config.generic.name.permanentSneakAllowInGUIs": "永久潜行在打开GUI界面时依然有效", "tweakeroo.config.generic.name.placementGridSize": "放置网格尺寸", "tweakeroo.config.generic.name.placementLimit": "放置数量限制", "tweakeroo.config.generic.name.placementRestrictionMode": "放置限制模式", "tweakeroo.config.generic.name.placementRestrictionTiedToFast": "放置限制与快速模式联动", "tweakeroo.config.generic.name.potionWarningBeneficialOnly": "仅警告有益药水", "tweakeroo.config.generic.name.potionWarningThreshold": "药水警告阈值", "tweakeroo.config.generic.name.rememberFlexibleFromClick": "记忆灵活放置初始点击", "tweakeroo.config.generic.name.renderLimitItem": "物品渲染数量上限", "tweakeroo.config.generic.name.renderLimitXPOrb": "经验球渲染数量上限", "tweakeroo.config.generic.name.sculkSensorPulseLength": "幽匿感测体脉冲时长", "tweakeroo.config.generic.name.selectiveBlocksHideEntities": "隐藏实体设置", "tweakeroo.config.generic.name.selectiveBlocksHideParticles": "隐藏粒子设置", "tweakeroo.config.generic.name.selectiveBlocksNoHit": "禁用隐藏方块选中", "tweakeroo.config.generic.name.selectiveBlocksTrackPistons": "追踪活塞运动", "tweakeroo.config.generic.name.serverDataSyncCacheRefresh": "服务器数据同步缓存刷新", "tweakeroo.config.generic.name.serverDataSyncCacheTimeout": "服务器数据同步缓存超时", "tweakeroo.config.generic.name.serverNbtRequestRate": "服务器NBT请求频率", "tweakeroo.config.generic.name.shulkerDisplayBgColor": "潜影盒 - 背景颜色", "tweakeroo.config.generic.name.shulkerDisplayEnderChest": "潜影盒 - 末影箱显示", "tweakeroo.config.generic.name.shulkerDisplayRequireShift": "潜影盒 - 预览需按住Shift", "tweakeroo.config.generic.name.slotSyncWorkaround": "槽位同步修复", "tweakeroo.config.generic.name.slotSyncWorkaroundAlways": "始终启用槽位同步修复", "tweakeroo.config.generic.name.snapAimIndicator": "对齐视线 - 指示器", "tweakeroo.config.generic.name.snapAimIndicatorColor": "对齐视线 - 指示器颜色", "tweakeroo.config.generic.name.snapAimMode": "对齐视线 - 模式", "tweakeroo.config.generic.name.snapAimOnlyCloseToAngle": "对齐视线 - 仅邻近角度", "tweakeroo.config.generic.name.snapAimPitchOvershoot": "对齐视线 - 俯仰过冲", "tweakeroo.config.generic.name.snapAimPitchStep": "对齐视线 - 俯仰步长", "tweakeroo.config.generic.name.snapAimThresholdPitch": "对齐视线 - 俯仰(上下)阈值", "tweakeroo.config.generic.name.snapAimThresholdYaw": "对齐视线 - 偏航(左右)阈值", "tweakeroo.config.generic.name.snapAimYawStep": "对齐视线 - 偏航步长", "tweakeroo.config.generic.name.structureBlockMaxSize": "结构方块最大尺寸", "tweakeroo.config.generic.name.toolSwapBetterEnchants": "工具切换 - 自适应附魔", "tweakeroo.config.generic.name.toolSwapPreferSilkTouch": "工具切换 - 偏好精准采集", "tweakeroo.config.generic.name.toolSwapBambooUsesSwordFirst": "工具切换 - 竹子优先使用剑", "tweakeroo.config.generic.name.toolSwapNeedsShearsFirst": "工具切换 - 优先使用剪刀", "tweakeroo.config.generic.name.toolSwapSilkTouchFirst": "工具切换 - 优先精准采集", "tweakeroo.config.generic.name.toolSwapSilkTouchOres": "工具切换 - 矿石精准采集", "tweakeroo.config.generic.name.toolSwapSilkTouchOverride": "工具切换 - 优先精准采集的方块列表", "tweakeroo.config.generic.name.toolSwitchIgnoredSlots": "工具切换 - 忽略槽位", "tweakeroo.config.generic.name.toolSwitchableSlots": "工具切换 - 可用槽位", "tweakeroo.config.generic.name.weaponSwapBetterEnchants": "武器切换 - 自适应附魔", "tweakeroo.config.generic.name.zoomAdjustMouseSensitivity": "缩放调整鼠标灵敏度", "tweakeroo.config.generic.name.zoomFov": "缩放视野", "tweakeroo.config.generic.name.zoomResetFovOnActivate": "每次缩放时重置视野值(FOV)", "tweakeroo.config.generic.prettyName.freeCameraPlayerInputs": "灵魂出窍(自由视角) - 玩家左右键", "tweakeroo.config.generic.prettyName.freeCameraPlayerMovement": "灵魂出窍(自由视角) - 玩家移动", "tweakeroo.config.generic.prettyName.toolSwapBetterEnchants": "工具切换 - 自适应附魔", "tweakeroo.config.generic.prettyName.toolSwapBambooUsesSwordFirst": "工具切换 - 竹子优先使用剑", "tweakeroo.config.generic.prettyName.toolSwapNeedsShearsFirst": "工具切换 - 优先使用剪刀", "tweakeroo.config.generic.prettyName.toolSwapSilkTouchFirst": "工具切换 - 优先精准采集", "tweakeroo.config.generic.prettyName.toolSwapPreferSilkTouch": "工具切换 - 偏好精准采集", "tweakeroo.config.generic.prettyName.toolSwapSilkTouchOres": "工具切换 - 矿石精准采集", "tweakeroo.config.generic.prettyName.toolSwapSilkTouchOverride": "工具切换 - 优先精准采集的方块列表", "tweakeroo.config.generic.prettyName.weaponSwapBetterEnchants": "武器切换 - 自适应附魔", "tweakeroo.config.hotkey.comment.accurateBlockPlacementInto": "激活精确方块放置模式及其提示的键，\n用于将方块朝向放置到点击的方块面上", "tweakeroo.config.hotkey.comment.accurateBlockPlacementReverse": "激活精确方块放置模式及其提示的键，\n用于将方块放置到与原本方向相反的方向", "tweakeroo.config.hotkey.comment.areaSelectionAddToList": "将选定方块添加至列表\n由 Andrew54757 在 TweakFork 下编写", "tweakeroo.config.hotkey.comment.areaSelectionOffset": "调整选区偏移量的快捷键\n由 Andrew54757 在 TweakFork 下编写", "tweakeroo.config.hotkey.comment.areaSelectionRemoveFromList": "从列表中移除选定方块\n由 Andrew54757 在 TweakFork 下编写", "tweakeroo.config.hotkey.comment.breakingRestrictionModeColumn": "切换破坏限制模式为列模式(法线方向)\n只能在“最初点击的方块表面”法线方向上破坏", "tweakeroo.config.hotkey.comment.breakingRestrictionModeDiagonal": "切换破坏限制模式为对角线模式(叉号方向)\n只能在“最初破坏的方块与其点击表面”为交点和所在平面的“两条对角线”上破坏", "tweakeroo.config.hotkey.comment.breakingRestrictionModeFace": "切换破坏限制模式为点击面模式(固定方向表面)\n只能在“最初点击的方块表面”相同方向的表面破坏", "tweakeroo.config.hotkey.comment.breakingRestrictionModeLayer": "切换破坏限制模式为层状模式(固定y值)\n只能在“最初破坏的方块”相同的y高度破坏", "tweakeroo.config.hotkey.comment.breakingRestrictionModeLine": "切换破坏限制模式为十字模式(十字方向)\n只能在“最初破坏的方块与其点击表面”为交点和所在平面的“两条坐标轴线”上破坏", "tweakeroo.config.hotkey.comment.breakingRestrictionModePlane": "切换破坏限制模式为平面模式\n只能在“最初破坏的方块”相同的y高度破坏", "tweakeroo.config.hotkey.comment.copySignText": "复制已放置告示牌的文本内容\n可与tweakSignCopy功能配合使用", "tweakeroo.config.hotkey.comment.elytraCamera": "锁定玩家实际视角方向，允许通过输入设备（鼠标）\n单独控制仅用于渲染的「相机视角」\n专为鞘翅飞行时自由观察周围环境设计", "tweakeroo.config.hotkey.comment.flexibleBlockPlacementAdjacent": "激活精确方块放置模式及其提示的键，\n用于将方块放置在相邻的位置", "tweakeroo.config.hotkey.comment.flexibleBlockPlacementOffset": "激活精确方块放置模式及其提示的键，\n用于将方块放置在“对边”位置", "tweakeroo.config.hotkey.comment.flexibleBlockPlacementRotation": "激活精确方块放置模式及其提示的键，\n用于调整放置方块的旋转方向/朝向", "tweakeroo.config.hotkey.comment.flyIncrement1": "使用“增量1”改变飞行速度", "tweakeroo.config.hotkey.comment.flyIncrement2": "使用“增量2”改变飞行速度", "tweakeroo.config.hotkey.comment.flyPreset1": "切换至飞行速度预设方案1", "tweakeroo.config.hotkey.comment.flyPreset2": "切换至飞行速度预设方案2", "tweakeroo.config.hotkey.comment.flyPreset3": "切换至飞行速度预设方案3", "tweakeroo.config.hotkey.comment.flyPreset4": "切换至飞行速度预设方案4", "tweakeroo.config.hotkey.comment.freeCameraPlayerInputs": "切换「通用」中的「灵魂出窍(自由视角) - 玩家左右键」选项", "tweakeroo.config.hotkey.comment.freeCameraPlayerMovement": "切换「通用」中的「灵魂出窍(自由视角) - 玩家移动」选项", "tweakeroo.config.hotkey.comment.hotbarScroll": "按住此键可通过背包行滚动切换快捷栏", "tweakeroo.config.hotkey.comment.hotbarSwap1": "将快捷栏与背包第一行（最顶行）物品交换", "tweakeroo.config.hotkey.comment.hotbarSwap2": "将快捷栏与背包第二行（中间行）物品交换", "tweakeroo.config.hotkey.comment.hotbarSwap3": "将快捷栏与背包第三行（最底行）物品交换", "tweakeroo.config.hotkey.comment.hotbarSwapBase": "显示快捷栏/背包交换界面的基础按键", "tweakeroo.config.hotkey.comment.inventoryPreview": "激活容器预览功能的快捷键", "tweakeroo.config.hotkey.comment.inventoryPreviewToggleScreen": "打开容器预览的可交互界面\n在此界面下可使用鼠标查看物品的详情信息", "tweakeroo.config.hotkey.comment.openConfigGui": "打开游戏内配置界面的快捷键", "tweakeroo.config.hotkey.comment.placementRestrictionModeColumn": "切换放置限制模式为列模式(法线方向)\n只能在“最初点击的方块表面”法线方向上放置", "tweakeroo.config.hotkey.comment.placementRestrictionModeDiagonal": "切换放置限制模式为对角线模式(叉号方向)\n只能在“最初放置的方块与其点击表面”为交点和所在平面的“两条对角线”上放置", "tweakeroo.config.hotkey.comment.placementRestrictionModeFace": "切换放置限制模式为点击面模式(固定方向表面)\n只能在“最初点击的方块表面”相同方向的表面放置", "tweakeroo.config.hotkey.comment.placementRestrictionModeLayer": "切换放置限制模式为层状模式(固定y值)\n只能在“最初放置的方块”相同的y高度放置", "tweakeroo.config.hotkey.comment.placementRestrictionModeLine": "切换放置限制模式为十字模式(十字方向)\n只能在“最初放置的方块与其点击表面”为交点和所在平面的“两条垂线”上放置", "tweakeroo.config.hotkey.comment.placementRestrictionModePlane": "切换放置限制模式为平面模式\n只能在“最初放置的方块”相同的y高度放置", "tweakeroo.config.hotkey.comment.placementYMirror": "在目标方块内部镜像Y轴坐标进行放置(作用于台阶、楼梯等)", "tweakeroo.config.hotkey.comment.playerInventoryPeek": "激活玩家背包预览功能的快捷键", "tweakeroo.config.hotkey.comment.sitDownNearbyPets": "使附近所有宠物进入坐下状态", "tweakeroo.config.hotkey.comment.skipAllRendering": "切换是否跳过所有渲染过程", "tweakeroo.config.hotkey.comment.skipWorldRendering": "切换是否跳过世界渲染", "tweakeroo.config.hotkey.comment.standUpNearbyPets": "使附近所有宠物恢复站立状态", "tweakeroo.config.hotkey.comment.swapElytraChestplate": "在胸甲槽位的鞘翅与胸甲之间快速切换", "tweakeroo.config.hotkey.comment.toggleAccuratePlacementProtocol": "切换「通用」中的「精准放置协议」选项状态", "tweakeroo.config.hotkey.comment.toggleGrabCursor": "根据当前状态锁定或释放鼠标光标", "tweakeroo.config.hotkey.comment.toolPick": "自动切换为适合破坏目标方块的有效工具", "tweakeroo.config.hotkey.comment.writeMapsAsImages": "将当前所有地图导出为图片文件\n保存至「config/tweakeroo/map_images/<世界名称>/」目录", "tweakeroo.config.hotkey.comment.zoomActivate": "缩放激活快捷键\n由 Andrew54757 在 TweakFork 下编写", "tweakeroo.config.hotkey.name.accurateBlockPlacementInto": "精准方块放置 - 内部朝向", "tweakeroo.config.hotkey.name.accurateBlockPlacementReverse": "精准方块放置 - 反向朝向", "tweakeroo.config.hotkey.name.areaSelectionAddToList": "添加选区到列表", "tweakeroo.config.hotkey.name.areaSelectionOffset": "选区偏移调整", "tweakeroo.config.hotkey.name.areaSelectionRemoveFromList": "从列表移除选区", "tweakeroo.config.hotkey.name.breakingRestrictionModeColumn": "破坏限制模式 - 列(法线方向)", "tweakeroo.config.hotkey.name.breakingRestrictionModeDiagonal": "破坏限制模式 - 对角线(叉号方向)", "tweakeroo.config.hotkey.name.breakingRestrictionModeFace": "破坏限制模式 - 点击面(固定方向表面)", "tweakeroo.config.hotkey.name.breakingRestrictionModeLayer": "破坏限制模式 - 层状(固定y值)", "tweakeroo.config.hotkey.name.breakingRestrictionModeLine": "破坏限制模式 - 十字", "tweakeroo.config.hotkey.name.breakingRestrictionModePlane": "破坏限制模式 - 平面", "tweakeroo.config.hotkey.name.copySignText": "复制告示牌文本", "tweakeroo.config.hotkey.name.elytraCamera": "鞘翅飞行视角", "tweakeroo.config.hotkey.name.flexibleBlockPlacementAdjacent": "灵活方块放置 - 相邻位置", "tweakeroo.config.hotkey.name.flexibleBlockPlacementOffset": "灵活方块放置 - 偏移位置", "tweakeroo.config.hotkey.name.flexibleBlockPlacementRotation": "灵活方块放置 - 旋转朝向", "tweakeroo.config.hotkey.name.flyIncrement1": "飞行速度 - 增量1", "tweakeroo.config.hotkey.name.flyIncrement2": "飞行速度 - 增量2", "tweakeroo.config.hotkey.name.flyPreset1": "飞行预设1", "tweakeroo.config.hotkey.name.flyPreset2": "飞行预设2", "tweakeroo.config.hotkey.name.flyPreset3": "飞行预设3", "tweakeroo.config.hotkey.name.flyPreset4": "飞行预设4", "tweakeroo.config.hotkey.name.freeCameraPlayerInputs": "灵魂出窍(自由视角) - 玩家左右键", "tweakeroo.config.hotkey.name.freeCameraPlayerMovement": "灵魂出窍(自由视角) - 玩家移动", "tweakeroo.config.hotkey.name.hotbarScroll": "快捷栏滚动", "tweakeroo.config.hotkey.name.hotbarSwap1": "快捷栏交换 - 第一行", "tweakeroo.config.hotkey.name.hotbarSwap2": "快捷栏交换 - 第二行", "tweakeroo.config.hotkey.name.hotbarSwap3": "快捷栏交换 - 第三行", "tweakeroo.config.hotkey.name.hotbarSwapBase": "快捷栏交换 - 基础按键", "tweakeroo.config.hotkey.name.inventoryPreview": "容器预览 - 快捷键", "tweakeroo.config.hotkey.name.inventoryPreviewToggleScreen": "容器预览 - 可交互界面", "tweakeroo.config.hotkey.name.openConfigGui": "打开配置界面", "tweakeroo.config.hotkey.name.placementRestrictionModeColumn": "放置限制模式 - 列(法线方向)", "tweakeroo.config.hotkey.name.placementRestrictionModeDiagonal": "放置限制模式 - 对角线(叉号方向)", "tweakeroo.config.hotkey.name.placementRestrictionModeFace": "放置限制模式 - 点击面(固定方向表面)", "tweakeroo.config.hotkey.name.placementRestrictionModeLayer": "放置限制模式 - 层状(固定y值)", "tweakeroo.config.hotkey.name.placementRestrictionModeLine": "放置限制模式 - 十字", "tweakeroo.config.hotkey.name.placementRestrictionModePlane": "放置限制模式 - 平面", "tweakeroo.config.hotkey.name.placementYMirror": "Y轴镜像放置(台阶、楼梯等)", "tweakeroo.config.hotkey.name.playerInventoryPeek": "玩家背包预览", "tweakeroo.config.hotkey.name.sitDownNearbyPets": "附近宠物坐下", "tweakeroo.config.hotkey.name.skipAllRendering": "跳过全部渲染", "tweakeroo.config.hotkey.name.skipWorldRendering": "跳过世界渲染", "tweakeroo.config.hotkey.name.standUpNearbyPets": "附近宠物站立", "tweakeroo.config.hotkey.name.swapElytraChestplate": "切换鞘翅与胸甲", "tweakeroo.config.hotkey.name.toggleAccuratePlacementProtocol": "开关 - 精准放置协议", "tweakeroo.config.hotkey.name.toggleGrabCursor": "开关 - 光标锁定", "tweakeroo.config.hotkey.name.toolPick": "工具切换", "tweakeroo.config.hotkey.name.writeMapsAsImages": "导出地图为图片", "tweakeroo.config.hotkey.name.zoomActivate": "缩放激活", "tweakeroo.config.internal.comment.darknessScaleValueOriginal": "The original darkness scale value, before the 'tweak darkness visibility' was enabled", "tweakeroo.config.internal.comment.flySpeedPreset": "此值仅供模组内部使用，用于追踪\n当前选择的飞行速度预设方案", "tweakeroo.config.internal.comment.gammaValueOriginal": "伽马覆盖功能启用前的原始亮度值", "tweakeroo.config.internal.comment.hotbarScrollCurrentRow": "此值仅供模组内部使用，用于追踪\n快捷栏滚动功能的「当前快捷栏行」状态", "tweakeroo.config.internal.comment.slimeBlockSlipperinessOriginal": "粘液块原本的滑动系数值", "tweakeroo.config.internal.comment.snapAimLastPitch": "最后一次对齐视线时的俯仰角数值", "tweakeroo.config.internal.comment.snapAimLastYaw": "最后一次对齐视线时的偏航角数值", "tweakeroo.config.internal.name.darknessScaleValueOriginal": "darknessScaleValueOriginal", "tweakeroo.config.internal.name.flySpeedPreset": "飞行速度预设", "tweakeroo.config.internal.name.gammaValueOriginal": "伽马值原始值", "tweakeroo.config.internal.name.hotbarScrollCurrentRow": "快捷栏滚动当前行", "tweakeroo.config.internal.name.slimeBlockSlipperinessOriginal": "粘液块原始滑度", "tweakeroo.config.internal.name.snapAimLastPitch": "上一次对齐视线的俯仰角", "tweakeroo.config.internal.name.snapAimLastYaw": "上一次对齐视线的偏航角", "tweakeroo.config.lists.comment.blockTypeBreakRestrictionBlackList": "当启用方块破坏限制且列表类型为黑名单时，\n禁止破坏此处列出的方块类型", "tweakeroo.config.lists.comment.blockTypeBreakRestrictionListType": "方块破坏限制功能的「列表」设置", "tweakeroo.config.lists.comment.blockTypeBreakRestrictionWhiteList": "当启用方块破坏限制且列表类型为白名单时，\n仅允许破坏此处列出的方块类型", "tweakeroo.config.lists.comment.creativeExtraItems": "将额外物品添加至创造模式物品栏。\n当前这些物品会出现在运输类目下，\n未来将支持自定义物品分组。", "tweakeroo.config.lists.comment.entityTypeAttackRestrictionBlackList": "当启用实体攻击限制且列表类型为黑名单时，\n禁止攻击此处列出的实体类型", "tweakeroo.config.lists.comment.entityTypeAttackRestrictionListType": "实体攻击限制功能的列表类型设置", "tweakeroo.config.lists.comment.entityTypeAttackRestrictionWhiteList": "当启用实体攻击限制且列表类型为白名单时，\n仅允许攻击此处列出的实体类型", "tweakeroo.config.lists.comment.entityWeaponMapping": "武器自动切换功能的实体-武器映射配置。\n'<default>'表示未定义映射时的默认武器\n'<ignore>'表示不触发武器切换", "tweakeroo.config.lists.comment.fastPlacementItemBlackList": "当快速放置物品列表类型为黑名单时，\n禁止使用此处列出的物品进行快速放置", "tweakeroo.config.lists.comment.fastPlacementItemListType": "快速方块放置功能的物品限制类型", "tweakeroo.config.lists.comment.fastPlacementItemWhiteList": "当快速放置物品列表类型为白名单时，\n仅允许使用此处列出的物品进行快速放置", "tweakeroo.config.lists.comment.fastRightClickBlackList": "当快速右键物品列表类型为黑名单时，\n禁止使用此处列出的物品进行快速右键", "tweakeroo.config.lists.comment.fastRightClickBlockBlackList": "当快速右键方块列表类型为黑名单时，\n禁止对此处列出的方块使用快速右键", "tweakeroo.config.lists.comment.fastRightClickBlockListType": "快速右键功能的方块目标限制类型", "tweakeroo.config.lists.comment.fastRightClickBlockWhiteList": "当快速右键方块列表类型为白名单时，\n仅允许对此处列出的方块使用快速右键", "tweakeroo.config.lists.comment.fastRightClickListType": "快速右键功能的物品限制类型", "tweakeroo.config.lists.comment.fastRightClickWhiteList": "当快速右键物品列表类型为白名单时，\n仅允许使用此处列出的物品进行快速右键", "tweakeroo.config.lists.comment.flatWorldPresets": "自定义超平坦世界预设格式：\n名称;方块字符串;生物群系;生成特征;图标物品\n方块字符串使用原版格式，例如：62*minecraft:dirt,minecraft:grass\n生物群系可使用注册名或数字ID\n图标物品格式示例：minecraft:iron_nugget", "tweakeroo.config.lists.comment.handRestockBlackList": "当手动补货列表类型为黑名单时，\n禁止对此处列出的物品进行补货", "tweakeroo.config.lists.comment.handRestockListType": "手动补货功能的列表类型设置", "tweakeroo.config.lists.comment.handRestockWhiteList": "当手动补货列表类型为白名单时，\n仅允许对此处列出的物品进行补货", "tweakeroo.config.lists.comment.potionWarningBlackList": "此处列出的药水效果将不会触发警告", "tweakeroo.config.lists.comment.potionWarningListType": "药水效果警告的列表类型设置", "tweakeroo.config.lists.comment.potionWarningWhiteList": "仅此处列出的药水效果会触发警告", "tweakeroo.config.lists.comment.silkTouchOverride": "使用工具切换时，可以将方块或方块标签添加到此列表，以应用优先精准采集(SilkTouchFirst)。\n还在启用“toolSwapSilkTouchOverride”的情况下，使用基于“§b#malilib:needs_silk_touch§r”方块标签的“toolSwapSilkTouchFirst”方法。\n\n一个有用的例子是将“§bminecraft:stone§r”添加到此列表。\n\n由于已存在一些方块标签，因此您无需将数十个方块添加到这样的列表中。\n但是，您可以通过关闭“工具切换 -  优先精准采集(toolSwapSilkTouchFirst)”，\n并仅启用“工具切换 -  优先精准采集的方块列表(toolSwapSilkTouchOverride)”来配置此列表以使其工作。\n然后，您可能希望在此处使用一些现有的“§b#malilib:§r”或“§b#minecraft:§r”方块标签；\n例如，添加“§b#malilib:glass_blocks§r”，而不是逐个输入所有18种玻璃。\n\n提示：\n优先精准采集(SilkTouchFirst)：精准采集绝对优先\n偏好精准采集(PreferSilkTouch)：精准采集是次要的条件，其他条件比较后都一样才会考虑优先精准采集", "tweakeroo.config.lists.comment.repairModeSlots": "设置修复模式生效的装备槽位\n有效值：\n  主手(mainhand)，副手(offhand)，\n  头部(head)，胸部(chest)，腿部(legs)，脚部(feet)", "tweakeroo.config.lists.comment.selectiveBlocksBlacklist": "需要屏蔽的方块位置列表\n由 Andrew54757 在 TweakFork 下编写", "tweakeroo.config.lists.comment.selectiveBlocksListType": "选择性方块功能的列表类型\n由 Andrew54757 在 TweakFork 下编写", "tweakeroo.config.lists.comment.selectiveBlocksWhitelist": "需要显示的方块位置列表\n由 Andrew54757 在 TweakFork 下编写", "tweakeroo.config.lists.comment.unstackingItems": "启用防拆堆保护物品(tweakItemUnstackingProtection)功能时\n需要保护的防拆堆物品的列表", "tweakeroo.config.lists.name.blockTypeBreakRestrictionBlackList": "方块破坏限制 - 黑名单", "tweakeroo.config.lists.name.blockTypeBreakRestrictionListType": "方块破坏限制 - 列表类型", "tweakeroo.config.lists.name.blockTypeBreakRestrictionWhiteList": "方块破坏限制 - 白名单", "tweakeroo.config.lists.name.creativeExtraItems": "创造模式额外物品", "tweakeroo.config.lists.name.entityTypeAttackRestrictionBlackList": "实体攻击限制 - 黑名单", "tweakeroo.config.lists.name.entityTypeAttackRestrictionListType": "实体攻击限制 - 列表类型", "tweakeroo.config.lists.name.entityTypeAttackRestrictionWhiteList": "实体攻击限制 - 白名单", "tweakeroo.config.lists.name.entityWeaponMapping": "实体武器映射", "tweakeroo.config.lists.name.fastPlacementItemBlackList": "快速放置 - 物品黑名单", "tweakeroo.config.lists.name.fastPlacementItemListType": "快速放置 - 物品列表类型", "tweakeroo.config.lists.name.fastPlacementItemWhiteList": "快速放置 - 物品白名单", "tweakeroo.config.lists.name.fastRightClickBlackList": "快速右键 - 物品黑名单", "tweakeroo.config.lists.name.fastRightClickBlockBlackList": "快速右键 - 方块黑名单", "tweakeroo.config.lists.name.fastRightClickBlockListType": "快速右键 - 方块列表类型", "tweakeroo.config.lists.name.fastRightClickBlockWhiteList": "快速右键 - 方块白名单", "tweakeroo.config.lists.name.fastRightClickListType": "快速右键 - 物品列表类型", "tweakeroo.config.lists.name.fastRightClickWhiteList": "快速右键 - 物品白名单", "tweakeroo.config.lists.name.flatWorldPresets": "超平坦世界预设", "tweakeroo.config.lists.name.handRestockBlackList": "手动补货 - 黑名单", "tweakeroo.config.lists.name.handRestockListType": "手动补货 - 列表类型", "tweakeroo.config.lists.name.handRestockWhiteList": "手动补货 - 白名单", "tweakeroo.config.lists.name.potionWarningBlackList": "药水警告 - 黑名单", "tweakeroo.config.lists.name.potionWarningListType": "药水警告 - 列表类型", "tweakeroo.config.lists.name.potionWarningWhiteList": "药水警告 - 白名单", "tweakeroo.config.lists.name.silkTouchOverride": "工具切换 - 优先精准采集的方块列表", "tweakeroo.config.lists.name.repairModeSlots": "修复模式槽位", "tweakeroo.config.lists.name.selectiveBlocksBlacklist": "选择性方块 - 黑名单", "tweakeroo.config.lists.name.selectiveBlocksListType": "选择性方块 - 列表类型", "tweakeroo.config.lists.name.selectiveBlocksWhitelist": "选择性方块 - 白名单", "tweakeroo.config.lists.name.unstackingItems": "防拆堆保护物品", "tweakeroo.gui.button.config_gui.disables": "禁用项", "tweakeroo.gui.button.config_gui.fixes": "修复项", "tweakeroo.gui.button.config_gui.generic": "通用", "tweakeroo.gui.button.config_gui.generic_hotkeys": "通用快捷键", "tweakeroo.gui.button.config_gui.generic_config_hotkeys": "通用快捷键", "tweakeroo.gui.button.config_gui.lists": "列表", "tweakeroo.gui.button.config_gui.placement": "放置相关", "tweakeroo.gui.button.config_gui.tweaks": "功能开关", "tweakeroo.gui.button.misc.command_block.hover.update_execution": "是否允许每游戏刻多次触发命令", "tweakeroo.gui.button.misc.command_block.set_name": "设置名称", "tweakeroo.gui.button.misc.command_block.update_execution.looping": "循环中", "tweakeroo.gui.button.misc.command_block.update_execution.off": "循环执行: §c禁用", "tweakeroo.gui.button.misc.command_block.update_execution.on": "循环执行: §a启用", "tweakeroo.gui.label.easy_place_protocol.auto": "自动", "tweakeroo.gui.label.easy_place_protocol.none": "无", "tweakeroo.gui.label.easy_place_protocol.slabs_only": "仅限半砖(Paper服)", "tweakeroo.gui.label.easy_place_protocol.v2": "v2(Carpet支持)", "tweakeroo.gui.label.easy_place_protocol.v3": "v3(单人或Servux支持)", "tweakeroo.gui.title.configs": "Tweakeroo 配置界面 - %s §l汉化 §rby §9DeepSeek-R1 §r& §6金合欢酱(acaciachan)", "tweakeroo.hotkeys.category.disable_toggle_hotkeys": "禁用切换快捷键", "tweakeroo.hotkeys.category.generic_hotkeys": "通用快捷键", "tweakeroo.hotkeys.category.tweak_toggle_hotkeys": "功能切换快捷键", "tweakeroo.label.config_comment.single_player_only": "§6注意：此功能仅在单机模式完全生效§r", "tweakeroo.label.placement_restriction_mode.column": "列(法线方向)", "tweakeroo.label.placement_restriction_mode.diagonal": "对角线(叉号方形)", "tweakeroo.label.placement_restriction_mode.face": "点击面(固定方向表面", "tweakeroo.label.placement_restriction_mode.layer": "层状(固定y值)", "tweakeroo.label.placement_restriction_mode.line": "十字", "tweakeroo.label.placement_restriction_mode.plane": "平面", "tweakeroo.label.snap_aim_mode.both": "偏航 & 俯仰", "tweakeroo.label.snap_aim_mode.pitch": "俯仰角", "tweakeroo.label.snap_aim_mode.yaw": "偏航角", "tweakeroo.message.death_coordinates": "§6死亡坐标§r @ [§b%d, %d, %d§r] 位于 §a%s", "tweakeroo.message.focusing_game": "§6已锁定§f游戏光标（进入操作模式）", "tweakeroo.message.potion_effects_running_out": "§6!! 警告 - %s种药水效果将在%s秒后失效 !!§r", "tweakeroo.message.repair_mode.swapped_repairable_item_to_slot": "已将可修复物品切换至%s号槽位", "tweakeroo.message.set_after_clicker_count_to": "后置点击器次数设置为%s", "tweakeroo.message.set_breaking_grid_size_to": "破坏网格间隔设置为%s", "tweakeroo.message.set_breaking_restriction_mode_to": "破坏限制模式设置为%s", "tweakeroo.message.set_fly_speed_preset_to": "已切换至飞行速度预设%s（速度：%s）", "tweakeroo.message.set_fly_speed_to": "预设%s的飞行速度已设为%s", "tweakeroo.message.set_hotbar_slot_cycle_max_to": "快捷栏循环最大槽位设为%s", "tweakeroo.message.set_hotbar_slot_randomizer_max_to": "快捷栏随机最大槽位设为%s", "tweakeroo.message.set_periodic_attack_interval_to": "周期性攻击间隔设为%s", "tweakeroo.message.set_periodic_hold_attack_interval_to": "周期性长按攻击间隔设为%s", "tweakeroo.message.set_periodic_hold_use_interval_to": "周期性长按使用间隔设为%s", "tweakeroo.message.set_periodic_use_interval_to": "周期性使用间隔设为%s", "tweakeroo.message.set_placement_grid_size_to": "放置网格间隔设为%s", "tweakeroo.message.set_placement_limit_to": "放置数量限制设为%s", "tweakeroo.message.set_placement_restriction_mode_to": "放置限制模式设为%s", "tweakeroo.message.set_snap_aim_pitch_step_to": "精准视角俯仰步长设为%s", "tweakeroo.message.set_snap_aim_yaw_step_to": "精准视角偏航步长设为%s", "tweakeroo.message.set_zoom_fov_to": "视角缩放视野设为%s", "tweakeroo.message.sign_text_copied": "告示牌文本已复制", "tweakeroo.message.snapped_to_pitch": "已锁定俯仰角至%s", "tweakeroo.message.snapped_to_yaw": "已锁定偏航角至%s", "tweakeroo.message.swapped_low_durability_item_for_better_durability": "已将低耐久物品替换为更高耐久的物品", "tweakeroo.message.swapped_low_durability_item_for_dummy_item": "已将低耐久物品替换为临时物品", "tweakeroo.message.swapped_low_durability_item_off_players_hand": "已将低耐久物品移出玩家手持槽位", "tweakeroo.message.toggled": "已切换%s %s", "tweakeroo.message.toggled_after_clicker_on": "已启用后置点击器功能%s，点击次数：%s", "tweakeroo.message.toggled_breaking_grid_on": "已启用破坏网格功能%s，网格间隔：%s", "tweakeroo.message.toggled_fast_placement_mode_on": "已启用快速放置模式%s，模式：%s", "tweakeroo.message.toggled_fly_speed_on": "已启用飞行速度调节%s，预设：%s，速度：%s", "tweakeroo.message.toggled_periodic": "已切换%s %s，速度：%s", "tweakeroo.message.toggled_placement_grid_on": "已启用放置网格功能%s，网格间隔：%s", "tweakeroo.message.toggled_placement_limit_on": "已启用放置数量限制%s，限制：%s", "tweakeroo.message.toggled_slot_cycle_on": "已启用快捷栏循环功能%s，最大槽位：%s", "tweakeroo.message.toggled_slot_randomizer_on": "已启用快捷栏随机功能%s，最大槽位：%s", "tweakeroo.message.toggled_snap_aim_on_both": "已启用双轴快速瞄准锁定%s，偏航步长%s，俯仰步长%s", "tweakeroo.message.toggled_snap_aim_on_pitch": "已启用俯仰精准视角锁定%s，步长%s度", "tweakeroo.message.toggled_snap_aim_on_yaw": "已启用偏航精准视角锁定%s，步长%s度", "tweakeroo.message.toggled_zoom_activate_off": "视角缩放已关闭，视野：%s", "tweakeroo.message.toggled_zoom_activate_on": "视角缩放已激活，视野：%s", "tweakeroo.message.toggled_zoom_on": "已切换视角缩放%s，视野：%s", "tweakeroo.message.unfocusing_game": "§6取消聚焦§f游戏（释放光标）", "tweakeroo.message.value.off": "关闭", "tweakeroo.message.value.on": "开启", "tweakeroo.message.warning.block_type_break_restriction": "§6根据方块类型破坏限制，操作已被阻止", "tweakeroo.message.warning.entity_type_attack_restriction": "§6根据实体类型攻击限制，操作已被阻止"}