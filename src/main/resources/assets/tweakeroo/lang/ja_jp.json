{"modmenu.descriptionTranslation.tweakeroo": "Adds a bunch of configurable client-side tweaks", "tweakeroo.config.disable.comment.disableArmorStandRendering": "Disables all Armor Stand entity rendering", "tweakeroo.config.disable.comment.disableAtmosphericFog": "Disables all Atmospheric Fog that exists\nbetween the player and the Render Distance Fog.", "tweakeroo.config.disable.comment.disableAxeStripping": "Disables stripping logs with an axe", "tweakeroo.config.disable.comment.disableBatSpawning": "Disables Bat spawning in single player", "tweakeroo.config.disable.comment.disableBeaconBeamRendering": "Disables Beacon beam rendering", "tweakeroo.config.disable.comment.disableBlockBreakCooldown": "Disables the block breaking cooldown,\nfor the time taken in between breaking\nblocks", "tweakeroo.config.disable.comment.disableBlockBreakingParticles": "Removes the block breaking particles.\n(This is originally from usefulmod by nessie.)", "tweakeroo.config.disable.comment.disableBossBar": "Disables boss bar rendering", "tweakeroo.config.disable.comment.disableBossFog": "Removes the fog that boss mobs cause", "tweakeroo.config.disable.comment.disableChunkRendering": "Disables chunk (re-)rendering. This will make any block changes non-visible\nuntil this is disabled again and F3 + A is used to refresh the world rendering.\nThis might help with low fps in places with lots of block changes in some situations,\nwhere the block changes are not really relevant at that time.", "tweakeroo.config.disable.comment.disableClientEntityUpdates": "Disables ALL except player entity updates on the client.\nThis is mainly meant for situations where you need to be\nable to do stuff to fix excessive entity count related problems", "tweakeroo.config.disable.comment.disableClientLightUpdates": "Disables all client-side light updates", "tweakeroo.config.disable.comment.disableConstantChunkSaving": "Disables the game saving up to 20 chunks every tick\nall the time, in addition to the normal auto-save cycle.", "tweakeroo.config.disable.comment.disableCreativeMenuInfestedBlocks": "Removes infested stone blocks from the creative search inventory", "tweakeroo.config.disable.comment.disableDeadMobRendering": "Prevents rendering dead mobs (entities that are at 0 health)", "tweakeroo.config.disable.comment.disableDeadMobTargeting": "Prevents targeting entities that are at 0 health.\nThis fixes for example hitting already dead mobs.", "tweakeroo.config.disable.comment.disableDoubleTapSprint": "Disables the double-tap-forward-key sprinting", "tweakeroo.config.disable.comment.disableEntityRendering": "Disables ALL except player entity rendering.\nThis is mainly meant for situations where you need to be\nable to do stuff to fix excessive entity count related problems", "tweakeroo.config.disable.comment.disableEntityTicking": "Prevent everything except player entities from getting ticked", "tweakeroo.config.disable.comment.disableFallingBlockEntityRendering": "If enabled, then falling block entities won't be rendered at all", "tweakeroo.config.disable.comment.disableFirstPersonEffectParticles": "Removes the potion effect particles/swirlies in first person\n(from the player itself)", "tweakeroo.config.disable.comment.disableInventoryEffectRendering": "Removes the potion effect rendering from the inventory GUIs", "tweakeroo.config.disable.comment.disableItemSwitchRenderCooldown": "If true, then there won't be any cooldown/equip animation\nwhen switching the held item or using the item.", "tweakeroo.config.disable.comment.disableMobSpawnerMobRendering": "Removes the entity rendering from mob spawners", "tweakeroo.config.disable.comment.disableNauseaEffect": "Disables the nausea visual effects", "tweakeroo.config.disable.comment.disableNetherFog": "Removes the fog in the Nether", "tweakeroo.config.disable.comment.disableNetherPortalSound": "Disables the Nether Portal sound", "tweakeroo.config.disable.comment.disableObserver": "Disable Observers from triggering at all", "tweakeroo.config.disable.comment.disableOffhandRendering": "Disables the offhand item from getting rendered", "tweakeroo.config.disable.comment.disableParticles": "Disables all particles", "tweakeroo.config.disable.comment.disablePortalGuiClosing": "If enabled, then you can still open GUIs while in a Nether Portal", "tweakeroo.config.disable.comment.disableRainEffects": "Disables rain rendering and sounds", "tweakeroo.config.disable.comment.disableRenderDistanceFog": "Disables the fog that increases around the render distance", "tweakeroo.config.disable.comment.disableRenderingScaffolding": "Disables rendering of Scaffolding Blocks", "tweakeroo.config.disable.comment.disableScoreboardRendering": "Removes the sidebar scoreboard rendering", "tweakeroo.config.disable.comment.disableShovelPathing": "Disables converting grass etc. to Path Blocks with a shovel", "tweakeroo.config.disable.comment.disableShulkerBoxTooltip": "Disables the vanilla text tooltip for Shulker Box contents", "tweakeroo.config.disable.comment.disableSignGui": "Prevent the Sign edit GUI from opening", "tweakeroo.config.disable.comment.disableSkyDarkness": "Disables the sky darkness below y = 63\n\n(By moving the threshold y to 2 blocks below the bottom of the world instead)", "tweakeroo.config.disable.comment.disableSlimeBlockSlowdown": "Removes the slowdown from walking on Slime Blocks.\n(This is originally from usefulmod by nessie.)", "tweakeroo.config.disable.comment.disableStatusEffectHud": "Disables the status effect HUD rendering (which is usually\nin the top right corner of the screen)", "tweakeroo.config.disable.comment.disableTileEntityRendering": "Prevents all TileEntity renderers from rendering", "tweakeroo.config.disable.comment.disableTileEntityTicking": "Prevent all TileEntities from getting ticked", "tweakeroo.config.disable.comment.disableVillagerTradeLocking": "Prevents villager trades from ever locking, by always incrementing\nthe max uses as well when the recipe uses is incremented", "tweakeroo.config.disable.comment.disableWallUnsprint": "Touching a wall doesn't drop you out from sprint mode", "tweakeroo.config.disable.comment.disableWorldViewBob": "Disables the view bob wobble effect of the world, but not the hand\nThis setting will fail if you have <PERSON> installed.", "tweakeroo.config.disable.name.disableArmorStandRendering": "disableArmorStandRendering", "tweakeroo.config.disable.name.disableAtmosphericFog": "disableAtmosphericFog", "tweakeroo.config.disable.name.disableAxeStripping": "disableAxeStripping", "tweakeroo.config.disable.name.disableBatSpawning": "disableBatSpawning", "tweakeroo.config.disable.name.disableBeaconBeamRendering": "disableBeaconBeamRendering", "tweakeroo.config.disable.name.disableBlockBreakCooldown": "disableBlockBreakCooldown", "tweakeroo.config.disable.name.disableBlockBreakingParticles": "disableBlockBreakingParticles", "tweakeroo.config.disable.name.disableBossBar": "disable<PERSON><PERSON><PERSON><PERSON>", "tweakeroo.config.disable.name.disableBossFog": "disable<PERSON>oss<PERSON><PERSON>", "tweakeroo.config.disable.name.disableChunkRendering": "disableChun<PERSON><PERSON><PERSON>ing", "tweakeroo.config.disable.name.disableClientEntityUpdates": "disableClientEntityUpdates", "tweakeroo.config.disable.name.disableClientLightUpdates": "disableClientLightUpdates", "tweakeroo.config.disable.name.disableConstantChunkSaving": "disableConstantChunkSaving", "tweakeroo.config.disable.name.disableCreativeMenuInfestedBlocks": "disableCreativeMenuInfestedBlocks", "tweakeroo.config.disable.name.disableDeadMobRendering": "disableDeadMobRendering", "tweakeroo.config.disable.name.disableDeadMobTargeting": "disableDeadMobTargeting", "tweakeroo.config.disable.name.disableDoubleTapSprint": "disableDoubleTapSprint", "tweakeroo.config.disable.name.disableEntityRendering": "disableEntityRendering", "tweakeroo.config.disable.name.disableEntityTicking": "disableEntityTicking", "tweakeroo.config.disable.name.disableFallingBlockEntityRendering": "disableFallingBlockEntityRendering", "tweakeroo.config.disable.name.disableFirstPersonEffectParticles": "disableFirstPersonEffectParticles", "tweakeroo.config.disable.name.disableInventoryEffectRendering": "disableInventoryEffectRendering", "tweakeroo.config.disable.name.disableItemSwitchRenderCooldown": "disableItemSwitchRenderCooldown", "tweakeroo.config.disable.name.disableMobSpawnerMobRendering": "disableMobSpawnerMobRendering", "tweakeroo.config.disable.name.disableNauseaEffect": "disableNauseaE<PERSON>ct", "tweakeroo.config.disable.name.disableNetherFog": "disableNetherFog", "tweakeroo.config.disable.name.disableNetherPortalSound": "disableNetherPortalSound", "tweakeroo.config.disable.name.disableObserver": "disableObserver", "tweakeroo.config.disable.name.disableOffhandRendering": "disableOffhandRendering", "tweakeroo.config.disable.name.disableParticles": "disableParticles", "tweakeroo.config.disable.name.disablePortalGuiClosing": "disablePortalGuiClosing", "tweakeroo.config.disable.name.disableRainEffects": "disableRainEffects", "tweakeroo.config.disable.name.disableRenderDistanceFog": "disableRenderDistanceFog", "tweakeroo.config.disable.name.disableRenderingScaffolding": "disableRenderingScaffolding", "tweakeroo.config.disable.name.disableScoreboardRendering": "disableScoreboardRendering", "tweakeroo.config.disable.name.disableShovelPathing": "disableShovelPathing", "tweakeroo.config.disable.name.disableShulkerBoxTooltip": "disableShulkerBoxTooltip", "tweakeroo.config.disable.name.disableSignGui": "disable<PERSON><PERSON><PERSON><PERSON>", "tweakeroo.config.disable.name.disableSkyDarkness": "disableSkyDarkness", "tweakeroo.config.disable.name.disableSlimeBlockSlowdown": "disableSlimeBlockSlowdown", "tweakeroo.config.disable.name.disableStatusEffectHud": "disableStatusEffectHud", "tweakeroo.config.disable.name.disableTileEntityRendering": "disableTileEntityRendering", "tweakeroo.config.disable.name.disableTileEntityTicking": "disableTileEntityTicking", "tweakeroo.config.disable.name.disableVillagerTradeLocking": "disableVillagerTradeLocking", "tweakeroo.config.disable.name.disableWallUnsprint": "disableWallUnsprint", "tweakeroo.config.disable.name.disableWorldViewBob": "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tweakeroo.config.disable.prettyName.disableArmorStandRendering": "Disable Armor Stand Rendering", "tweakeroo.config.disable.prettyName.disableAtmosphericFog": "Disable Atmospheric Fog", "tweakeroo.config.disable.prettyName.disableAxeStripping": "Disable Axe Stripping", "tweakeroo.config.disable.prettyName.disableBatSpawning": "Disable Bat Spawning", "tweakeroo.config.disable.prettyName.disableBeaconBeamRendering": "Disable Beacon Beam Rendering", "tweakeroo.config.disable.prettyName.disableBlockBreakCooldown": "Disable Block Break Cooldown", "tweakeroo.config.disable.prettyName.disableBlockBreakingParticles": "Disable Block Breaking Particles", "tweakeroo.config.disable.prettyName.disableBossBar": "Disable Boss Bar", "tweakeroo.config.disable.prettyName.disableBossFog": "Disable Boss Fog", "tweakeroo.config.disable.prettyName.disableChunkRendering": "Disable Chunk Rendering", "tweakeroo.config.disable.prettyName.disableClientEntityUpdates": "Disable Client Entity Updates", "tweakeroo.config.disable.prettyName.disableClientLightUpdates": "Disable Client Light Updates", "tweakeroo.config.disable.prettyName.disableConstantChunkSaving": "Disable Constant Chunk Saving", "tweakeroo.config.disable.prettyName.disableCreativeMenuInfestedBlocks": "Disable Creative Menu Infested Blocks", "tweakeroo.config.disable.prettyName.disableDeadMobRendering": "Disable Dead Mob Rendering", "tweakeroo.config.disable.prettyName.disableDeadMobTargeting": "Disable Dead Mob Targeting", "tweakeroo.config.disable.prettyName.disableDoubleTapSprint": "Disable Double Tap Sprint", "tweakeroo.config.disable.prettyName.disableEntityRendering": "Disable Entity Rendering", "tweakeroo.config.disable.prettyName.disableEntityTicking": "Disable Entity Ticking", "tweakeroo.config.disable.prettyName.disableFallingBlockEntityRendering": "Disable Falling Block Entity Rendering", "tweakeroo.config.disable.prettyName.disableFirstPersonEffectParticles": "Disable First Person Effect Particles", "tweakeroo.config.disable.prettyName.disableInventoryEffectRendering": "Disable Inventory Effect Rendering", "tweakeroo.config.disable.prettyName.disableItemSwitchRenderCooldown": "Disable Item Switch Render Cooldown", "tweakeroo.config.disable.prettyName.disableMobSpawnerMobRendering": "Disable Mob Spawner <PERSON><PERSON>", "tweakeroo.config.disable.prettyName.disableNauseaEffect": "Disable Nausea Effect", "tweakeroo.config.disable.prettyName.disableNetherFog": "Disable <PERSON><PERSON>og", "tweakeroo.config.disable.prettyName.disableNetherPortalSound": "Disable Nether Portal Sound", "tweakeroo.config.disable.prettyName.disableObserver": "Disable Observer", "tweakeroo.config.disable.prettyName.disableOffhandRendering": "Disable Off hand Rendering", "tweakeroo.config.disable.prettyName.disableParticles": "Disable Particles", "tweakeroo.config.disable.prettyName.disablePortalGuiClosing": "Disable Portal Gui Closing", "tweakeroo.config.disable.prettyName.disableRainEffects": "Disable Rain Effects", "tweakeroo.config.disable.prettyName.disableRenderDistanceFog": "Disable Render Distance Fog", "tweakeroo.config.disable.prettyName.disableRenderingScaffolding": "Disable Rendering Scaffolding", "tweakeroo.config.disable.prettyName.disableScoreboardRendering": "Disable Scoreboard Rendering", "tweakeroo.config.disable.prettyName.disableShovelPathing": "Disable <PERSON><PERSON><PERSON>", "tweakeroo.config.disable.prettyName.disableShulkerBoxTooltip": "Disable Shulker Box Tooltip", "tweakeroo.config.disable.prettyName.disableSignGui": "Disable Sign Gui", "tweakeroo.config.disable.prettyName.disableSkyDarkness": "Disable Sky Darkness", "tweakeroo.config.disable.prettyName.disableSlimeBlockSlowdown": "Disable Slime Block Slowdown", "tweakeroo.config.disable.prettyName.disableStatusEffectHud": "Disable Status Effect Hud", "tweakeroo.config.disable.prettyName.disableTileEntityRendering": "Disable Tile Entity Rendering", "tweakeroo.config.disable.prettyName.disableTileEntityTicking": "Disable Tile Entity Ticking", "tweakeroo.config.disable.prettyName.disableVillagerTradeLocking": "Disable Villager Trade Locking", "tweakeroo.config.disable.prettyName.disableWallUnsprint": "Disable Wall Unsprint", "tweakeroo.config.disable.prettyName.disableWorldViewBob": "Disable World View Bob", "tweakeroo.config.feature_toggle.comment.tweakAccurateBlockPlacement": "Enables a simpler version of Flexible placement, similar to\nthe Carpet mod, so basically either facing into or out\nfrom the block face clicked on.", "tweakeroo.config.feature_toggle.comment.tweakAfterClicker": "Enables a \"after clicker\" tweak, which does automatic right\nclicks on the just-placed block.\nUseful for example for Repeaters (setting the delay).\nTo quickly adjust the value, scroll while\nholding down the tweak toggle keybind.", "tweakeroo.config.feature_toggle.comment.tweakAimLock": "Enables an aim lock, locking the yaw and pitch rotations\nto the current values.\nThis is separate from the snap aim lock,\nwhich locks them to the snapped value.\nThis allows locking them \"freely\" to the current value.", "tweakeroo.config.feature_toggle.comment.tweakAngelBlock": "Enables an \"Angel Block\" tweak, which allows\nplacing blocks in mid-air in Creative mode.\nPowered by \"Flotato\" technology.", "tweakeroo.config.feature_toggle.comment.tweakAreaSelector": "Enables the area selector\nWritten by Andrew54757 under TweakFork", "tweakeroo.config.feature_toggle.comment.tweakAutoSwitchElytra": "Automatically switches to the Elytra when falling\nand back to the previous chest equipment when landing.", "tweakeroo.config.feature_toggle.comment.tweakBlockReachOverride": "Overrides the block reach distance with\nthe one set in Generic -> blockReachDistance", "tweakeroo.config.feature_toggle.comment.tweakBlockTypeBreakRestriction": "Restricts which blocks you are able to break (manually).\nSee the corresponding 'blockBreakRestriction*' configs in the Lists category.", "tweakeroo.config.feature_toggle.comment.tweakBreakingGrid": "When enabled, you can only break blocks in\na grid pattern, with a configurable interval.\nTo quickly adjust the interval, scroll while\nholding down the tweak toggle keybind.", "tweakeroo.config.feature_toggle.comment.tweakBreakingRestriction": "Enables the Breaking Restriction mode\n  (<PERSON>e, Layer, Face, Column, Line, Diagonal).\nBasically only allows you to break blocks\nin those patterns, while holding down the attack key.", "tweakeroo.config.feature_toggle.comment.tweakBundleDisplay": "Enables rendering a preview of the Bundle contents,\nwhen you hold shift while hovering over a Bundle item", "tweakeroo.config.feature_toggle.comment.tweakChatBackgroundColor": "Overrides the default chat background color\nwith the one from Generics -> 'chatBackgroundColor'", "tweakeroo.config.feature_toggle.comment.tweakChatPersistentText": "Stores the text from the chat input text field\nand restores it when the chat is opened again", "tweakeroo.config.feature_toggle.comment.tweakChatTimestamp": "Adds timestamps to chat messages", "tweakeroo.config.feature_toggle.comment.tweakCommandBlockExtraFields": "Adds extra fields to the Command Block GUI, for settings\nthe name of the command block, and seeing the stats results", "tweakeroo.config.feature_toggle.comment.tweakCreativeExtraItems": "Adds custom items to item groups.\nSee Lists -> 'creativeExtraItems' to control which items are added to the groups.\nNote: Currently these will be added to the Transportation group\n(because it has the least items), but in the future\nthe groups will be configurable per added item", "tweakeroo.config.feature_toggle.comment.tweakCustomFlatPresets": "Allows adding custom flat world presets to the list.\nThe presets are defined in Lists -> flatWorldPresets", "tweakeroo.config.feature_toggle.comment.tweakCustomFlyDeceleration": "Allows changing the fly deceleration in creative or spectator mode.\nThis is mainly meant for faster deceleration ie. less \"glide\"\nwhen releasing the movement keys.\nSee Generic -> flyDecelerationRampValue", "tweakeroo.config.feature_toggle.comment.tweakCustomInventoryScreenScale": "Allows using a custom GUI scale for any inventory screen.\nSee Generic -> §ecustomInventoryGuiScale§r for the scale value", "tweakeroo.config.feature_toggle.comment.tweakDarknessVisibility": "If enabled, this will increase visibility\nwhile effected by the Darkness Status Effect.", "tweakeroo.config.feature_toggle.comment.tweakElytraCamera": "Allows locking the real player rotations while holding the 'elytraCamera' activation key.\nThe controls will then only affect the separate 'camera rotations' for the rendering/camera.\nMeant for things like looking down/around while elytra flying nice and straight.", "tweakeroo.config.feature_toggle.comment.tweakEmptyShulkerBoxesStack": "Enables empty Shulker Boxes stacking up to 64.\nNOTE: They will also stack inside inventories!\nOn servers this will cause desyncs/glitches\nunless the server has a mod that does the same.\nIn single player this changes shulker box based system behaviour.", "tweakeroo.config.feature_toggle.comment.tweakEntityReachOverride": "Overrides the entity reach distance with\nthe one set in Generic -> entityReachDistance", "tweakeroo.config.feature_toggle.comment.tweakEntityTypeAttackRestriction": "Restricts which entities you are able to attack (manually).\nSee the corresponding 'entityAttackRestriction*' configs in the Lists category.", "tweakeroo.config.feature_toggle.comment.tweakExplosionReducedParticles": "If enabled, then all explosion particles will use the\nEXPLOSION_NORMAL particle instead of possibly\nthe EXPLOSION_LARGE or EXPLOSION_HUGE particles", "tweakeroo.config.feature_toggle.comment.tweakF3Cursor": "Enables always rendering the F3 screen cursor", "tweakeroo.config.feature_toggle.comment.tweakFakeSneakPlacement": "This tweak offsets the click position to the adjacent air block\nfrom the block that you actually click on.\nThis basically allows you to place blocks against blocks\nthat have a click action, such as opening inventory GUIs,\nwithout having to sneak. Note that this doesn't actually\nfake sneaking in any way, just the apparent effect is similar.", "tweakeroo.config.feature_toggle.comment.tweakFakeSneaking": "Enables \"fake sneaking\" ie. prevents you from falling from edges\nwithout slowing down the movement speed", "tweakeroo.config.feature_toggle.comment.tweakFastBlockPlacement": "Enables fast/convenient block placement when moving\nthe cursor over new blocks", "tweakeroo.config.feature_toggle.comment.tweakFastLeftClick": "Enables automatic fast left clicking while holding down\nthe attack button (left click).\nThe number of clicks per tick is set in the Generic configs.", "tweakeroo.config.feature_toggle.comment.tweakFastRightClick": "Enables automatic fast right clicking while holding down\nthe use button (right click).\nThe number of clicks per tick is set in the Generic configs.", "tweakeroo.config.feature_toggle.comment.tweakFillCloneLimit": "Enables overriding the /fill and /clone command\nblock limits in single player.\nThe new limit can be set in the Generic configs,\nin the 'fillCloneLimit' config value", "tweakeroo.config.feature_toggle.comment.tweakFlexibleBlockPlacement": "Enables placing blocks in different orientations\nor with an offset, while holding down the\nhotkeys for those modes.", "tweakeroo.config.feature_toggle.comment.tweakFlySpeed": "Enables overriding the fly speed in creative or spectator mode\nand using some presets for it", "tweakeroo.config.feature_toggle.comment.tweakFreeCamera": "Enables a free camera mode, similar to spectator mode,\nbut where the player will remain in place where\nyou first activate the free camera mode", "tweakeroo.config.feature_toggle.comment.tweakGammaOverride": "Overrides the video settings gamma value with\nthe one set in the Generic configs", "tweakeroo.config.feature_toggle.comment.tweakHandRestock": "Enables swapping a new stack to the main or the offhand\nwhen the previous stack runs out", "tweakeroo.config.feature_toggle.comment.tweakHangableEntityBypass": "Allows not targeting hangable entities (Item Frames and Paintings).\nThe Generic -> hangableEntityBypassInverse option can be used to control\nwhether you must be sneaking or not sneaking to be able to target the entity.", "tweakeroo.config.feature_toggle.comment.tweakHoldAttack": "Emulates holding down the attack button", "tweakeroo.config.feature_toggle.comment.tweakHoldUse": "Emulates holding down the use button", "tweakeroo.config.feature_toggle.comment.tweakHotbarScroll": "Enables the hotbar swapping via scrolling feature", "tweakeroo.config.feature_toggle.comment.tweakHotbarSlotCycle": "Enables cycling the selected hotbar slot after each placed\nblock, up to the set max slot number.\nTo quickly adjust the value, scroll while\nholding down the tweak toggle keybind.", "tweakeroo.config.feature_toggle.comment.tweakHotbarSlotRandomizer": "Enables randomizing the selected hotbar slot after each placed\nblock, up to the set max slot number.\nTo quickly adjust the value, scroll while\nholding down the tweak toggle keybind.", "tweakeroo.config.feature_toggle.comment.tweakHotbarSwap": "Enables the hotbar swapping via hotkeys feature", "tweakeroo.config.feature_toggle.comment.tweakInventoryPreview": "Enables an inventory preview while having the cursor over\na block or an entity with an inventory and holding down\nthe configured hotkey.\n§6Deprecated in favor of the 1.21+ MiniHUD version", "tweakeroo.config.feature_toggle.comment.tweakItemUnstackingProtection": "If enabled, then items configured in Lists -> unstackingItems\nwon't be allowed to spill out when using.\nThis is meant for example to prevent throwing buckets\ninto lava when filling them.", "tweakeroo.config.feature_toggle.comment.tweakLavaVisibility": "If enabled, then the level of Respiration and Aqua Affinity enchantments,\nand having the Fire Resistance effect active,\nwill greatly increase the visibility under lava.", "tweakeroo.config.feature_toggle.comment.tweakMapPreview": "If enabled, then holding shift over maps in an inventory\nwill render a preview of the map", "tweakeroo.config.feature_toggle.comment.tweakMovementKeysLast": "If enabled, then opposite movement keys won't cancel each other,\nbut instead the last pressed key is the active input.", "tweakeroo.config.feature_toggle.comment.tweakPeriodicAttack": "Enables periodic attacks (left clicks)\nConfigure the interval in Generic -> periodicAttackInterval", "tweakeroo.config.feature_toggle.comment.tweakPeriodicHoldAttack": "Enables periodically holding attack for a configurable amount of time.\nConfigure the interval in Generic -> periodicHoldAttackInterval\nand the duration in periodicHoldAttackDuration\n§6Note: You should not use the normal hold attack\n§6or the periodic attack at the same time", "tweakeroo.config.feature_toggle.comment.tweakPeriodicHoldUse": "Enables periodically holding use for a configurable amount of time.\nConfigure the interval in Generic -> periodicHoldUseInterval\nand the duration in periodicHoldUseDuration\n§6Note: You should not use the normal hold use\n§6or the periodic use at the same time", "tweakeroo.config.feature_toggle.comment.tweakPeriodicUse": "Enables periodic uses (right clicks)\nConfigure the interval in Generic -> periodicUseInterval", "tweakeroo.config.feature_toggle.comment.tweakPermanentSneak": "If enabled, the player will be sneaking the entire time", "tweakeroo.config.feature_toggle.comment.tweakPermanentSprint": "If enabled, the player will be always sprinting when moving forward", "tweakeroo.config.feature_toggle.comment.tweakPickBeforePlace": "If enabled, then before each block placement, the same block\nis switched to hand that you are placing against", "tweakeroo.config.feature_toggle.comment.tweakPlacementGrid": "When enabled, you can only place blocks in\na grid pattern, with a configurable interval.\nTo quickly adjust the value, scroll while\nholding down the tweak toggle keybind.", "tweakeroo.config.feature_toggle.comment.tweakPlacementLimit": "When enabled, you can only place a set number\nof blocks per use/right click.\nTo quickly adjust the value, scroll while\nholding down the tweak toggle keybind.", "tweakeroo.config.feature_toggle.comment.tweakPlacementRestriction": "Enables the Placement Restriction mode\n  (Plane, Layer, Face, Column, Line, Diagonal)", "tweakeroo.config.feature_toggle.comment.tweakPlacementRestrictionFirst": "Restricts block placement so that you can only\nplace blocks against the same block type\nyou first clicked on", "tweakeroo.config.feature_toggle.comment.tweakPlacementRestrictionHand": "Restricts block placement so that you can only\nplace blocks against the same block type\nyou are holding in your hand", "tweakeroo.config.feature_toggle.comment.tweakPlayerInventoryPeek": "Enables a player inventory peek/preview, while holding the\nconfigured hotkey key for it.", "tweakeroo.config.feature_toggle.comment.tweakPlayerListAlwaysVisible": "If enabled, then the player list is always rendered without\nhaving to hold down the key (tab by default)", "tweakeroo.config.feature_toggle.comment.tweakPotionWarning": "Prints a warning message to the hotbar when\nnon-ambient potion effects are about to run out", "tweakeroo.config.feature_toggle.comment.tweakPrintDeathCoordinates": "Enables printing the player's coordinates to chat on death.\nThis feature is originally from usefulmod by nessie.", "tweakeroo.config.feature_toggle.comment.tweakRenderEdgeChunks": "Allows the edge-most client-loaded chunks to render.\nVanilla doesn't allow rendering chunks that don't have\nall the adjacent chunks loaded, meaning that the edge-most chunk\nof the client's loaded won't render in vanilla.\n§lThis is also very helpful in the Free Camera mode!§r", "tweakeroo.config.feature_toggle.comment.tweakRenderInvisibleEntities": "When enabled, invisible entities are rendered like\nthey would be in spectator mode.", "tweakeroo.config.feature_toggle.comment.tweakRenderLimitEntities": "Enables limiting the number of certain types of entities\nto render per frame. Currently XP Orbs and Item entities\nare supported, see Generic configs for the limits.", "tweakeroo.config.feature_toggle.comment.tweakRepairMode": "If enabled, then fully repaired items held in hand will\nbe swapped to damaged items that have Mending on them.", "tweakeroo.config.feature_toggle.comment.tweakSculkPulseLength": "Allows modifying the Sculk Sensor pulse length. Set the pulse length in Generic -> sculkSensorPulseLength", "tweakeroo.config.feature_toggle.comment.tweakSelectiveBlocksRenderOutline": "Renders an outline over listed blocks\nWritten by Andrew54757 under TweakFork", "tweakeroo.config.feature_toggle.comment.tweakSelectiveBlocksRendering": "Enables selectively visible blocks rendering\nWritten by Andrew54757 under TweakFork", "tweakeroo.config.feature_toggle.comment.tweakServerDataSync": "Use Server Data Syncer for entities such as Shulker Boxes,\nwhich allows inventoryPreview to work on servers such as with Servux.\n§6Y<PERSON> must be an operator of the server or install server-side\n§6mods to make the syncer work even this option is set to true.", "tweakeroo.config.feature_toggle.comment.tweakServerDataSyncBackup": "The Server Data Syncer can be configured to use the\nVanilla NbtQueryRequest packets when Servux is not available", "tweakeroo.config.feature_toggle.comment.tweakShulkerBoxDisplay": "Enables the Shulker Box contents display when hovering\nover them in an inventory and holding shift", "tweakeroo.config.feature_toggle.comment.tweakSignCopy": "When enabled, placed signs will use the text from\nthe previously placed sign.\nCan be combined with tweakNoSignGui to quickly place copies\nof a sign, by enabling that tweak after making the first sign.", "tweakeroo.config.feature_toggle.comment.tweakSnapAim": "Enabled a snap aim tweak, to make the player face to pre-set exact yaw rotations", "tweakeroo.config.feature_toggle.comment.tweakSnapAimLock": "Enables a snap aim lock, locking the yaw and/or pitch rotations\nto the currently snapped value", "tweakeroo.config.feature_toggle.comment.tweakSneak_1_15_2": "Restores the 1.15.2 sneaking behavior", "tweakeroo.config.feature_toggle.comment.tweakSpectatorTeleport": "Allows spectators to teleport to other spectators.\nThis is originally from usefulmod by nessie.", "tweakeroo.config.feature_toggle.comment.tweakStructureBlockLimit": "Allows overriding the structure block limit.\nThe new limit is set in Generic -> structureBlockMaxSize", "tweakeroo.config.feature_toggle.comment.tweakSwapAlmostBrokenTools": "If enabled, then any damageable items held in the hand that\nare about to break will be swapped to fresh ones", "tweakeroo.config.feature_toggle.comment.tweakTabCompleteCoordinate": "If enabled, then tab-completing coordinates while not\nlooking at a block, will use the player's position\ninstead of adding the ~ character.", "tweakeroo.config.feature_toggle.comment.tweakToolSwitch": "Enables automatically switching to an effective tool for the targeted block", "tweakeroo.config.feature_toggle.comment.tweakWaterVisibility": "If enabled, then the level of Respiration and Aqua Affinity enchantments,\nwill greatly increase the visibility under water.", "tweakeroo.config.feature_toggle.comment.tweakWeaponSwitch": "Enables automatically switching to a weapon for the targeted entity", "tweakeroo.config.feature_toggle.comment.tweakYMirror": "Mirrors the targeted y-position within the block bounds.\nThis is basically for placing slabs or stairs\nin the opposite top/bottom state from normal,\nif you have to place them against another slab for example.", "tweakeroo.config.feature_toggle.comment.tweakZoom": "Enables using the zoom hotkey to, well, zoom in", "tweakeroo.config.feature_toggle.name.tweakAccurateBlockPlacement": "tweakAccurateBlockPlacement", "tweakeroo.config.feature_toggle.name.tweakAfterClicker": "tweakAfterClicker", "tweakeroo.config.feature_toggle.name.tweakAimLock": "tweakAimLock", "tweakeroo.config.feature_toggle.name.tweakAngelBlock": "tweakAngelBlock", "tweakeroo.config.feature_toggle.name.tweakAreaSelector": "tweakAreaSelector", "tweakeroo.config.feature_toggle.name.tweakAutoSwitchElytra": "tweakAutoSwitchElytra", "tweakeroo.config.feature_toggle.name.tweakBlockReachOverride": "§6tweakBlockReachOverride§r", "tweakeroo.config.feature_toggle.name.tweakBlockTypeBreakRestriction": "tweakBlockTypeBreakRestriction", "tweakeroo.config.feature_toggle.name.tweakBreakingGrid": "tweakBreakingGrid", "tweakeroo.config.feature_toggle.name.tweakBreakingRestriction": "tweakBreakingRestriction", "tweakeroo.config.feature_toggle.name.tweakBundleDisplay": "tweakBundleDisplay", "tweakeroo.config.feature_toggle.name.tweakChatBackgroundColor": "tweakChatBackgroundColor", "tweakeroo.config.feature_toggle.name.tweakChatPersistentText": "tweakChatPersistentText", "tweakeroo.config.feature_toggle.name.tweakChatTimestamp": "tweakChatTimestamp", "tweakeroo.config.feature_toggle.name.tweakCommandBlockExtraFields": "tweakCommandBlockExtraFields", "tweakeroo.config.feature_toggle.name.tweakCreativeExtraItems": "tweakCreativeExtraItems", "tweakeroo.config.feature_toggle.name.tweakCustomFlatPresets": "tweakCustomFlatPresets", "tweakeroo.config.feature_toggle.name.tweakCustomFlyDeceleration": "tweakCustomFlyDeceleration", "tweakeroo.config.feature_toggle.name.tweakCustomInventoryScreenScale": "tweakCustomInventoryScreenScale", "tweakeroo.config.feature_toggle.name.tweakDarknessVisibility": "tweakDarknessVisibility", "tweakeroo.config.feature_toggle.name.tweakElytraCamera": "tweakElytraCamera", "tweakeroo.config.feature_toggle.name.tweakEmptyShulkerBoxesStack": "§6tweakEmptyShulkerBoxesStack§r", "tweakeroo.config.feature_toggle.name.tweakEntityReachOverride": "§6tweakEntityReachOverride§r", "tweakeroo.config.feature_toggle.name.tweakEntityTypeAttackRestriction": "tweakEntityTypeAttackRestriction", "tweakeroo.config.feature_toggle.name.tweakExplosionReducedParticles": "tweakExplosionReducedParticles", "tweakeroo.config.feature_toggle.name.tweakF3Cursor": "tweakF3Cursor", "tweakeroo.config.feature_toggle.name.tweakFakeSneakPlacement": "tweakFakeSneakPlacement", "tweakeroo.config.feature_toggle.name.tweakFakeSneaking": "tweakFakeSneaking", "tweakeroo.config.feature_toggle.name.tweakFastBlockPlacement": "tweakFastBlockPlacement", "tweakeroo.config.feature_toggle.name.tweakFastLeftClick": "tweakFastLeftClick", "tweakeroo.config.feature_toggle.name.tweakFastRightClick": "tweakFastRightClick", "tweakeroo.config.feature_toggle.name.tweakFillCloneLimit": "§6tweakFillCloneLimit§r", "tweakeroo.config.feature_toggle.name.tweakFlexibleBlockPlacement": "tweakFlexibleBlockPlacement", "tweakeroo.config.feature_toggle.name.tweakFlySpeed": "tweakFlySpeed", "tweakeroo.config.feature_toggle.name.tweakFreeCamera": "tweakFreeCamera", "tweakeroo.config.feature_toggle.name.tweakGammaOverride": "tweakGammaOverride", "tweakeroo.config.feature_toggle.name.tweakHandRestock": "tweakHandRestock", "tweakeroo.config.feature_toggle.name.tweakHangableEntityBypass": "tweakHangableEntityBypass", "tweakeroo.config.feature_toggle.name.tweakHoldAttack": "tweakHoldAttack", "tweakeroo.config.feature_toggle.name.tweakHoldUse": "tweakHoldUse", "tweakeroo.config.feature_toggle.name.tweakHotbarScroll": "tweakHotbarScroll", "tweakeroo.config.feature_toggle.name.tweakHotbarSlotCycle": "tweakHotbarSlotCycle", "tweakeroo.config.feature_toggle.name.tweakHotbarSlotRandomizer": "tweakHotbarSlotRandomizer", "tweakeroo.config.feature_toggle.name.tweakHotbarSwap": "tweakHotbarSwap", "tweakeroo.config.feature_toggle.name.tweakInventoryPreview": "§6tweakInventoryPreview§r", "tweakeroo.config.feature_toggle.name.tweakItemUnstackingProtection": "tweakItemUnstackingProtection", "tweakeroo.config.feature_toggle.name.tweakLavaVisibility": "tweakLavaVisibility", "tweakeroo.config.feature_toggle.name.tweakMapPreview": "tweakMapPreview", "tweakeroo.config.feature_toggle.name.tweakMovementKeysLast": "tweakMovementKeysLast", "tweakeroo.config.feature_toggle.name.tweakPeriodicAttack": "tweakPeriodicAttack", "tweakeroo.config.feature_toggle.name.tweakPeriodicHoldAttack": "tweakPeriodicHoldAttack", "tweakeroo.config.feature_toggle.name.tweakPeriodicHoldUse": "tweakPeriodicHoldUse", "tweakeroo.config.feature_toggle.name.tweakPeriodicUse": "tweakPeriodicUse", "tweakeroo.config.feature_toggle.name.tweakPermanentSneak": "tweakPermanentSneak", "tweakeroo.config.feature_toggle.name.tweakPermanentSprint": "tweakPermanentSprint", "tweakeroo.config.feature_toggle.name.tweakPickBeforePlace": "tweakPickBeforePlace", "tweakeroo.config.feature_toggle.name.tweakPlacementGrid": "tweakPlacementGrid", "tweakeroo.config.feature_toggle.name.tweakPlacementLimit": "tweakPlacementLimit", "tweakeroo.config.feature_toggle.name.tweakPlacementRestriction": "tweakPlacementRestriction", "tweakeroo.config.feature_toggle.name.tweakPlacementRestrictionFirst": "tweakPlacementRestrictionFirst", "tweakeroo.config.feature_toggle.name.tweakPlacementRestrictionHand": "tweakPlacementRestrictionHand", "tweakeroo.config.feature_toggle.name.tweakPlayerInventoryPeek": "tweakPlayerInventoryPeek", "tweakeroo.config.feature_toggle.name.tweakPlayerListAlwaysVisible": "tweakPlayerListAlwaysVisible", "tweakeroo.config.feature_toggle.name.tweakPotionWarning": "tweakPotionWarning", "tweakeroo.config.feature_toggle.name.tweakPrintDeathCoordinates": "tweakPrintDeathCoordinates", "tweakeroo.config.feature_toggle.name.tweakRenderEdgeChunks": "tweakRenderEdgeChunks", "tweakeroo.config.feature_toggle.name.tweakRenderInvisibleEntities": "tweakRenderInvisibleEntities", "tweakeroo.config.feature_toggle.name.tweakRenderLimitEntities": "tweakRenderLimitEntities", "tweakeroo.config.feature_toggle.name.tweakRepairMode": "tweakRepairMode", "tweakeroo.config.feature_toggle.name.tweakSculkPulseLength": "§6tweakSculkPulseLength§r", "tweakeroo.config.feature_toggle.name.tweakSelectiveBlocksRenderOutline": "tweakSelectiveBlocksRenderOutline", "tweakeroo.config.feature_toggle.name.tweakSelectiveBlocksRendering": "tweakSelectiveBlocksRendering", "tweakeroo.config.feature_toggle.name.tweakServerDataSync": "tweakServerDataSync", "tweakeroo.config.feature_toggle.name.tweakServerDataSyncBackup": "tweakServerDataSyncBackup", "tweakeroo.config.feature_toggle.name.tweakShulkerBoxDisplay": "tweakShulkerBoxDisplay", "tweakeroo.config.feature_toggle.name.tweakSignCopy": "tweakSignCopy", "tweakeroo.config.feature_toggle.name.tweakSnapAim": "tweakSnapAim", "tweakeroo.config.feature_toggle.name.tweakSnapAimLock": "tweakSnapAimLock", "tweakeroo.config.feature_toggle.name.tweakSneak_1_15_2": "tweakSneak_1.15.2", "tweakeroo.config.feature_toggle.name.tweakSpectatorTeleport": "tweakSpectatorTeleport", "tweakeroo.config.feature_toggle.name.tweakStructureBlockLimit": "§6tweakStructureBlockLimit§r", "tweakeroo.config.feature_toggle.name.tweakSwapAlmostBrokenTools": "tweakSwapAlmostBrokenTools", "tweakeroo.config.feature_toggle.name.tweakTabCompleteCoordinate": "tweakTabCompleteCoordinate", "tweakeroo.config.feature_toggle.name.tweakToolSwitch": "tweakToolSwitch", "tweakeroo.config.feature_toggle.name.tweakWaterVisibility": "tweakWaterVisibility", "tweakeroo.config.feature_toggle.name.tweakWeaponSwitch": "tweakWeaponSwitch", "tweakeroo.config.feature_toggle.name.tweakYMirror": "tweakYMirror", "tweakeroo.config.feature_toggle.name.tweakZoom": "tweakZoom", "tweakeroo.config.feature_toggle.prettyName.tweakAccurateBlockPlacement": "Accurate Block Placement", "tweakeroo.config.feature_toggle.prettyName.tweakAfterClicker": "After Clicker", "tweakeroo.config.feature_toggle.prettyName.tweakAimLock": "Aim Lock", "tweakeroo.config.feature_toggle.prettyName.tweakAngelBlock": "Angel Block", "tweakeroo.config.feature_toggle.prettyName.tweakAreaSelector": "Area Selector", "tweakeroo.config.feature_toggle.prettyName.tweakAutoSwitchElytra": "Auto Switch Elytra", "tweakeroo.config.feature_toggle.prettyName.tweakBlockReachOverride": "Block Reach Override", "tweakeroo.config.feature_toggle.prettyName.tweakBlockTypeBreakRestriction": "Block Type Break Restriction", "tweakeroo.config.feature_toggle.prettyName.tweakBreakingGrid": "Breaking Grid", "tweakeroo.config.feature_toggle.prettyName.tweakBreakingRestriction": "Breaking Restriction", "tweakeroo.config.feature_toggle.prettyName.tweakBundleDisplay": "Bundle Display", "tweakeroo.config.feature_toggle.prettyName.tweakChatBackgroundColor": "Chat Background Color", "tweakeroo.config.feature_toggle.prettyName.tweakChatPersistentText": "Chat Persistent Text", "tweakeroo.config.feature_toggle.prettyName.tweakChatTimestamp": "Chat Timestamp", "tweakeroo.config.feature_toggle.prettyName.tweakCommandBlockExtraFields": "Command Block Extra Fields", "tweakeroo.config.feature_toggle.prettyName.tweakCreativeExtraItems": "Creative Extra Items", "tweakeroo.config.feature_toggle.prettyName.tweakCustomFlatPresets": "Custom Flat Presets", "tweakeroo.config.feature_toggle.prettyName.tweakCustomFlyDeceleration": "Custom Fly Deceleration", "tweakeroo.config.feature_toggle.prettyName.tweakCustomInventoryScreenScale": "Custom Inventory Screen Scale", "tweakeroo.config.feature_toggle.prettyName.tweakDarknessVisibility": "Darkness Visibility", "tweakeroo.config.feature_toggle.prettyName.tweakElytraCamera": "Elytra Camera", "tweakeroo.config.feature_toggle.prettyName.tweakEmptyShulkerBoxesStack": "Empty Shulker Boxes Stack", "tweakeroo.config.feature_toggle.prettyName.tweakEntityReachOverride": "Entity Reach Override", "tweakeroo.config.feature_toggle.prettyName.tweakEntityTypeAttackRestriction": "Entity Type Attack Restriction", "tweakeroo.config.feature_toggle.prettyName.tweakExplosionReducedParticles": "Explosion Reduced Particles", "tweakeroo.config.feature_toggle.prettyName.tweakF3Cursor": "F3 Cursor", "tweakeroo.config.feature_toggle.prettyName.tweakFakeSneakPlacement": "Fake Sneak Placement", "tweakeroo.config.feature_toggle.prettyName.tweakFakeSneaking": "Fake Sneaking", "tweakeroo.config.feature_toggle.prettyName.tweakFastBlockPlacement": "Fast Block Placement", "tweakeroo.config.feature_toggle.prettyName.tweakFastLeftClick": "Fast Left Click", "tweakeroo.config.feature_toggle.prettyName.tweakFastRightClick": "Fast Right Click", "tweakeroo.config.feature_toggle.prettyName.tweakFillCloneLimit": "<PERSON>ll Clone Limit", "tweakeroo.config.feature_toggle.prettyName.tweakFlexibleBlockPlacement": "Flexible Block Placement", "tweakeroo.config.feature_toggle.prettyName.tweakFlySpeed": "Fly Speed", "tweakeroo.config.feature_toggle.prettyName.tweakFreeCamera": "Free Camera", "tweakeroo.config.feature_toggle.prettyName.tweakGammaOverride": "Gamma Override", "tweakeroo.config.feature_toggle.prettyName.tweakHandRestock": "Hand <PERSON>ock", "tweakeroo.config.feature_toggle.prettyName.tweakHangableEntityBypass": "Hangable Entity Bypass", "tweakeroo.config.feature_toggle.prettyName.tweakHoldAttack": "Hold Attack", "tweakeroo.config.feature_toggle.prettyName.tweakHoldUse": "Hold Use", "tweakeroo.config.feature_toggle.prettyName.tweakHotbarScroll": "<PERSON><PERSON>", "tweakeroo.config.feature_toggle.prettyName.tweakHotbarSlotCycle": "Hotbar Slot Cycle", "tweakeroo.config.feature_toggle.prettyName.tweakHotbarSlotRandomizer": "Hotbar Slot Randomizer", "tweakeroo.config.feature_toggle.prettyName.tweakHotbarSwap": "<PERSON>bar Swap", "tweakeroo.config.feature_toggle.prettyName.tweakInventoryPreview": "Inventory Preview", "tweakeroo.config.feature_toggle.prettyName.tweakItemUnstackingProtection": "Item Unstacking Protection", "tweakeroo.config.feature_toggle.prettyName.tweakLavaVisibility": "Lava Visibility", "tweakeroo.config.feature_toggle.prettyName.tweakMapPreview": "Map Preview", "tweakeroo.config.feature_toggle.prettyName.tweakMovementKeysLast": "Movement Keys Last", "tweakeroo.config.feature_toggle.prettyName.tweakPeriodicAttack": "Periodic Attack", "tweakeroo.config.feature_toggle.prettyName.tweakPeriodicHoldAttack": "Periodic Hold Attack", "tweakeroo.config.feature_toggle.prettyName.tweakPeriodicHoldUse": "Periodic Hold Use", "tweakeroo.config.feature_toggle.prettyName.tweakPeriodicUse": "Periodic Use", "tweakeroo.config.feature_toggle.prettyName.tweakPermanentSneak": "Permanent Sneak", "tweakeroo.config.feature_toggle.prettyName.tweakPermanentSprint": "Permanent Sprint", "tweakeroo.config.feature_toggle.prettyName.tweakPickBeforePlace": "Pick Before Place", "tweakeroo.config.feature_toggle.prettyName.tweakPlacementGrid": "Placement Grid", "tweakeroo.config.feature_toggle.prettyName.tweakPlacementLimit": "Placement Limit", "tweakeroo.config.feature_toggle.prettyName.tweakPlacementRestriction": "Placement Restriction", "tweakeroo.config.feature_toggle.prettyName.tweakPlacementRestrictionFirst": "Placement Restriction First", "tweakeroo.config.feature_toggle.prettyName.tweakPlacementRestrictionHand": "Placement Restriction Hand", "tweakeroo.config.feature_toggle.prettyName.tweakPlayerInventoryPeek": "Player In<PERSON>ory <PERSON>", "tweakeroo.config.feature_toggle.prettyName.tweakPlayerListAlwaysVisible": "Player List Always Visible", "tweakeroo.config.feature_toggle.prettyName.tweakPotionWarning": "Potion Warning", "tweakeroo.config.feature_toggle.prettyName.tweakPrintDeathCoordinates": "Print Death Coordinates", "tweakeroo.config.feature_toggle.prettyName.tweakRenderEdgeChunks": "Render Edge Chunks", "tweakeroo.config.feature_toggle.prettyName.tweakRenderInvisibleEntities": "Render Invisible Entities", "tweakeroo.config.feature_toggle.prettyName.tweakRenderLimitEntities": "Render Limit Entities", "tweakeroo.config.feature_toggle.prettyName.tweakRepairMode": "Repair Mode", "tweakeroo.config.feature_toggle.prettyName.tweakSculkPulseLength": "Sculk Pulse Length", "tweakeroo.config.feature_toggle.prettyName.tweakSelectiveBlocksRenderOutline": "Selective Blocks Render Outline", "tweakeroo.config.feature_toggle.prettyName.tweakSelectiveBlocksRendering": "Selective Blocks Rendering", "tweakeroo.config.feature_toggle.prettyName.tweakServerDataSync": "Server Data Sync", "tweakeroo.config.feature_toggle.prettyName.tweakServerDataSyncBackup": "Server Data Sync Backup", "tweakeroo.config.feature_toggle.prettyName.tweakShulkerBoxDisplay": "Shulker Box Display", "tweakeroo.config.feature_toggle.prettyName.tweakSignCopy": "Sign Copy", "tweakeroo.config.feature_toggle.prettyName.tweakSnapAim": "Snap Aim", "tweakeroo.config.feature_toggle.prettyName.tweakSnapAimLock": "Snap Aim Lock", "tweakeroo.config.feature_toggle.prettyName.tweakSneak_1_15_2": "1.15.2 Sneaking", "tweakeroo.config.feature_toggle.prettyName.tweakSpectatorTeleport": "Spectator Teleport", "tweakeroo.config.feature_toggle.prettyName.tweakStructureBlockLimit": "Structure Block Limit", "tweakeroo.config.feature_toggle.prettyName.tweakSwapAlmostBrokenTools": "Swap Almost Broken Tools", "tweakeroo.config.feature_toggle.prettyName.tweakTabCompleteCoordinate": "Tab Complete Coordinate", "tweakeroo.config.feature_toggle.prettyName.tweakToolSwitch": "Tool Switch", "tweakeroo.config.feature_toggle.prettyName.tweakWaterVisibility": "Water Visibility", "tweakeroo.config.feature_toggle.prettyName.tweakWeaponSwitch": "Weapon Switch", "tweakeroo.config.feature_toggle.prettyName.tweakYMirror": "Y Mirror", "tweakeroo.config.feature_toggle.prettyName.tweakZoom": "Camera Zoom", "tweakeroo.config.fixes.comment.elytraFix": "Elytra landing fix by Earthcomputer and <PERSON><PERSON><PERSON>.\nThe deployment fix is now in vanilla, so this only affects landing now.", "tweakeroo.config.fixes.comment.elytraSprintCancel": "Fixes the Vanilla bug (MC-279688) that removes\nyou from sprinting while using an Elytra.\nThis setting is only needed for 1.21.4, but for 1.21.5 this is fixed.", "tweakeroo.config.fixes.comment.macHorizontalScroll": "If you are on Mac/OSX, this applies the same fix/change\nas the hscroll mod, while not breaking all the scroll handling\nin malilib-based mods.", "tweakeroo.config.fixes.comment.ravagerClientBlockBreakFix": "Fixes Ravagers breaking blocks on the client side,\nwhich causes annoying ghost blocks/block desyncs", "tweakeroo.config.fixes.name.elytraFix": "elytraFix", "tweakeroo.config.fixes.name.elytraSprintCancel": "elytraSprintCancel", "tweakeroo.config.fixes.name.macHorizontalScroll": "macHorizontalScroll", "tweakeroo.config.fixes.name.ravagerClientBlockBreakFix": "ravagerClientBlockBreakFix", "tweakeroo.config.generic.comment.accuratePlacementProtocol": "If enabled, then the Flexible Block Placement and the\nAccurate Block Placement use the protocol implemented in Carpet mod.\n§6Note: This is required for any block rotations to work, other than\n§6blocks that only care about the block side you click on (Hoppers, Logs etc.)", "tweakeroo.config.generic.comment.accuratePlacementProtocolMode": "The type of \"accurate placement protocol\" to use.\n- Auto: Uses v3 in single player, and by default Slabs-only in multiplayer,\n  unless the server has Carpet mod that sends a 'carpet:hello'\n  packet, in which case v2 is used on that server.\n- Version 3: Supported by Tweakeroo itself (in single player) or with Servux.\n- Version 2: Compatible with servers with the Carpet mod\n  (either QuickCarpet by skyrising and DeadlyMC,\n  or CarpetExtra in addition to FabricCarpet.\n  And in both cases the 'accurateBlockPlacement' Carpet rule needs\n  to be enabled on the server).\n- Slabs only: Only fixes top slabs. Compatible with Paper servers.\n- None: Does not modify coordinates.", "tweakeroo.config.generic.comment.afterClickerClickCount": "The number of right clicks to do per placed block when\ntweakAfterClicker is enabled", "tweakeroo.config.generic.comment.angelBlockPlacementDistance": "The distance from the player blocks can be placed in\nthe air when tweakAngelBlock is enabled.\n5 is the maximum the server allows.", "tweakeroo.config.generic.comment.areaSelectionUseAll": "Whether or not to include air in selection\nWritten by Andrew54757 under TweakFork", "tweakeroo.config.generic.comment.blockReachDistance": "The block reach distance to use if the override tweak is enabled.\nThe maximum the game allows is 64.\n§6Do not attempt to use this at a value beyond\n§6[0.5 - 1.0] higher than the Rules defined on a server.", "tweakeroo.config.generic.comment.blockTypeBreakRestrictionWarn": "Selects which type of warning message to show (if any)\nwhen the Block Type Break Restriction feature prevents breaking a block", "tweakeroo.config.generic.comment.breakingGridSize": "The grid interval size for the grid breaking mode.\nTo quickly adjust the value, scroll while\nholding down the tweak toggle keybind.", "tweakeroo.config.generic.comment.breakingRestrictionMode": "The Breaking Restriction mode to use (hotkey-selectable)", "tweakeroo.config.generic.comment.bundleDisplayBgColor": "Enables tinting/coloring the Bundle display\nbackground texture with the dye color of the bundle", "tweakeroo.config.generic.comment.bundleDisplayRequireShift": "Whether or not holding shift is required for the Bundle preview\n§6NOTE: Disabling this causes the Vanilla Bundle tooltip to be fully disabled.", "tweakeroo.config.generic.comment.bundleDisplayRowWidth": "Adjust the Row Width size of the Bundle Preview display,\nAnything smaller or larger will cause texture display problems.", "tweakeroo.config.generic.comment.chatBackgroundColor": "The background color for the chat messages,\nif 'tweakChatBackgroundColor' is enabled", "tweakeroo.config.generic.comment.chatTimeFormat": "The time format for chat messages, if tweakChatTimestamp is enabled\nUses the Java SimpleDateFormat format specifiers.", "tweakeroo.config.generic.comment.clientPlacementRotation": "Enable single player and client side placement rotations,\nsuch as Accurate Placement working in single player without Carpet mod", "tweakeroo.config.generic.comment.customInventoryGuiScale": "The GUI scale value to use for inventory screens, if\n§etweakCustomInventoryScreenScale§r is enabled.", "tweakeroo.config.generic.comment.debugLogging": "Enables some debug log messages in the game console,\nfor debugging certain issues or crashes.", "tweakeroo.config.generic.comment.darknessScaleOverrideValue": "The override value used by 'tweakDarknessVisibility'\nto automatically weaken the 'darkening' screen effect.\nThis is a percentage value for the amount\nthat the screen darkens for each effect pulse.", "tweakeroo.config.generic.comment.elytraCameraIndicator": "Whether or not to render the real pitch angle\nindicator when the elytra camera mode is active", "tweakeroo.config.generic.comment.entityReachDistance": "The entity reach distance to use if the override tweak is enabled.\nThe maximum the game allows is 64.\n§6Do not attempt to use this at a value beyond\n§6[0.5 - 1.0] higher than the Rules defined on a server.", "tweakeroo.config.generic.comment.entityTypeAttackRestrictionWarn": "Selects which type of warning message to show (if any)\nwhen the Entity Type Attack Restriction feature prevents attacking an entity", "tweakeroo.config.generic.comment.fastBlockPlacementCount": "The maximum number of blocks to place per game tick\nwith the Fast Block Placement tweak", "tweakeroo.config.generic.comment.fastLeftClickAllowTools": "Allow the Fast Left Click to work in survival\nalso while holding tool items", "tweakeroo.config.generic.comment.fastLeftClickCount": "The number of left clicks to do per game tick when\ntweakFastLeftClick is enabled and the attack button is held down", "tweakeroo.config.generic.comment.fastPlacementRememberOrientation": "If enabled, then the Fast Block Placement feature will always remember\nthe orientation of the first block you place.\nWithout this, the orientation will only be remembered\nwith the Flexible Block Placement enabled and active.", "tweakeroo.config.generic.comment.fastRightClickCount": "The number of right clicks to do per game tick when\ntweakFastRightClick is enabled and the use button is held down", "tweakeroo.config.generic.comment.fillCloneLimit": "The new /fill and /clone block limit in single player,\nif the tweak to override them is enabled", "tweakeroo.config.generic.comment.flexibleBlockPlacementOverlayColor": "The color of the currently pointed-at\nregion in block placement the overlay", "tweakeroo.config.generic.comment.flyDecelerationFactor": "This adjusts how quickly the player will stop if the\n'customFlyDeceleration' tweak is enabled", "tweakeroo.config.generic.comment.flySpeedIncrement1": "How much the fly speed changes for increment 1", "tweakeroo.config.generic.comment.flySpeedIncrement2": "How much the fly speed changes for increment 2", "tweakeroo.config.generic.comment.flySpeedPreset1": "The fly speed for preset 1", "tweakeroo.config.generic.comment.flySpeedPreset2": "The fly speed for preset 2", "tweakeroo.config.generic.comment.flySpeedPreset3": "The fly speed for preset 3", "tweakeroo.config.generic.comment.flySpeedPreset4": "The fly speed for preset 4", "tweakeroo.config.generic.comment.freeCameraPlayerInputs": "When enabled, the attacks and use actions\n(ie. left and right clicks) in Free Camera mode are\nlet through to the actual player.", "tweakeroo.config.generic.comment.freeCameraPlayerMovement": "When enabled, the movement inputs in the Free Camera mode\nwill move the actual client player instead of the camera", "tweakeroo.config.generic.comment.gammaOverrideValue": "The gamma value to use when the override option is enabled", "tweakeroo.config.generic.comment.handRestockPre": "If enabled, then hand restocking happens\nbefore the stack runs out", "tweakeroo.config.generic.comment.handRestockPreThreshold": "The stack size threshold at which Hand Restock will happen\nin the pre-restock mode", "tweakeroo.config.generic.comment.hangableEntityBypassInverse": "If the hangableEntityTargetingBypass tweak is enabled,\nthen this controls whether the player must be or must not be\nsneaking to be able to target the hangable entity (Item Frame or Painting).\n > true - Sneaking = ignore/bypass the entity\n > false - Sneaking = target the entity", "tweakeroo.config.generic.comment.hotbarSlotCycleMax": "This is the last hotbar slot to use/cycle through\nif the hotbar slot cycle tweak is enabled.\nBasically the cycle will jump back to the first slot\nwhen going over the maximum slot number set here.", "tweakeroo.config.generic.comment.hotbarSlotRandomizerMax": "This is the last hotbar slot to use if the hotbar slot randomizer\ntweak is enabled. Basically the selected hotbar slot will be randomly\npicked from 1 to this maximum slot after an item use.", "tweakeroo.config.generic.comment.hotbarSwapOverlayAlignment": "The positioning of the hotbar swap overlay", "tweakeroo.config.generic.comment.hotbarSwapOverlayOffsetX": "The horizontal offset of the hotbar swap overlay", "tweakeroo.config.generic.comment.hotbarSwapOverlayOffsetY": "The vertical offset of the hotbar swap overlay", "tweakeroo.config.generic.comment.inventoryPreviewVillagerBGColor": "Enable/Disable the Background Color\ndisplay for the Villager Trades based upon their Profession.", "tweakeroo.config.generic.comment.itemSwapDurabilityThreshold": "This is the durability threshold (in uses left)\nfor the low-durability item swap feature.\nNote that items with low total durability will go lower\nand be swapped at 5%% left.", "tweakeroo.config.generic.comment.itemUsePacketCheckBypass": "Bypass the new distance/coordinate check that was added in 1.18.2.\n\nThat check breaks the \"accurate placement protocol\" and causes\nany blocks placed with a rotation (or other property) request to just become ghost blocks.\n\nThere is basically no need to ever disable this.\nThe check didn't even exist ever before 1.18.2.", "tweakeroo.config.generic.comment.mapPreviewRequireShift": "Whether holding shift is required for the Map Preview", "tweakeroo.config.generic.comment.mapPreviewSize": "The size of the rendered map previews", "tweakeroo.config.generic.comment.periodicAttackInterval": "The number of game ticks between automatic attacks (left clicks)", "tweakeroo.config.generic.comment.periodicAttackResetIntervalOnActivate": "Reset the Periodic Attack Interval upon Deactivation,\nif the Mouse Scroll Wheel was used to adjust it", "tweakeroo.config.generic.comment.periodicHoldAttackDuration": "The number of game ticks to hold down attack", "tweakeroo.config.generic.comment.periodicHoldAttackInterval": "The number of game ticks between starting to hold down attack (left click)", "tweakeroo.config.generic.comment.periodicHoldAttackResetIntervalOnActivate": "Reset the Periodic Hold Attack Interval upon Deactivation,\nif the Mouse Scroll Wheel was used to adjust it", "tweakeroo.config.generic.comment.periodicHoldUseDuration": "The number of game ticks to hold down use", "tweakeroo.config.generic.comment.periodicHoldUseInterval": "The number of game ticks between starting to hold down use (right click)", "tweakeroo.config.generic.comment.periodicHoldUseResetIntervalOnActivate": "Reset the Periodic Hold Use Interval upon Deactivation,\nif the Mouse Scroll Wheel was used to adjust it", "tweakeroo.config.generic.comment.periodicUseInterval": "The number of game ticks between automatic uses (right clicks)", "tweakeroo.config.generic.comment.periodicUseResetIntervalOnActivate": "Reset the Periodic Use Interval upon Deactivation,\nif the Mouse Scroll Wheel was used to adjust it", "tweakeroo.config.generic.comment.permanentSneakAllowInGUIs": "If true, then the permanent sneak tweak will\nalso work while GUIs are open", "tweakeroo.config.generic.comment.placementGridSize": "The grid interval size for the grid placement mode.\nTo quickly adjust the value, scroll while\nholding down the tweak toggle keybind.", "tweakeroo.config.generic.comment.placementLimit": "The number of blocks you are able to place at maximum per\nright click, if tweakPlacementLimit is enabled.\nTo quickly adjust the value, scroll while\nholding down the tweak toggle keybind.", "tweakeroo.config.generic.comment.placementRestrictionMode": "The Placement Restriction mode to use (hotkey-selectable)", "tweakeroo.config.generic.comment.placementRestrictionTiedToFast": "When enabled, the Placement Restriction mode will toggle\nits state on/off when you toggle the Fast Placement mode.", "tweakeroo.config.generic.comment.potionWarningBeneficialOnly": "Only warn about potion effects running out that are marked as \"beneficial\"", "tweakeroo.config.generic.comment.potionWarningThreshold": "The remaining duration of potion effects (in ticks)\nafter which the warning will start showing", "tweakeroo.config.generic.comment.rememberFlexibleFromClick": "If enabled, then the Flexible Block Placement status\nwill be remembered from the first placed block,\nas long as the use key (right click) is held down.\nBasically you don't have to keep holding all the flexible\nactivation keys to fast place all the blocks in the same orientation.", "tweakeroo.config.generic.comment.renderLimitItem": "Maximum number of item entities rendered per frame.\nUse -1 for normal behaviour, ie. to disable this limit.", "tweakeroo.config.generic.comment.renderLimitXPOrb": "Maximum number of XP orb entities rendered per frame.\nUse -1 for normal behaviour, ie. to disable this limit.", "tweakeroo.config.generic.comment.sculkSensorPulseLength": "The pulse length for Sculk Sensors, if the 'tweakSculkPulseLength' tweak is enabled.", "tweakeroo.config.generic.comment.selectiveBlocksHideEntities": "Whether or not to hide entities for selective block rendering\nWritten by Andrew54757 under TweakFork", "tweakeroo.config.generic.comment.selectiveBlocksHideParticles": "Whether or not to hide particles for selective block rendering\nWritten by Andrew54757 under TweakFork", "tweakeroo.config.generic.comment.selectiveBlocksNoHit": "Whether or not to disable targeting hidden blocks\nWritten by Andrew54757 under TweakFork", "tweakeroo.config.generic.comment.selectiveBlocksTrackPistons": "Whether or not to track piston movements for selective block rendering\nWritten by Andrew54757 under TweakFork", "tweakeroo.config.generic.comment.serverDataSyncCacheRefresh": "The Cache refresh value as a fraction of a second.\nThis value is the trigger for whenever requesting entity data\ngets refreshed from the server, even if it still exists in the Cache.\nThis should be set around 25% or less of\nthe \"entityDataSyncCacheTimeout\" value.\nA value of '0.05f' means 50 ms or once every game tick.", "tweakeroo.config.generic.comment.serverDataSyncCacheTimeout": "The Cache timeout value in seconds that\nthe Entity Cache keeps records for.\nA lower value means more frequent updates.", "tweakeroo.config.generic.comment.serverNbtRequestRate": "Limit request rate for server entity data syncer", "tweakeroo.config.generic.comment.shulkerDisplayBgColor": "Enables tinting/coloring the Shulker Box display\nbackground texture with the dye color of the box", "tweakeroo.config.generic.comment.shulkerDisplayEnderChest": "Enables Ender Chest display similar to the Shulker Box Display.", "tweakeroo.config.generic.comment.shulkerDisplayRequireShift": "Whether or not holding shift is required for the Shulker Box preview", "tweakeroo.config.generic.comment.slotSyncWorkaround": "This prevents the server from overriding the durability or\nstack size on items that are being used quickly for example\nwith the fast right click tweak.", "tweakeroo.config.generic.comment.slotSyncWorkaroundAlways": "Enables the slot sync workaround at all times when the use key\nis held, not only when using fast right click or fast block placement.\nThis is mainly for other mods that may quickly use items when\nholding down use, such as Litematica's Easy Place mode.", "tweakeroo.config.generic.comment.snapAimIndicator": "Whether or not to render the snap aim angle indicator", "tweakeroo.config.generic.comment.snapAimIndicatorColor": "The color for the snap aim indicator background", "tweakeroo.config.generic.comment.snapAimMode": "Snap aim mode: yaw, or pitch, or both", "tweakeroo.config.generic.comment.snapAimOnlyCloseToAngle": "If enabled, then the snap aim only snaps to the angle\nwhen the internal angle is within a certain distance of it.\nThe threshold can be set in snapAimThreshold", "tweakeroo.config.generic.comment.snapAimPitchOvershoot": "Whether or not to allow overshooting the pitch angle\nfrom the normal +/- 90 degrees up to +/- 180 degrees", "tweakeroo.config.generic.comment.snapAimPitchStep": "The pitch angle step of the snap aim tweak", "tweakeroo.config.generic.comment.snapAimThresholdPitch": "The angle threshold inside which the player rotation will\nbe snapped to the snap angle.", "tweakeroo.config.generic.comment.snapAimThresholdYaw": "The angle threshold inside which the player rotation will\nbe snapped to the snap angle.", "tweakeroo.config.generic.comment.snapAimYawStep": "The yaw angle step of the snap aim tweak", "tweakeroo.config.generic.comment.structureBlockMaxSize": "The maximum dimensions for a Structure Block's saved area", "tweakeroo.config.generic.comment.toolSwapBetterEnchants": "Consider a Tools' Enchantments and Rarity\nfor Tool swapping, only after comparing if\nit's the correct tool to use on the target.", "tweakeroo.config.generic.comment.toolSwapPreferSilkTouch": "Consider your final Silk Touch preference\nwith tools whenever all other results are\nequal (Block Breaking Speed, Enchantments, Materials, etc).\nThis is useful for determining the default\npickaxe to choose when SilkTouchFirst is not\napplicable for the block that you are mining.\n§6NOTE: When this is disabled, the mod will always prefer\n§6to use the non-silk touch tool during the\n§6comparison if one is found and matches\n§6all of the criteria.", "tweakeroo.config.generic.comment.toolSwapBambooUsesSwordFirst": "Check if the targeted block is Bamboo,\nand if so; prefer using a Sword instead\nof an Axe to mine it.", "tweakeroo.config.generic.comment.toolSwapNeedsShearsFirst": "Check if the block requires Shears first,\nsuch as wool, vines or cobwebs.\nThe blocks chosen are based on the new\n'§b#malilib:needs_shears§r' Block Tag\nsince this doesn't exist in Vanilla.\n§6NOTE: This feature is only activate when\n§6the block is not properly matched in Vanilla.", "tweakeroo.config.generic.comment.toolSwapSilkTouchFirst": "Check if the block requires Silk Touch first,\nsuch as glass or an ender chest.\nThe blocks chosen is based on the new\n'§b#malilib:needs_silk_touch§r' Block Tag\nsince this doesn't exist in Vanilla.", "tweakeroo.config.generic.comment.toolSwapSilkTouchOres": "Check if the block is an Ore Block via the\n'§b#malilib:ore_blocks§r' tag, and then use\nSilk Touch First on it.\n§6NOTE: Disable this to use fortune.§r", "tweakeroo.config.generic.comment.toolSwapSilkTouchOverride": "Check the Silk Touch Override list for\nblocks to apply SilkTouchFirst to.\n§6NOTE: This can work without 'toolSwapSilkTouchFirst'\n§6being enabled, but it is designed to work\n§6the same, and can co-exist with it on.", "tweakeroo.config.generic.comment.toolSwitchIgnoredSlots": "The slots where the Tool Switch tweak does not work when they are active.", "tweakeroo.config.generic.comment.toolSwitchableSlots": "The slots that the Tool Switch tweak is allowed to put tools to.\nNote that Tool Switch can also switch to other slots in the hotbar,\nif they already have the preferred tool, but it will only\nswap new tools to these slots", "tweakeroo.config.generic.comment.weaponSwapBetterEnchants": "Consider a Weapons' Enchantments and Rarity\nfor Weapon swapping, only after comparing if \nit's the correct Weapon to use on the target.", "tweakeroo.config.generic.comment.zoomAdjustMouseSensitivity": "If enabled, then the mouse sensitivity is reduced\nwhile the zoom feature is enabled and the zoom key is active", "tweakeroo.config.generic.comment.zoomFov": "The FOV value used for the zoom feature", "tweakeroo.config.generic.comment.zoomResetFovOnActivate": "Reset the Camera Zoom FOV upon Deactivation,\nif the Mouse Scroll Wheel was used to adjust it", "tweakeroo.config.generic.name.accuratePlacementProtocol": "accuratePlacementProtocol", "tweakeroo.config.generic.name.accuratePlacementProtocolMode": "accuratePlacementProtocolMode", "tweakeroo.config.generic.name.afterClickerClickCount": "afterClickerClickCount", "tweakeroo.config.generic.name.angelBlockPlacementDistance": "angelBlockPlacementDistance", "tweakeroo.config.generic.name.areaSelectionUseAll": "areaSelectionUseAll", "tweakeroo.config.generic.name.blockReachDistance": "blockReachDistance", "tweakeroo.config.generic.name.blockTypeBreakRestrictionWarn": "blockTypeBreakRestrictionWarn", "tweakeroo.config.generic.name.breakingGridSize": "breakingGridSize", "tweakeroo.config.generic.name.breakingRestrictionMode": "breakingRestrictionMode", "tweakeroo.config.generic.name.bundleDisplayBgColor": "bundleDisplayBgColor", "tweakeroo.config.generic.name.bundleDisplayRequireShift": "bundleDisplayRequireShift", "tweakeroo.config.generic.name.bundleDisplayRowWidth": "bundleDisplayRowWidth", "tweakeroo.config.generic.name.chatBackgroundColor": "chatBackgroundColor", "tweakeroo.config.generic.name.chatTimeFormat": "chatTimeFormat", "tweakeroo.config.generic.name.clientPlacementRotation": "clientPlacementRotation", "tweakeroo.config.generic.name.customInventoryGuiScale": "customInventoryGuiScale", "tweakeroo.config.generic.name.debugLogging": "debugLogging", "tweakeroo.config.generic.name.darknessScaleOverrideValue": "darknessScaleOverrideValue", "tweakeroo.config.generic.name.elytraCameraIndicator": "elytraCameraIndicator", "tweakeroo.config.generic.name.entityReachDistance": "entityReachDistance", "tweakeroo.config.generic.name.entityTypeAttackRestrictionWarn": "entityTypeAttackRestrictionWarn", "tweakeroo.config.generic.name.fastBlockPlacementCount": "fastBlockPlacementCount", "tweakeroo.config.generic.name.fastLeftClickAllowTools": "fastLeftClickAllowTools", "tweakeroo.config.generic.name.fastLeftClickCount": "fastLeftClickCount", "tweakeroo.config.generic.name.fastPlacementRememberOrientation": "fastPlacementRememberOrientation", "tweakeroo.config.generic.name.fastRightClickCount": "fastRightClickCount", "tweakeroo.config.generic.name.fillCloneLimit": "fillCloneLimit", "tweakeroo.config.generic.name.flexibleBlockPlacementOverlayColor": "flexibleBlockPlacementOverlayColor", "tweakeroo.config.generic.name.flyDecelerationFactor": "flyDecelerationFactor", "tweakeroo.config.generic.name.flySpeedIncrement1": "flySpeedIncrement1", "tweakeroo.config.generic.name.flySpeedIncrement2": "flySpeedIncrement2", "tweakeroo.config.generic.name.flySpeedPreset1": "flySpeedPreset1", "tweakeroo.config.generic.name.flySpeedPreset2": "flySpeedPreset2", "tweakeroo.config.generic.name.flySpeedPreset3": "flySpeedPreset3", "tweakeroo.config.generic.name.flySpeedPreset4": "flySpeedPreset4", "tweakeroo.config.generic.name.freeCameraPlayerInputs": "freeCameraPlayerInputs", "tweakeroo.config.generic.name.freeCameraPlayerMovement": "freeCameraPlayerMovement", "tweakeroo.config.generic.name.gammaOverrideValue": "gammaOverrideValue", "tweakeroo.config.generic.name.handRestockPre": "handRestockPre", "tweakeroo.config.generic.name.handRestockPreThreshold": "handRestockPreThreshold", "tweakeroo.config.generic.name.hangableEntityBypassInverse": "hangableEntityBypassInverse", "tweakeroo.config.generic.name.hotbarSlotCycleMax": "hotbarSlotCycleMax", "tweakeroo.config.generic.name.hotbarSlotRandomizerMax": "hotbarSlotRandomizerMax", "tweakeroo.config.generic.name.hotbarSwapOverlayAlignment": "hotbarSwapOverlayAlignment", "tweakeroo.config.generic.name.hotbarSwapOverlayOffsetX": "hotbarSwapOverlayOffsetX", "tweakeroo.config.generic.name.hotbarSwapOverlayOffsetY": "hotbarSwapOverlayOffsetY", "tweakeroo.config.generic.name.inventoryPreviewVillagerBGColor": "inventoryPreviewVillagerBGColor", "tweakeroo.config.generic.name.itemSwapDurabilityThreshold": "itemSwapDurabilityThreshold", "tweakeroo.config.generic.name.itemUsePacketCheckBypass": "itemUsePacketCheckBypass", "tweakeroo.config.generic.name.mapPreviewRequireShift": "mapPreviewRequireShift", "tweakeroo.config.generic.name.mapPreviewSize": "mapPreviewSize", "tweakeroo.config.generic.name.periodicAttackInterval": "periodicAttackInterval", "tweakeroo.config.generic.name.periodicAttackResetIntervalOnActivate": "periodicAttackResetIntervalOnActivate", "tweakeroo.config.generic.name.periodicHoldAttackDuration": "periodicHoldAttackDuration", "tweakeroo.config.generic.name.periodicHoldAttackInterval": "periodicHoldAttackInterval", "tweakeroo.config.generic.name.periodicHoldAttackResetIntervalOnActivate": "periodicHoldAttackResetIntervalOnActivate", "tweakeroo.config.generic.name.periodicHoldUseDuration": "periodicHoldUseDuration", "tweakeroo.config.generic.name.periodicHoldUseInterval": "periodicHoldUseInterval", "tweakeroo.config.generic.name.periodicHoldUseResetIntervalOnActivate": "periodicHoldUseResetIntervalOnActivate", "tweakeroo.config.generic.name.periodicUseInterval": "periodicUseInterval", "tweakeroo.config.generic.name.periodicUseResetIntervalOnActivate": "periodicUseResetIntervalOnActivate", "tweakeroo.config.generic.name.permanentSneakAllowInGUIs": "permanentSneakAllowInGUIs", "tweakeroo.config.generic.name.placementGridSize": "placementGridSize", "tweakeroo.config.generic.name.placementLimit": "placementLimit", "tweakeroo.config.generic.name.placementRestrictionMode": "placementRestrictionMode", "tweakeroo.config.generic.name.placementRestrictionTiedToFast": "placementRestrictionTiedToFast", "tweakeroo.config.generic.name.potionWarningBeneficialOnly": "potionWarningBeneficialOnly", "tweakeroo.config.generic.name.potionWarningThreshold": "potionWarningThreshold", "tweakeroo.config.generic.name.rememberFlexibleFromClick": "rememberFlexibleFromClick", "tweakeroo.config.generic.name.renderLimitItem": "renderLimitItem", "tweakeroo.config.generic.name.renderLimitXPOrb": "renderLimitXPOrb", "tweakeroo.config.generic.name.sculkSensorPulseLength": "sculkSensorPulseLength", "tweakeroo.config.generic.name.selectiveBlocksHideEntities": "selectiveBlocksHideEntities", "tweakeroo.config.generic.name.selectiveBlocksHideParticles": "selectiveBlocksHideParticles", "tweakeroo.config.generic.name.selectiveBlocksNoHit": "selectiveBlocksNoHit", "tweakeroo.config.generic.name.selectiveBlocksTrackPistons": "selectiveBlocksTrackPistons", "tweakeroo.config.generic.name.serverDataSyncCacheRefresh": "serverDataSyncCacheRefresh", "tweakeroo.config.generic.name.serverDataSyncCacheTimeout": "serverDataSyncCacheTimeout", "tweakeroo.config.generic.name.serverNbtRequestRate": "serverNbtRequestRate", "tweakeroo.config.generic.name.shulkerDisplayBgColor": "shulkerDisplayBgColor", "tweakeroo.config.generic.name.shulkerDisplayEnderChest": "shulkerDisplayEnderChest", "tweakeroo.config.generic.name.shulkerDisplayRequireShift": "shulkerDisplayRequireShift", "tweakeroo.config.generic.name.slotSyncWorkaround": "slotSyncWorkaround", "tweakeroo.config.generic.name.slotSyncWorkaroundAlways": "slotSyncWorkaroundAlways", "tweakeroo.config.generic.name.snapAimIndicator": "snapAimIndicator", "tweakeroo.config.generic.name.snapAimIndicatorColor": "snapAimIndicatorColor", "tweakeroo.config.generic.name.snapAimMode": "snapAimMode", "tweakeroo.config.generic.name.snapAimOnlyCloseToAngle": "snapAimOnlyCloseToAngle", "tweakeroo.config.generic.name.snapAimPitchOvershoot": "snapAimPitchOvershoot", "tweakeroo.config.generic.name.snapAimPitchStep": "snapAimPitchStep", "tweakeroo.config.generic.name.snapAimThresholdPitch": "snapAimT<PERSON><PERSON><PERSON>", "tweakeroo.config.generic.name.snapAimThresholdYaw": "snapAimThresholdYaw", "tweakeroo.config.generic.name.snapAimYawStep": "snapAimYawStep", "tweakeroo.config.generic.name.structureBlockMaxSize": "structureBlockMaxSize", "tweakeroo.config.generic.name.toolSwapBetterEnchants": "toolSwapBetterEnchants", "tweakeroo.config.generic.name.toolSwapPreferSilkTouch": "toolSwapPreferSilkTouch", "tweakeroo.config.generic.name.toolSwapBambooUsesSwordFirst": "toolSwapBambooUsesSwordFirst", "tweakeroo.config.generic.name.toolSwapNeedsShearsFirst": "toolSwapNeedsShearsFirst", "tweakeroo.config.generic.name.toolSwapSilkTouchFirst": "toolSwapSilkTouchFirst", "tweakeroo.config.generic.name.toolSwapSilkTouchOres": "toolSwapSilkTouchOres", "tweakeroo.config.generic.name.toolSwapSilkTouchOverride": "toolSwapSilkTouchOverride", "tweakeroo.config.generic.name.toolSwitchIgnoredSlots": "toolSwitchIgnoredSlots", "tweakeroo.config.generic.name.toolSwitchableSlots": "toolSwitchableSlots", "tweakeroo.config.generic.name.weaponSwapBetterEnchants": "weaponSwapBetterEnchants", "tweakeroo.config.generic.name.zoomAdjustMouseSensitivity": "zoomAdjustMouseSensitivity", "tweakeroo.config.generic.name.zoomFov": "zoomFov", "tweakeroo.config.generic.name.zoomResetFovOnActivate": "zoomResetFovOnActivate", "tweakeroo.config.generic.prettyName.freeCameraPlayerInputs": "Free Camera Player Inputs", "tweakeroo.config.generic.prettyName.freeCameraPlayerMovement": "Free Camera Player Movement", "tweakeroo.config.generic.prettyName.toolSwapBetterEnchants": "Tool Swap Better Enchants", "tweakeroo.config.generic.prettyName.toolSwapBambooUsesSwordFirst": "Tool Swap Bamboo Uses Sword First", "tweakeroo.config.generic.prettyName.toolSwapNeedsShearsFirst": "Tool Swap Needs Shears First", "tweakeroo.config.generic.prettyName.toolSwapSilkTouchFirst": "Tool Swap Silk Touch First", "tweakeroo.config.generic.prettyName.toolSwapPreferSilkTouch": "Tool Swap Prefer Silk Touch", "tweakeroo.config.generic.prettyName.toolSwapSilkTouchOres": "Tool Swap Silk Touch Ores", "tweakeroo.config.generic.prettyName.toolSwapSilkTouchOverride": "Tool Swap Silk Touch Override", "tweakeroo.config.generic.prettyName.weaponSwapBetterEnchants": "Weapon Swap Better Enchants", "tweakeroo.config.hotkey.comment.accurateBlockPlacementInto": "The key to activate the accurate block placement\nmode/overlay for placing the block facing\ninto the clicked block face", "tweakeroo.config.hotkey.comment.accurateBlockPlacementReverse": "The key to activate the accurate block placement\nmode/overlay for placing the block facing\nthe opposite way from what it would be otherwise", "tweakeroo.config.hotkey.comment.areaSelectionAddToList": "Add selected blocks to list\nWritten by Andrew54757 under TweakFork", "tweakeroo.config.hotkey.comment.areaSelectionOffset": "The key to offset selection pos\nWritten by Andrew54757 under TweakFork", "tweakeroo.config.hotkey.comment.areaSelectionRemoveFromList": "remove selected blocks from list\nWritten by Andrew54757 under TweakFork", "tweakeroo.config.hotkey.comment.breakingRestrictionModeColumn": "Switch the Breaking Restriction mode to the Column mode", "tweakeroo.config.hotkey.comment.breakingRestrictionModeDiagonal": "Switch the Breaking Restriction mode to the Diagonal mode", "tweakeroo.config.hotkey.comment.breakingRestrictionModeFace": "Switch the Breaking Restriction mode to the Face mode", "tweakeroo.config.hotkey.comment.breakingRestrictionModeLayer": "Switch the Breaking Restriction mode to the Layer mode", "tweakeroo.config.hotkey.comment.breakingRestrictionModeLine": "Switch the Breaking Restriction mode to the Line mode", "tweakeroo.config.hotkey.comment.breakingRestrictionModePlane": "Switch the Breaking Restriction mode to the Plane mode", "tweakeroo.config.hotkey.comment.copySignText": "Copies the text from an already-placed sign.\nThat text can be used with the tweakSignCopy tweak.", "tweakeroo.config.hotkey.comment.elytraCamera": "The key to lock the current real player rotations, only allowing the\ninputs (mouse) to affect separate \"camera rotations\" used only for the rendering\nwhile this key is active.\nMeant for freely looking down/around while elytra flying straight.", "tweakeroo.config.hotkey.comment.flexibleBlockPlacementAdjacent": "The key to activate the flexible block placement\nmode/overlay for placing the block in an adjacent position", "tweakeroo.config.hotkey.comment.flexibleBlockPlacementOffset": "The key to activate the flexible block placement\nmode/overlay for placing the block in a\noffset or diagonal position", "tweakeroo.config.hotkey.comment.flexibleBlockPlacementRotation": "The key to activate the flexible block placement\nmode/overlay for placing the block with\na rotation/facing", "tweakeroo.config.hotkey.comment.flyIncrement1": "Change fly speed by increment 1", "tweakeroo.config.hotkey.comment.flyIncrement2": "Change fly speed by increment 2", "tweakeroo.config.hotkey.comment.flyPreset1": "Switch to fly preset 1", "tweakeroo.config.hotkey.comment.flyPreset2": "Switch to fly preset 2", "tweakeroo.config.hotkey.comment.flyPreset3": "Switch to fly preset 3", "tweakeroo.config.hotkey.comment.flyPreset4": "Switch to fly preset 4", "tweakeroo.config.hotkey.comment.freeCameraPlayerInputs": "Toggle the Generic -> freeCameraPlayerInputs option", "tweakeroo.config.hotkey.comment.freeCameraPlayerMovement": "Toggle the Generic -> freeCameraPlayerMovement option", "tweakeroo.config.hotkey.comment.hotbarScroll": "The key to hold to allow scrolling the hotbar\nthrough the player inventory rows", "tweakeroo.config.hotkey.comment.hotbarSwap1": "Swap the hotbar with the top-most inventory row", "tweakeroo.config.hotkey.comment.hotbarSwap2": "Swap the hotbar with the middle inventory row", "tweakeroo.config.hotkey.comment.hotbarSwap3": "Swap the hotbar with the bottom-most inventory row", "tweakeroo.config.hotkey.comment.hotbarSwapBase": "The base key to show the hotbar/inventory overlay", "tweakeroo.config.hotkey.comment.inventoryPreview": "The key to activate the inventory preview feature", "tweakeroo.config.hotkey.comment.inventoryPreviewToggleScreen": "Open a screen for inventory preview\nYou can use your mouse to see tooltips", "tweakeroo.config.hotkey.comment.openConfigGui": "The key open the in-game config GUI", "tweakeroo.config.hotkey.comment.placementRestrictionModeColumn": "Switch the Placement Restriction mode to the Column mode", "tweakeroo.config.hotkey.comment.placementRestrictionModeDiagonal": "Switch the Placement Restriction mode to the Diagonal mode", "tweakeroo.config.hotkey.comment.placementRestrictionModeFace": "Switch the Placement Restriction mode to the Face mode", "tweakeroo.config.hotkey.comment.placementRestrictionModeLayer": "Switch the Placement Restriction mode to the Layer mode", "tweakeroo.config.hotkey.comment.placementRestrictionModeLine": "Switch the Placement Restriction mode to the Line mode", "tweakeroo.config.hotkey.comment.placementRestrictionModePlane": "Switch the Placement Restriction mode to the Plane mode", "tweakeroo.config.hotkey.comment.placementYMirror": "The key to mirror the targeted y-position within the block", "tweakeroo.config.hotkey.comment.playerInventoryPeek": "The key to activate the player inventory peek/preview feature", "tweakeroo.config.hotkey.comment.sitDownNearbyPets": "Makes all nearby pets sit down", "tweakeroo.config.hotkey.comment.skipAllRendering": "Toggles skipping _all_ rendering", "tweakeroo.config.hotkey.comment.skipWorldRendering": "Toggles skipping world rendering", "tweakeroo.config.hotkey.comment.standUpNearbyPets": "Makes all nearby pets stand up", "tweakeroo.config.hotkey.comment.swapElytraChestplate": "Swaps the currently equipped item in the chest slot between an Elytra and a Chest Plate", "tweakeroo.config.hotkey.comment.toggleAccuratePlacementProtocol": "Toggles the value of the Generic -> 'accuratePlacementProtocol' option", "tweakeroo.config.hotkey.comment.toggleGrabCursor": "Grabs or ungrabs the mouse cursor, depending on the current state", "tweakeroo.config.hotkey.comment.toolPick": "Switches to the effective tool for the targeted block", "tweakeroo.config.hotkey.comment.writeMapsAsImages": "Writes all the currently available maps as images\nto the 'config/tweakeroo/map_images/<worldname>/' directory", "tweakeroo.config.hotkey.comment.zoomActivate": "Zoom activation hotkey\nWritten by Andrew54757 under TweakFork", "tweakeroo.config.hotkey.name.accurateBlockPlacementInto": "accurateBlockPlacementInto", "tweakeroo.config.hotkey.name.accurateBlockPlacementReverse": "accurateBlockPlacementReverse", "tweakeroo.config.hotkey.name.areaSelectionAddToList": "areaSelectionAddToList", "tweakeroo.config.hotkey.name.areaSelectionOffset": "areaSelectionOffset", "tweakeroo.config.hotkey.name.areaSelectionRemoveFromList": "areaSelectionRemoveFromList", "tweakeroo.config.hotkey.name.breakingRestrictionModeColumn": "breakingRestrictionModeColumn", "tweakeroo.config.hotkey.name.breakingRestrictionModeDiagonal": "breakingRestrictionModeDiagonal", "tweakeroo.config.hotkey.name.breakingRestrictionModeFace": "breakingRestrictionModeFace", "tweakeroo.config.hotkey.name.breakingRestrictionModeLayer": "breakingRestrictionModeLayer", "tweakeroo.config.hotkey.name.breakingRestrictionModeLine": "breakingRestrictionModeLine", "tweakeroo.config.hotkey.name.breakingRestrictionModePlane": "breakingRestrictionModePlane", "tweakeroo.config.hotkey.name.copySignText": "copySignText", "tweakeroo.config.hotkey.name.elytraCamera": "elytraCamera", "tweakeroo.config.hotkey.name.flexibleBlockPlacementAdjacent": "flexibleBlockPlacementAdjacent", "tweakeroo.config.hotkey.name.flexibleBlockPlacementOffset": "flexibleBlockPlacementOffset", "tweakeroo.config.hotkey.name.flexibleBlockPlacementRotation": "flexibleBlockPlacementRotation", "tweakeroo.config.hotkey.name.flyIncrement1": "flyIncrement1", "tweakeroo.config.hotkey.name.flyIncrement2": "flyIncrement2", "tweakeroo.config.hotkey.name.flyPreset1": "flyPreset1", "tweakeroo.config.hotkey.name.flyPreset2": "flyPreset2", "tweakeroo.config.hotkey.name.flyPreset3": "flyPreset3", "tweakeroo.config.hotkey.name.flyPreset4": "flyPreset4", "tweakeroo.config.hotkey.name.freeCameraPlayerInputs": "freeCameraPlayerInputs", "tweakeroo.config.hotkey.name.freeCameraPlayerMovement": "freeCameraPlayerMovement", "tweakeroo.config.hotkey.name.hotbarScroll": "hotbarScroll", "tweakeroo.config.hotkey.name.hotbarSwap1": "hotbarSwap1", "tweakeroo.config.hotkey.name.hotbarSwap2": "hotbarSwap2", "tweakeroo.config.hotkey.name.hotbarSwap3": "hotbarSwap3", "tweakeroo.config.hotkey.name.hotbarSwapBase": "hotbarSwapBase", "tweakeroo.config.hotkey.name.inventoryPreview": "inventoryPreview", "tweakeroo.config.hotkey.name.inventoryPreviewToggleScreen": "inventoryPreviewToggleScreen", "tweakeroo.config.hotkey.name.openConfigGui": "openConfigGui", "tweakeroo.config.hotkey.name.placementRestrictionModeColumn": "placementRestrictionModeColumn", "tweakeroo.config.hotkey.name.placementRestrictionModeDiagonal": "placementRestrictionModeDiagonal", "tweakeroo.config.hotkey.name.placementRestrictionModeFace": "placementRestrictionModeFace", "tweakeroo.config.hotkey.name.placementRestrictionModeLayer": "placementRestrictionModeLayer", "tweakeroo.config.hotkey.name.placementRestrictionModeLine": "placementRestrictionModeLine", "tweakeroo.config.hotkey.name.placementRestrictionModePlane": "placementRestrictionModePlane", "tweakeroo.config.hotkey.name.placementYMirror": "placementYMirror", "tweakeroo.config.hotkey.name.playerInventoryPeek": "playerInventoryPeek", "tweakeroo.config.hotkey.name.sitDownNearbyPets": "sitDownNearbyPets", "tweakeroo.config.hotkey.name.skipAllRendering": "skip<PERSON><PERSON><PERSON><PERSON>ing", "tweakeroo.config.hotkey.name.skipWorldRendering": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tweakeroo.config.hotkey.name.standUpNearbyPets": "standUpNearbyPets", "tweakeroo.config.hotkey.name.swapElytraChestplate": "swapElytraChestplate", "tweakeroo.config.hotkey.name.toggleAccuratePlacementProtocol": "toggleAccuratePlacementProtocol", "tweakeroo.config.hotkey.name.toggleGrabCursor": "toggleGrabCursor", "tweakeroo.config.hotkey.name.toolPick": "toolPick", "tweakeroo.config.hotkey.name.writeMapsAsImages": "writeMapsAsImages", "tweakeroo.config.hotkey.name.zoomActivate": "zoomActivate", "tweakeroo.config.internal.comment.darknessScaleValueOriginal": "The original darkness scale value, before the 'tweak darkness visibility' was enabled", "tweakeroo.config.internal.comment.flySpeedPreset": "This is just for the mod internally to track the\ncurrently selected fly speed preset", "tweakeroo.config.internal.comment.gammaValueOriginal": "The original gamma value, before the gamma override was enabled", "tweakeroo.config.internal.comment.hotbarScrollCurrentRow": "This is just for the mod internally to track the\n\"current hotbar row\" for the hotbar scrolling feature", "tweakeroo.config.internal.comment.slimeBlockSlipperinessOriginal": "The original slipperiness value of Slime Blocks", "tweakeroo.config.internal.comment.snapAimLastPitch": "The last snapped-to pitch value", "tweakeroo.config.internal.comment.snapAimLastYaw": "The last snapped-to yaw value", "tweakeroo.config.internal.name.darknessScaleValueOriginal": "darknessScaleValueOriginal", "tweakeroo.config.internal.name.flySpeedPreset": "flySpeedPreset", "tweakeroo.config.internal.name.gammaValueOriginal": "gammaValueOriginal", "tweakeroo.config.internal.name.hotbarScrollCurrentRow": "hotbarScrollCurrentRow", "tweakeroo.config.internal.name.slimeBlockSlipperinessOriginal": "slimeBlockSlipperinessOriginal", "tweakeroo.config.internal.name.snapAimLastPitch": "snapAimLastPitch", "tweakeroo.config.internal.name.snapAimLastYaw": "snapAimLastYaw", "tweakeroo.config.lists.comment.blockTypeBreakRestrictionBlackList": "The blocks that are NOT allowed to be broken while the Block Break Restriction tweak is enabled,\nif the blockBreakRestrictionListType is set to Black List", "tweakeroo.config.lists.comment.blockTypeBreakRestrictionListType": "The restriction list type for the Block Type Break Restriction tweak", "tweakeroo.config.lists.comment.blockTypeBreakRestrictionWhiteList": "The only blocks that can be broken while the Block Break Restriction tweak is enabled,\nif the blockBreakRestrictionListType is set to White List", "tweakeroo.config.lists.comment.creativeExtraItems": "Extra items that should be appended to the creative inventory.\nCurrently these will appear in the Transportation category.\nIn the future the group per added item will be customizable.", "tweakeroo.config.lists.comment.entityTypeAttackRestrictionBlackList": "The entities that are NOT allowed to be attacked while the Entity Attack Restriction tweak is enabled,\nif the entityAttackRestrictionListType is set to Black List", "tweakeroo.config.lists.comment.entityTypeAttackRestrictionListType": "The restriction list type for the Entity Type Attack Restriction tweak", "tweakeroo.config.lists.comment.entityTypeAttackRestrictionWhiteList": "The only entities that can be attacked while the Entity Attack Restriction tweak is enabled,\nif the entityAttackRestrictionListType is set to White List", "tweakeroo.config.lists.comment.entityWeaponMapping": "Mapping for what weapon should be used with the\n'tweakWeaponSwitch' tweak.\n'<default>' will be used when no other mapping is defined.\n'<ignore>' will not trigger a weapon switch.", "tweakeroo.config.lists.comment.fastPlacementItemBlackList": "The items that are NOT allowed to be used for the Fast Block Placement tweak,\nif the fastPlacementItemListType is set to Black List", "tweakeroo.config.lists.comment.fastPlacementItemListType": "The item restriction type for the Fast Block Placement tweak", "tweakeroo.config.lists.comment.fastPlacementItemWhiteList": "The items that are allowed to be used for the Fast Block Placement tweak,\nif the fastPLacementItemListType is set to White List", "tweakeroo.config.lists.comment.fastRightClickBlackList": "The items that are NOT allowed to be used for the Fast Right Click tweak,\nif the fastRightClickListType is set to Black List", "tweakeroo.config.lists.comment.fastRightClickBlockBlackList": "The blocks that are NOT allowed to be right clicked on with\nthe Fast Right Click tweak, if the fastRightClickBlockListType is set to Black List", "tweakeroo.config.lists.comment.fastRightClickBlockListType": "The targeted block restriction type for the Fast Right Click tweak", "tweakeroo.config.lists.comment.fastRightClickBlockWhiteList": "The blocks that are allowed to be right clicked on with\nthe Fast Right Click tweak, if the fastRightClickBlockListType is set to White List", "tweakeroo.config.lists.comment.fastRightClickListType": "The item restriction type for the Fast Right Click tweak", "tweakeroo.config.lists.comment.fastRightClickWhiteList": "The items that are allowed to be used for the Fast Right Click tweak,\nif the fastRightClickListType is set to White List", "tweakeroo.config.lists.comment.flatWorldPresets": "Custom flat world preset strings.\nThese are in the format: name;blocks_string;biome;generation_features;icon_item\nThe blocks string format is the vanilla format, such as: 62*minecraft:dirt,minecraft:grass\nThe biome can be the registry name, or the int ID\nThe icon item name format is minecraft:iron_nugget", "tweakeroo.config.lists.comment.handRestockBlackList": "The items that are NOT allowed to be restocked with the Hand Restock tweak,\nif the handRestockListType is set to Black List", "tweakeroo.config.lists.comment.handRestockListType": "The restriction list type for the Hand Restock tweak", "tweakeroo.config.lists.comment.handRestockWhiteList": "The only allowed items that can be restocked with the Hand Restock tweak,\nif the handRestockListType is set to White List", "tweakeroo.config.lists.comment.potionWarningBlackList": "The potion effects that will not be warned about", "tweakeroo.config.lists.comment.potionWarningListType": "The list type for potion warning effects", "tweakeroo.config.lists.comment.potionWarningWhiteList": "The only potion effects that will be warned about", "tweakeroo.config.lists.comment.silkTouchOverride": "When using tweak tool switch, add blocks or\nblock tags to this list to apply SilkTouchFirst to,\nin addition to using the '§b#malilib:needs_silk_touch§r' Block Tag\nbased 'toolSwapSilkTouchFirst' method when\n'toolSwapSilkTouchOverride' is enabled.\n\nA useful example, would be adding\n'§bminecraft:stone§r' to this list.\n\nThe Block Tag exists, so that you don't need\nto add dozens of blocks to a list like this.\nBut; You can configure this list to work this way\nby disabling 'toolSwapSilkTouchFirst' and only enabling\n`toolSwapSilkTouchOverride` instead.\nYou may then want to make use of some of\nthe existing `§b#malilib:§r` or '§b#minecraft:§r' block tags here;\nsuch as adding '§b#malilib:glass_blocks§r', for example\ninstead of typing in all 18 glass blocks one by one.", "tweakeroo.config.lists.comment.repairModeSlots": "The slots the repair mode should use\nValid values: mainhand, offhand, head, chest, legs, feet", "tweakeroo.config.lists.comment.selectiveBlocksBlacklist": "The block positions you want to blacklist\nWritten by Andrew54757 under TweakFork", "tweakeroo.config.lists.comment.selectiveBlocksListType": "The list type for selective blocks tweak\nWritten by Andrew54757 under TweakFork", "tweakeroo.config.lists.comment.selectiveBlocksWhitelist": "The block positions you want to whitelist\nWritten by Andrew54757 under TweakFork", "tweakeroo.config.lists.comment.unstackingItems": "The items that should be considered for the\n'tweakItemUnstackingProtection' tweak", "tweakeroo.config.lists.name.blockTypeBreakRestrictionBlackList": "blockTypeBreakRestrictionBlackList", "tweakeroo.config.lists.name.blockTypeBreakRestrictionListType": "blockTypeBreakRestrictionListType", "tweakeroo.config.lists.name.blockTypeBreakRestrictionWhiteList": "blockTypeBreakRestrictionWhiteList", "tweakeroo.config.lists.name.creativeExtraItems": "creativeExtraItems", "tweakeroo.config.lists.name.entityTypeAttackRestrictionBlackList": "entityTypeAttackRestrictionBlackList", "tweakeroo.config.lists.name.entityTypeAttackRestrictionListType": "entityTypeAttackRestrictionListType", "tweakeroo.config.lists.name.entityTypeAttackRestrictionWhiteList": "entityTypeAttackRestrictionWhiteList", "tweakeroo.config.lists.name.entityWeaponMapping": "entityWeaponMapping", "tweakeroo.config.lists.name.fastPlacementItemBlackList": "fastPlacementItemBlackList", "tweakeroo.config.lists.name.fastPlacementItemListType": "fastPlacementItemListType", "tweakeroo.config.lists.name.fastPlacementItemWhiteList": "fastPlacementItemWhiteList", "tweakeroo.config.lists.name.fastRightClickBlackList": "fastRightClickBlackList", "tweakeroo.config.lists.name.fastRightClickBlockBlackList": "fastRightClickBlockBlackList", "tweakeroo.config.lists.name.fastRightClickBlockListType": "fastRightClickBlockListType", "tweakeroo.config.lists.name.fastRightClickBlockWhiteList": "fastRightClickBlockWhiteList", "tweakeroo.config.lists.name.fastRightClickListType": "fastRightClickListType", "tweakeroo.config.lists.name.fastRightClickWhiteList": "fastRightClickWhiteList", "tweakeroo.config.lists.name.flatWorldPresets": "flatWorldPresets", "tweakeroo.config.lists.name.handRestockBlackList": "handRestockBlackList", "tweakeroo.config.lists.name.handRestockListType": "handRestockListType", "tweakeroo.config.lists.name.handRestockWhiteList": "handRestockWhiteList", "tweakeroo.config.lists.name.potionWarningBlackList": "potionWarningBlackList", "tweakeroo.config.lists.name.potionWarningListType": "potionWarningListType", "tweakeroo.config.lists.name.potionWarningWhiteList": "potionWarningWhiteList", "tweakeroo.config.lists.name.silkTouchOverride": "silkTouchOverride", "tweakeroo.config.lists.name.repairModeSlots": "repairModeSlots", "tweakeroo.config.lists.name.selectiveBlocksBlacklist": "selectiveBlocksBlacklist", "tweakeroo.config.lists.name.selectiveBlocksListType": "selectiveBlocksListType", "tweakeroo.config.lists.name.selectiveBlocksWhitelist": "selective<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tweakeroo.config.lists.name.unstackingItems": "unstackingItems", "tweakeroo.gui.button.config_gui.disables": "Yeets", "tweakeroo.gui.button.config_gui.fixes": "Fixes", "tweakeroo.gui.button.config_gui.generic": "Generic", "tweakeroo.gui.button.config_gui.generic_hotkeys": "Hotkeys", "tweakeroo.gui.button.config_gui.generic_config_hotkeys": "Generic Hotkeys", "tweakeroo.gui.button.config_gui.lists": "Lists", "tweakeroo.gui.button.config_gui.placement": "Placement stuff", "tweakeroo.gui.button.config_gui.tweaks": "Tweaks", "tweakeroo.gui.button.misc.command_block.hover.update_execution": "Whether or not multiple triggers per game tick are allowed", "tweakeroo.gui.button.misc.command_block.set_name": "Set name", "tweakeroo.gui.button.misc.command_block.update_execution.looping": "Looping", "tweakeroo.gui.button.misc.command_block.update_execution.off": "Loops: §cOFF", "tweakeroo.gui.button.misc.command_block.update_execution.on": "Loops: §aON", "tweakeroo.gui.label.easy_place_protocol.auto": "Auto", "tweakeroo.gui.label.easy_place_protocol.none": "None", "tweakeroo.gui.label.easy_place_protocol.slabs_only": "Slabs only", "tweakeroo.gui.label.easy_place_protocol.v2": "Version 2", "tweakeroo.gui.label.easy_place_protocol.v3": "Version 3", "tweakeroo.gui.title.configs": "Tweakeroo Configs - %s", "tweakeroo.hotkeys.category.disable_toggle_hotkeys": "Disable Toggle Hotkeys", "tweakeroo.hotkeys.category.generic_hotkeys": "Generic hotkeys", "tweakeroo.hotkeys.category.tweak_toggle_hotkeys": "Tweak toggle hotkeys", "tweakeroo.label.config_comment.single_player_only": "§6Note: This feature works either at all or§r\n§6at least fully only in single player§r", "tweakeroo.label.placement_restriction_mode.column": "Column", "tweakeroo.label.placement_restriction_mode.diagonal": "Diagonal", "tweakeroo.label.placement_restriction_mode.face": "Face", "tweakeroo.label.placement_restriction_mode.layer": "Layer", "tweakeroo.label.placement_restriction_mode.line": "Line", "tweakeroo.label.placement_restriction_mode.plane": "Plane", "tweakeroo.label.snap_aim_mode.both": "Yaw & Pitch", "tweakeroo.label.snap_aim_mode.pitch": "Pitch", "tweakeroo.label.snap_aim_mode.yaw": "Yaw", "tweakeroo.message.death_coordinates": "§6Y<PERSON> died§r @ [§b%d, %d, %d§r] in §a%s", "tweakeroo.message.focusing_game": "§6Focusing§f the game (grabbing the cursor)", "tweakeroo.message.potion_effects_running_out": "§6!! WARNING - %s potion effect(s) about to run out in %s seconds !!§r", "tweakeroo.message.repair_mode.swapped_repairable_item_to_slot": "Swapped a repairable item to %s slot", "tweakeroo.message.set_after_clicker_count_to": "Set After Clicker count to %s", "tweakeroo.message.set_breaking_grid_size_to": "Set Breaking Grid size to %s", "tweakeroo.message.set_breaking_restriction_mode_to": "Set Breaking Restriction mode to %s", "tweakeroo.message.set_fly_speed_preset_to": "Switched to fly speed preset %s (speed: %s)", "tweakeroo.message.set_fly_speed_to": "Set fly speed of preset %s to %s", "tweakeroo.message.set_hotbar_slot_cycle_max_to": "Set Hotbar Slot Cycle max slot to %s", "tweakeroo.message.set_hotbar_slot_randomizer_max_to": "Set Hotbar Slot Randomizer max slot to %s", "tweakeroo.message.set_periodic_attack_interval_to": "Set Periodic Attack Interval to %s", "tweakeroo.message.set_periodic_hold_attack_interval_to": "Set Periodic Hold Attack Interval to %s", "tweakeroo.message.set_periodic_hold_use_interval_to": "Set Periodic Hold Use Interval to %s", "tweakeroo.message.set_periodic_use_interval_to": "Set Periodic Use Interval to %s", "tweakeroo.message.set_placement_grid_size_to": "Set Placement Grid size to %s", "tweakeroo.message.set_placement_limit_to": "Set Placement Limit to %s", "tweakeroo.message.set_placement_restriction_mode_to": "Set Placement Restriction mode to %s", "tweakeroo.message.set_snap_aim_pitch_step_to": "Set Snap Aim pitch step to %s", "tweakeroo.message.set_snap_aim_yaw_step_to": "Set Snap Aim yaw step to %s", "tweakeroo.message.set_zoom_fov_to": "Set Camera Zoom FOV to %s", "tweakeroo.message.sign_text_copied": "Sign text copied", "tweakeroo.message.snapped_to_pitch": "Snapped to pitch %s", "tweakeroo.message.snapped_to_yaw": "Snapped to yaw %s", "tweakeroo.message.swapped_low_durability_item_for_better_durability": "Swapped a low durability item for one with more durability", "tweakeroo.message.swapped_low_durability_item_for_dummy_item": "Swapped a low durability item for a dummy item", "tweakeroo.message.swapped_low_durability_item_off_players_hand": "Swapped a low durability item off the player's hand", "tweakeroo.message.toggled": "Toggled %s %s", "tweakeroo.message.toggled_after_clicker_on": "Toggled After Clicker tweak %s, clicks: %s", "tweakeroo.message.toggled_breaking_grid_on": "Toggled Breaking Grid tweak %s, grid interval: %s", "tweakeroo.message.toggled_fast_placement_mode_on": "Toggled Fast Placement mode %s, mode: %s", "tweakeroo.message.toggled_fly_speed_on": "Toggled Fly Speed tweak %s, preset: %s, speed: %s", "tweakeroo.message.toggled_periodic": "Toggled %s %s, speed: %s", "tweakeroo.message.toggled_placement_grid_on": "Toggled Placement Grid tweak %s, grid interval: %s", "tweakeroo.message.toggled_placement_limit_on": "Toggled Placement Limit tweak %s, limit: %s", "tweakeroo.message.toggled_slot_cycle_on": "Toggled Hotbar Slot Cycle tweak %s, max slot: %s", "tweakeroo.message.toggled_slot_randomizer_on": "Toggled Hotbar Slot Randomizer tweak %s, max slot: %s", "tweakeroo.message.toggled_snap_aim_on_both": "Toggled Yaw & Pitch Snap Aim %s, with yaw step %s, pitch step %s", "tweakeroo.message.toggled_snap_aim_on_pitch": "Toggled Pitch Snap Aim %s, with pitch step size %s degrees", "tweakeroo.message.toggled_snap_aim_on_yaw": "Toggled Yaw Snap Aim %s, with yaw step size %s degrees", "tweakeroo.message.toggled_zoom_activate_off": "Camera Zoom Deactivated, FOV: %s", "tweakeroo.message.toggled_zoom_activate_on": "Camera Zoom Activated, FOV: %s", "tweakeroo.message.toggled_zoom_on": "Toggled Camera Zoom %s, FOV: %s", "tweakeroo.message.unfocusing_game": "§6Un-focusing§f the game (un-grabbing the cursor)", "tweakeroo.message.value.off": "OFF", "tweakeroo.message.value.on": "ON", "tweakeroo.message.warning.block_type_break_restriction": "§6Block breaking prevented by Block Type Break Restriction tweak", "tweakeroo.message.warning.entity_type_attack_restriction": "§6Entity attack prevented by Entity Type Attack Restriction tweak"}