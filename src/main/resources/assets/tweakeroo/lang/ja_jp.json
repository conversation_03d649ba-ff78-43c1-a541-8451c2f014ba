{"modmenu.descriptionTranslation.tweakeroo": "設定可能なクライアントサイドの調整機能を多数追加します", "tweakeroo.config.disable.comment.disableArmorStandRendering": "すべてのアーマースタンドエンティティのレンダリングを無効にします", "tweakeroo.config.disable.comment.disableAtmosphericFog": "プレイヤーと描画距離フォグの間に存在する\nすべての大気フォグを無効にします。", "tweakeroo.config.disable.comment.disableAxeStripping": "斧での原木の皮剥ぎを無効にします", "tweakeroo.config.disable.comment.disableBatSpawning": "シングルプレイヤーでのコウモリのスポーンを無効にします", "tweakeroo.config.disable.comment.disableBeaconBeamRendering": "ビーコンビームのレンダリングを無効にします", "tweakeroo.config.disable.comment.disableBlockBreakCooldown": "ブロック破壊のクールダウンを無効にします。\nブロックを破壊する間の時間を短縮します", "tweakeroo.config.disable.comment.disableBlockBreakingParticles": "ブロック破壊パーティクルを削除します。\n（これは元々nessieのusefulmodからのものです。）", "tweakeroo.config.disable.comment.disableBossBar": "ボスバーのレンダリングを無効にします", "tweakeroo.config.disable.comment.disableBossFog": "ボスモブが引き起こすフォグを削除します", "tweakeroo.config.disable.comment.disableChunkRendering": "チャンクの（再）レンダリングを無効にします。これにより、ブロックの変更が\n再び有効にしてF3 + Aでワールドレンダリングを更新するまで見えなくなります。\nブロックの変更があまり関係ない状況で、多くのブロック変更がある場所での\n低FPSの改善に役立つ場合があります。", "tweakeroo.config.disable.comment.disableClientEntityUpdates": "プレイヤーエンティティ以外のすべてのクライアント側エンティティ更新を無効にします。\nこれは主に過剰なエンティティ数に関連する問題を修正するために\n何かを行う必要がある状況を対象としています", "tweakeroo.config.disable.comment.disableClientLightUpdates": "すべてのクライアント側光源更新を無効にします", "tweakeroo.config.disable.comment.disableConstantChunkSaving": "通常の自動保存サイクルに加えて、\nゲームが毎ティック最大20チャンクを保存することを無効にします。", "tweakeroo.config.disable.comment.disableCreativeMenuInfestedBlocks": "クリエイティブ検索インベントリから虫食い石ブロックを削除します", "tweakeroo.config.disable.comment.disableDeadMobRendering": "死んだモブ（体力が0のエンティティ）のレンダリングを防ぎます", "tweakeroo.config.disable.comment.disableDeadMobTargeting": "体力が0のエンティティをターゲットすることを防ぎます。\nこれにより、例えば既に死んだモブを攻撃することを修正します。", "tweakeroo.config.disable.comment.disableDoubleTapSprint": "前進キーのダブルタップスプリントを無効にします", "tweakeroo.config.disable.comment.disableEntityRendering": "プレイヤーエンティティ以外のすべてのエンティティレンダリングを無効にします。\nこれは主に過剰なエンティティ数に関連する問題を修正するために\n何かを行う必要がある状況を対象としています", "tweakeroo.config.disable.comment.disableEntityTicking": "プレイヤーエンティティ以外のすべてのエンティティのティックを防ぎます", "tweakeroo.config.disable.comment.disableFallingBlockEntityRendering": "有効にすると、落下ブロックエンティティが全くレンダリングされなくなります", "tweakeroo.config.disable.comment.disableFirstPersonEffectParticles": "一人称視点でのポーション効果パーティクル/渦巻きを削除します\n（プレイヤー自身からの）", "tweakeroo.config.disable.comment.disableInventoryEffectRendering": "インベントリGUIからポーション効果のレンダリングを削除します", "tweakeroo.config.disable.comment.disableItemSwitchRenderCooldown": "trueの場合、持っているアイテムを切り替えたり使用したりする際の\nクールダウン/装備アニメーションがなくなります。", "tweakeroo.config.disable.comment.disableMobSpawnerMobRendering": "モブスポナーからのエンティティレンダリングを削除します", "tweakeroo.config.disable.comment.disableNauseaEffect": "吐き気の視覚効果を無効にします", "tweakeroo.config.disable.comment.disableNetherFog": "ネザーのフォグを削除します", "tweakeroo.config.disable.comment.disableNetherPortalSound": "ネザーポータルの音を無効にします", "tweakeroo.config.disable.comment.disableObserver": "オブザーバーが全く作動しないようにします", "tweakeroo.config.disable.comment.disableOffhandRendering": "オフハンドアイテムのレンダリングを無効にします", "tweakeroo.config.disable.comment.disableParticles": "すべてのパーティクルを無効にします", "tweakeroo.config.disable.comment.disablePortalGuiClosing": "有効にすると、ネザーポータル内でもGUIを開くことができます", "tweakeroo.config.disable.comment.disableRainEffects": "雨のレンダリングと音を無効にします", "tweakeroo.config.disable.comment.disableRenderDistanceFog": "描画距離周辺で増加するフォグを無効にします", "tweakeroo.config.disable.comment.disableRenderingScaffolding": "足場ブロックのレンダリングを無効にします", "tweakeroo.config.disable.comment.disableScoreboardRendering": "サイドバーのスコアボードレンダリングを削除します", "tweakeroo.config.disable.comment.disableShovelPathing": "シャベルで草などを道ブロックに変換することを無効にします", "tweakeroo.config.disable.comment.disableShulkerBoxTooltip": "シュルカーボックスの内容物のバニラテキストツールチップを無効にします", "tweakeroo.config.disable.comment.disableSignGui": "看板編集GUIが開くことを防ぎます", "tweakeroo.config.disable.comment.disableSkyDarkness": "y = 63以下での空の暗さを無効にします\n\n（閾値yを世界の底から2ブロック下に移動することで）", "tweakeroo.config.disable.comment.disableSlimeBlockSlowdown": "スライムブロック上を歩く際の減速を削除します。\n（これは元々nessieのusefulmodからのものです。）", "tweakeroo.config.disable.comment.disableStatusEffectHud": "ステータス効果HUDのレンダリングを無効にします（通常は\n画面の右上角にあります）", "tweakeroo.config.disable.comment.disableTileEntityRendering": "すべてのタイルエンティティレンダラーのレンダリングを防ぎます", "tweakeroo.config.disable.comment.disableTileEntityTicking": "すべてのタイルエンティティのティックを防ぎます", "tweakeroo.config.disable.comment.disableVillagerTradeLocking": "村人の取引がロックされることを防ぎます。レシピの使用回数が増加する際に\n最大使用回数も常に増加させることで実現します", "tweakeroo.config.disable.comment.disableWallUnsprint": "壁に触れてもスプリントモードから外れません", "tweakeroo.config.disable.comment.disableWorldViewBob": "世界の視点ボブ揺れ効果を無効にしますが、手は除きます\nIrisがインストールされている場合、この設定は失敗します。", "tweakeroo.config.disable.name.disableArmorStandRendering": "アーマースタンドレンダリング無効", "tweakeroo.config.disable.name.disableAtmosphericFog": "大気フォグ無効", "tweakeroo.config.disable.name.disableAxeStripping": "斧皮剥ぎ無効", "tweakeroo.config.disable.name.disableBatSpawning": "コウモリスポーン無効", "tweakeroo.config.disable.name.disableBeaconBeamRendering": "ビーコンビームレンダリング無効", "tweakeroo.config.disable.name.disableBlockBreakCooldown": "ブロック破壊クールダウン無効", "tweakeroo.config.disable.name.disableBlockBreakingParticles": "ブロック破壊パーティクル無効", "tweakeroo.config.disable.name.disableBossBar": "ボスバー無効", "tweakeroo.config.disable.name.disableBossFog": "ボスフォグ無効", "tweakeroo.config.disable.name.disableChunkRendering": "チャンクレンダリング無効", "tweakeroo.config.disable.name.disableClientEntityUpdates": "クライアントエンティティ更新無効", "tweakeroo.config.disable.name.disableClientLightUpdates": "クライアント光源更新無効", "tweakeroo.config.disable.name.disableConstantChunkSaving": "常時チャンク保存無効", "tweakeroo.config.disable.name.disableCreativeMenuInfestedBlocks": "クリエイティブメニュー虫食いブロック無効", "tweakeroo.config.disable.name.disableDeadMobRendering": "死亡モブレンダリング無効", "tweakeroo.config.disable.name.disableDeadMobTargeting": "死亡モブターゲット無効", "tweakeroo.config.disable.name.disableDoubleTapSprint": "ダブルタップスプリント無効", "tweakeroo.config.disable.name.disableEntityRendering": "エンティティレンダリング無効", "tweakeroo.config.disable.name.disableEntityTicking": "エンティティティック無効", "tweakeroo.config.disable.name.disableFallingBlockEntityRendering": "落下ブロックエンティティレンダリング無効", "tweakeroo.config.disable.name.disableFirstPersonEffectParticles": "一人称効果パーティクル無効", "tweakeroo.config.disable.name.disableInventoryEffectRendering": "インベントリ効果レンダリング無効", "tweakeroo.config.disable.name.disableItemSwitchRenderCooldown": "アイテム切替レンダリングクールダウン無効", "tweakeroo.config.disable.name.disableMobSpawnerMobRendering": "モブスポナーモブレンダリング無効", "tweakeroo.config.disable.name.disableNauseaEffect": "吐き気効果無効", "tweakeroo.config.disable.name.disableNetherFog": "ネザーフォグ無効", "tweakeroo.config.disable.name.disableNetherPortalSound": "ネザーポータル音無効", "tweakeroo.config.disable.name.disableObserver": "オブザーバー無効", "tweakeroo.config.disable.name.disableOffhandRendering": "オフハンドレンダリング無効", "tweakeroo.config.disable.name.disableParticles": "パーティクル無効", "tweakeroo.config.disable.name.disablePortalGuiClosing": "ポータルGUI閉じる無効", "tweakeroo.config.disable.name.disableRainEffects": "雨効果無効", "tweakeroo.config.disable.name.disableRenderDistanceFog": "描画距離フォグ無効", "tweakeroo.config.disable.name.disableRenderingScaffolding": "足場レンダリング無効", "tweakeroo.config.disable.name.disableScoreboardRendering": "スコアボードレンダリング無効", "tweakeroo.config.disable.name.disableShovelPathing": "シャベル道作り無効", "tweakeroo.config.disable.name.disableShulkerBoxTooltip": "シュルカーボックスツールチップ無効", "tweakeroo.config.disable.name.disableSignGui": "看板GUI無効", "tweakeroo.config.disable.name.disableSkyDarkness": "空の暗さ無効", "tweakeroo.config.disable.name.disableSlimeBlockSlowdown": "スライムブロック減速無効", "tweakeroo.config.disable.name.disableStatusEffectHud": "ステータス効果HUD無効", "tweakeroo.config.disable.name.disableTileEntityRendering": "タイルエンティティレンダリング無効", "tweakeroo.config.disable.name.disableTileEntityTicking": "タイルエンティティティック無効", "tweakeroo.config.disable.name.disableVillagerTradeLocking": "村人取引ロック無効", "tweakeroo.config.disable.name.disableWallUnsprint": "壁スプリント解除無効", "tweakeroo.config.disable.name.disableWorldViewBob": "世界視点ボブ無効", "tweakeroo.config.disable.prettyName.disableArmorStandRendering": "アーマースタンドレンダリング無効", "tweakeroo.config.disable.prettyName.disableAtmosphericFog": "大気フォグ無効", "tweakeroo.config.disable.prettyName.disableAxeStripping": "斧皮剥ぎ無効", "tweakeroo.config.disable.prettyName.disableBatSpawning": "コウモリスポーン無効", "tweakeroo.config.disable.prettyName.disableBeaconBeamRendering": "ビーコンビームレンダリング無効", "tweakeroo.config.disable.prettyName.disableBlockBreakCooldown": "ブロック破壊クールダウン無効", "tweakeroo.config.disable.prettyName.disableBlockBreakingParticles": "ブロック破壊パーティクル無効", "tweakeroo.config.disable.prettyName.disableBossBar": "ボスバー無効", "tweakeroo.config.disable.prettyName.disableBossFog": "ボスフォグ無効", "tweakeroo.config.disable.prettyName.disableChunkRendering": "チャンクレンダリング無効", "tweakeroo.config.disable.prettyName.disableClientEntityUpdates": "クライアントエンティティ更新無効", "tweakeroo.config.disable.prettyName.disableClientLightUpdates": "クライアント光源更新無効", "tweakeroo.config.disable.prettyName.disableConstantChunkSaving": "常時チャンク保存無効", "tweakeroo.config.disable.prettyName.disableCreativeMenuInfestedBlocks": "クリエイティブメニュー虫食いブロック無効", "tweakeroo.config.disable.prettyName.disableDeadMobRendering": "死亡モブレンダリング無効", "tweakeroo.config.disable.prettyName.disableDeadMobTargeting": "死亡モブターゲット無効", "tweakeroo.config.disable.prettyName.disableDoubleTapSprint": "ダブルタップスプリント無効", "tweakeroo.config.disable.prettyName.disableEntityRendering": "エンティティレンダリング無効", "tweakeroo.config.disable.prettyName.disableEntityTicking": "エンティティティック無効", "tweakeroo.config.disable.prettyName.disableFallingBlockEntityRendering": "落下ブロックエンティティレンダリング無効", "tweakeroo.config.disable.prettyName.disableFirstPersonEffectParticles": "一人称効果パーティクル無効", "tweakeroo.config.disable.prettyName.disableInventoryEffectRendering": "インベントリ効果レンダリング無効", "tweakeroo.config.disable.prettyName.disableItemSwitchRenderCooldown": "アイテム切替レンダリングクールダウン無効", "tweakeroo.config.disable.prettyName.disableMobSpawnerMobRendering": "モブスポナーモブレンダリング無効", "tweakeroo.config.disable.prettyName.disableNauseaEffect": "吐き気効果無効", "tweakeroo.config.disable.prettyName.disableNetherFog": "ネザーフォグ無効", "tweakeroo.config.disable.prettyName.disableNetherPortalSound": "ネザーポータル音無効", "tweakeroo.config.disable.prettyName.disableObserver": "オブザーバー無効", "tweakeroo.config.disable.prettyName.disableOffhandRendering": "オフハンドレンダリング無効", "tweakeroo.config.disable.prettyName.disableParticles": "パーティクル無効", "tweakeroo.config.disable.prettyName.disablePortalGuiClosing": "ポータルGUI閉じる無効", "tweakeroo.config.disable.prettyName.disableRainEffects": "雨効果無効", "tweakeroo.config.disable.prettyName.disableRenderDistanceFog": "描画距離フォグ無効", "tweakeroo.config.disable.prettyName.disableRenderingScaffolding": "足場レンダリング無効", "tweakeroo.config.disable.prettyName.disableScoreboardRendering": "スコアボードレンダリング無効", "tweakeroo.config.disable.prettyName.disableShovelPathing": "シャベル道作り無効", "tweakeroo.config.disable.prettyName.disableShulkerBoxTooltip": "シュルカーボックスツールチップ無効", "tweakeroo.config.disable.prettyName.disableSignGui": "看板GUI無効", "tweakeroo.config.disable.prettyName.disableSkyDarkness": "空の暗さ無効", "tweakeroo.config.disable.prettyName.disableSlimeBlockSlowdown": "スライムブロック減速無効", "tweakeroo.config.disable.prettyName.disableStatusEffectHud": "ステータス効果HUD無効", "tweakeroo.config.disable.prettyName.disableTileEntityRendering": "タイルエンティティレンダリング無効", "tweakeroo.config.disable.prettyName.disableTileEntityTicking": "タイルエンティティティック無効", "tweakeroo.config.disable.prettyName.disableVillagerTradeLocking": "村人取引ロック無効", "tweakeroo.config.disable.prettyName.disableWallUnsprint": "壁スプリント解除無効", "tweakeroo.config.disable.prettyName.disableWorldViewBob": "世界視点ボブ無効", "tweakeroo.config.feature_toggle.comment.tweakAccurateBlockPlacement": "Carpetmodに似た、より簡単なフレキシブル配置を有効にします。\n基本的にクリックしたブロック面に向かって、または\nそこから離れる方向に配置します。", "tweakeroo.config.feature_toggle.comment.tweakAfterClicker": "「アフタークリッカー」調整を有効にします。配置したばかりの\nブロックに自動的に右クリックを行います。\nリピーター（遅延設定）などに便利です。\n値を素早く調整するには、調整トグルキーバインドを\n押しながらスクロールしてください。", "tweakeroo.config.feature_toggle.comment.tweakAimLock": "エイムロックを有効にし、ヨーとピッチの回転を\n現在の値にロックします。\nこれはスナップエイムロックとは別で、\nスナップされた値にロックするものです。\nこれにより「自由に」現在の値にロックできます。", "tweakeroo.config.feature_toggle.comment.tweakAngelBlock": "「エンジェルブロック」調整を有効にします。\nクリエイティブモードで空中にブロックを配置できます。\n「<PERSON>lotato」技術によって動作します。", "tweakeroo.config.feature_toggle.comment.tweakAreaSelector": "エリアセレクターを有効にします\nTweakForkのAndrew54757によって作成", "tweakeroo.config.feature_toggle.comment.tweakAutoSwitchElytra": "落下時に自動的にエリトラに切り替え、\n着地時に前の胸装備に戻します。", "tweakeroo.config.feature_toggle.comment.tweakBlockReachOverride": "ブロックリーチ距離を\nGeneric -> blockReachDistanceで設定された値で上書きします", "tweakeroo.config.feature_toggle.comment.tweakBlockTypeBreakRestriction": "（手動で）破壊できるブロックを制限します。\nListsカテゴリの対応する'blockBreakRestriction*'設定を参照してください。", "tweakeroo.config.feature_toggle.comment.tweakBreakingGrid": "有効にすると、設定可能な間隔で\nグリッドパターンでのみブロックを破壊できます。\n間隔を素早く調整するには、調整トグルキーバインドを\n押しながらスクロールしてください。", "tweakeroo.config.feature_toggle.comment.tweakBreakingRestriction": "破壊制限モードを有効にします\n（平面、レイヤー、面、列、線、対角線）。\n基本的に攻撃キーを押している間、\nそれらのパターンでのみブロックを破壊できます。", "tweakeroo.config.feature_toggle.comment.tweakBundleDisplay": "バンドルアイテムにカーソルを合わせてシフトを押している時に\nバンドルの内容のプレビューをレンダリングします", "tweakeroo.config.feature_toggle.comment.tweakChatBackgroundColor": "デフォルトのチャット背景色を\nGenerics -> 'chatBackgroundColor'の色で上書きします", "tweakeroo.config.feature_toggle.comment.tweakChatPersistentText": "チャット入力テキストフィールドのテキストを保存し、\nチャットが再び開かれた時に復元します", "tweakeroo.config.feature_toggle.comment.tweakChatTimestamp": "チャットメッセージにタイムスタンプを追加します", "tweakeroo.config.feature_toggle.comment.tweakCommandBlockExtraFields": "コマンドブロックGUIに追加フィールドを追加します。\nコマンドブロックの名前設定と統計結果の表示用です", "tweakeroo.config.feature_toggle.comment.tweakCreativeExtraItems": "アイテムグループにカスタムアイテムを追加します。\nグループに追加するアイテムを制御するにはLists -> 'creativeExtraItems'を参照してください。\n注意：現在これらは交通グループに追加されます\n（最もアイテムが少ないため）が、将来的には\n追加アイテムごとにグループを設定可能になります", "tweakeroo.config.feature_toggle.comment.tweakCustomFlatPresets": "リストにカスタムフラットワールドプリセットを追加できます。\nプリセットはLists -> flatWorldPresetsで定義されます", "tweakeroo.config.feature_toggle.comment.tweakCustomFlyDeceleration": "クリエイティブまたはスペクテイターモードでの飛行減速を変更できます。\nこれは主により速い減速、つまり移動キーを離した時の\n「滑り」を少なくするためのものです。\nGeneric -> flyDecelerationRampValueを参照してください", "tweakeroo.config.feature_toggle.comment.tweakCustomInventoryScreenScale": "任意のインベントリ画面でカスタムGUIスケールを使用できます。\nスケール値についてはGeneric -> §ecustomInventoryGuiScale§rを参照してください", "tweakeroo.config.feature_toggle.comment.tweakDarknessVisibility": "有効にすると、暗闇ステータス効果の影響を受けている間の\n視認性が向上します。", "tweakeroo.config.feature_toggle.comment.tweakElytraCamera": "'elytraCamera'アクティベーションキーを押している間、実際のプレイヤーの回転をロックできます。\nコントロールはレンダリング/カメラ用の別の「カメラ回転」にのみ影響します。\nエリトラで真っ直ぐ飛行しながら下や周りを見回すためのものです。", "tweakeroo.config.feature_toggle.comment.tweakEmptyShulkerBoxesStack": "空のシュルカーボックスを64個までスタックできるようにします。\n注意：インベントリ内でもスタックします！\nサーバーでは、同じことを行うmodがない限り\n同期ずれ/不具合を引き起こします。\nシングルプレイヤーではシュルカーボックスベースのシステム動作が変わります。", "tweakeroo.config.feature_toggle.comment.tweakEntityReachOverride": "エンティティリーチ距離を\nGeneric -> entityReachDistanceで設定された値で上書きします", "tweakeroo.config.feature_toggle.comment.tweakEntityTypeAttackRestriction": "（手動で）攻撃できるエンティティを制限します。\nListsカテゴリの対応する'entityAttackRestriction*'設定を参照してください。", "tweakeroo.config.feature_toggle.comment.tweakExplosionReducedParticles": "有効にすると、すべての爆発パーティクルが\nEXPLOSION_LARGEやEXPLOSION_HUGEパーティクルの代わりに\nEXPLOSION_NORMALパーティクルを使用します", "tweakeroo.config.feature_toggle.comment.tweakF3Cursor": "F3画面カーソルを常にレンダリングします", "tweakeroo.config.feature_toggle.comment.tweakFakeSneakPlacement": "この調整は、実際にクリックしたブロックから\n隣接する空気ブロックにクリック位置をオフセットします。\nこれにより基本的に、インベントリGUIを開くなどの\nクリックアクションがあるブロックに対して、\nスニークせずにブロックを配置できます。これは実際には\nスニークを偽装するわけではなく、見た目の効果が似ているだけです。", "tweakeroo.config.feature_toggle.comment.tweakFakeSneaking": "「偽スニーク」を有効にします。つまり、移動速度を\n遅くすることなく端から落ちることを防ぎます", "tweakeroo.config.feature_toggle.comment.tweakFastBlockPlacement": "新しいブロックにカーソルを移動する際の\n高速/便利なブロック配置を有効にします", "tweakeroo.config.feature_toggle.comment.tweakFastLeftClick": "攻撃ボタン（左クリック）を押している間の\n自動高速左クリックを有効にします。\nティックあたりのクリック数はGeneric設定で設定されます。", "tweakeroo.config.feature_toggle.comment.tweakFastRightClick": "使用ボタン（右クリック）を押している間の\n自動高速右クリックを有効にします。\nティックあたりのクリック数はGeneric設定で設定されます。", "tweakeroo.config.feature_toggle.comment.tweakFillCloneLimit": "シングルプレイヤーでの/fillと/cloneコマンドの\nブロック制限の上書きを有効にします。\n新しい制限はGeneric設定の\n'fillCloneLimit'設定値で設定できます", "tweakeroo.config.feature_toggle.comment.tweakFlexibleBlockPlacement": "それらのモード用のホットキーを押している間、\n異なる向きやオフセットでブロックを配置できます。", "tweakeroo.config.feature_toggle.comment.tweakFlySpeed": "クリエイティブまたはスペクテイターモードでの飛行速度の上書きと\nいくつかのプリセットの使用を有効にします", "tweakeroo.config.feature_toggle.comment.tweakFreeCamera": "スペクテイターモードに似たフリーカメラモードを有効にしますが、\nプレイヤーは最初にフリーカメラモードを\nアクティベートした場所に留まります", "tweakeroo.config.feature_toggle.comment.tweakGammaOverride": "ビデオ設定のガンマ値を\nGeneric設定で設定された値で上書きします", "tweakeroo.config.feature_toggle.comment.tweakHandRestock": "前のスタックがなくなった時に\nメインハンドまたはオフハンドに新しいスタックを交換します", "tweakeroo.config.feature_toggle.comment.tweakHangableEntityBypass": "掛けられるエンティティ（アイテムフレームと絵画）をターゲットしないようにします。\nGeneric -> hangableEntityBypassInverseオプションを使用して、\nエンティティをターゲットできるようにするためにスニークする必要があるか、\nスニークしない必要があるかを制御できます。", "tweakeroo.config.feature_toggle.comment.tweakHoldAttack": "攻撃ボタンを押し続けることをエミュレートします", "tweakeroo.config.feature_toggle.comment.tweakHoldUse": "使用ボタンを押し続けることをエミュレートします", "tweakeroo.config.feature_toggle.comment.tweakHotbarScroll": "スクロールによるホットバー交換機能を有効にします", "tweakeroo.config.feature_toggle.comment.tweakHotbarSlotCycle": "各ブロック配置後に選択されたホットバースロットを\n設定された最大スロット番号まで循環させます。\n値を素早く調整するには、調整トグルキーバインドを\n押しながらスクロールしてください。", "tweakeroo.config.feature_toggle.comment.tweakHotbarSlotRandomizer": "各ブロック配置後に選択されたホットバースロットを\n設定された最大スロット番号までランダム化します。\n値を素早く調整するには、調整トグルキーバインドを\n押しながらスクロールしてください。", "tweakeroo.config.feature_toggle.comment.tweakHotbarSwap": "ホットキーによるホットバー交換機能を有効にします", "tweakeroo.config.feature_toggle.comment.tweakInventoryPreview": "インベントリを持つブロックまたはエンティティにカーソルを合わせて\n設定されたホットキーを押している間、\nインベントリプレビューを有効にします。\n§61.21+ MiniHUDバージョンを優先して非推奨", "tweakeroo.config.feature_toggle.comment.tweakItemUnstackingProtection": "有効にすると、Lists -> unstackingItemsで設定されたアイテムが\n使用時にこぼれ出ることを防ぎます。\nこれは例えば、バケツを溶岩に投げ込むことを\n防ぐためのものです。", "tweakeroo.config.feature_toggle.comment.tweakLavaVisibility": "有効にすると、水中呼吸と水中採掘のエンチャントレベル、\nおよび火炎耐性効果がアクティブであることで、\n溶岩の下での視認性が大幅に向上します。", "tweakeroo.config.feature_toggle.comment.tweakMapPreview": "有効にすると、インベントリ内の地図にシフトを押しながら\nカーソルを合わせると地図のプレビューがレンダリングされます", "tweakeroo.config.feature_toggle.comment.tweakMovementKeysLast": "有効にすると、反対の移動キーが互いにキャンセルされず、\n代わりに最後に押されたキーがアクティブな入力になります。", "tweakeroo.config.feature_toggle.comment.tweakPeriodicAttack": "定期的な攻撃（左クリック）を有効にします\nGeneric -> periodicAttackIntervalで間隔を設定してください", "tweakeroo.config.feature_toggle.comment.tweakPeriodicHoldAttack": "設定可能な時間だけ定期的に攻撃を保持することを有効にします。\nGeneric -> periodicHoldAttackIntervalで間隔を設定し、\nperiodicHoldAttackDurationで持続時間を設定してください\n§6注意：通常のホールド攻撃や定期攻撃を\n§6同時に使用しないでください", "tweakeroo.config.feature_toggle.comment.tweakPeriodicHoldUse": "設定可能な時間だけ定期的に使用を保持することを有効にします。\nGeneric -> periodicHoldUseIntervalで間隔を設定し、\nperiodicHoldUseDurationで持続時間を設定してください\n§6注意：通常のホールド使用や定期使用を\n§6同時に使用しないでください", "tweakeroo.config.feature_toggle.comment.tweakPeriodicUse": "定期的な使用（右クリック）を有効にします\nGeneric -> periodicUseIntervalで間隔を設定してください", "tweakeroo.config.feature_toggle.comment.tweakPermanentSneak": "有効にすると、プレイヤーは常にスニークします", "tweakeroo.config.feature_toggle.comment.tweakPermanentSprint": "有効にすると、プレイヤーは前進時に常にスプリントします", "tweakeroo.config.feature_toggle.comment.tweakPickBeforePlace": "有効にすると、各ブロック配置前に、配置しようとしている\n同じブロックが手に切り替わります", "tweakeroo.config.feature_toggle.comment.tweakPlacementGrid": "有効にすると、設定可能な間隔で\nグリッドパターンでのみブロックを配置できます。\n値を素早く調整するには、調整トグルキーバインドを\n押しながらスクロールしてください。", "tweakeroo.config.feature_toggle.comment.tweakPlacementLimit": "有効にすると、使用/右クリックあたり\n設定された数のブロックのみ配置できます。\n値を素早く調整するには、調整トグルキーバインドを\n押しながらスクロールしてください。", "tweakeroo.config.feature_toggle.comment.tweakPlacementRestriction": "配置制限モードを有効にします\n（平面、レイヤー、面、列、線、対角線）", "tweakeroo.config.feature_toggle.comment.tweakPlacementRestrictionFirst": "ブロック配置を制限し、最初にクリックした\n同じブロックタイプに対してのみ\nブロックを配置できるようにします", "tweakeroo.config.feature_toggle.comment.tweakPlacementRestrictionHand": "ブロック配置を制限し、手に持っている\n同じブロックタイプに対してのみ\nブロックを配置できるようにします", "tweakeroo.config.feature_toggle.comment.tweakPlayerInventoryPeek": "設定されたホットキーを押している間、\nプレイヤーインベントリのピーク/プレビューを有効にします。", "tweakeroo.config.feature_toggle.comment.tweakPlayerListAlwaysVisible": "有効にすると、キー（デフォルトではタブ）を\n押し続けることなく、プレイヤーリストが常にレンダリングされます", "tweakeroo.config.feature_toggle.comment.tweakPotionWarning": "非環境ポーション効果が切れそうになった時に\nホットバーに警告メッセージを表示します", "tweakeroo.config.feature_toggle.comment.tweakPrintDeathCoordinates": "死亡時にプレイヤーの座標をチャットに表示することを有効にします。\nこの機能は元々nessieのusefulmodからのものです。", "tweakeroo.config.feature_toggle.comment.tweakRenderEdgeChunks": "最端のクライアント読み込みチャンクのレンダリングを許可します。\nバニラでは隣接するすべてのチャンクが読み込まれていない\nチャンクのレンダリングを許可しないため、\nクライアントの読み込み済みの最端チャンクはバニラではレンダリングされません。\n§lこれはフリーカメラモードでも非常に役立ちます！§r", "tweakeroo.config.feature_toggle.comment.tweakRenderInvisibleEntities": "有効にすると、透明なエンティティが\nスペクテイターモードのようにレンダリングされます。", "tweakeroo.config.feature_toggle.comment.tweakRenderLimitEntities": "フレームごとにレンダリングする特定タイプのエンティティ数の\n制限を有効にします。現在XPオーブとアイテムエンティティが\nサポートされています。制限についてはGeneric設定を参照してください。", "tweakeroo.config.feature_toggle.comment.tweakRepairMode": "有効にすると、手に持った完全に修理されたアイテムが\n修繕エンチャントが付いた損傷したアイテムと交換されます。", "tweakeroo.config.feature_toggle.comment.tweakSculkPulseLength": "スカルクセンサーのパルス長を変更できます。Generic -> sculkSensorPulseLengthでパルス長を設定してください", "tweakeroo.config.feature_toggle.comment.tweakSelectiveBlocksRenderOutline": "リストされたブロックにアウトラインをレンダリングします\nTweakForkのAndrew54757によって作成", "tweakeroo.config.feature_toggle.comment.tweakSelectiveBlocksRendering": "選択的に表示されるブロックレンダリングを有効にします\nTweakForkのAndrew54757によって作成", "tweakeroo.config.feature_toggle.comment.tweakServerDataSync": "シュルカーボックスなどのエンティティにサーバーデータシンカーを使用し、\nServuxなどでサーバー上でinventoryPreviewが動作するようにします。\n§6このオプションがtrueに設定されていても、シンカーが動作するには\n§6サーバーのオペレーターであるか、サーバーサイドmodを\n§6インストールする必要があります。", "tweakeroo.config.feature_toggle.comment.tweakServerDataSyncBackup": "サーバーデータシンカーは、Servuxが利用できない時に\nバニラNbtQueryRequestパケットを使用するよう設定できます", "tweakeroo.config.feature_toggle.comment.tweakShulkerBoxDisplay": "インベントリ内でシュルカーボックスにカーソルを合わせて\nシフトを押している時のシュルカーボックス内容表示を有効にします", "tweakeroo.config.feature_toggle.comment.tweakSignCopy": "有効にすると、配置された看板は\n前に配置された看板のテキストを使用します。\ntweakNoSignGuiと組み合わせて、最初の看板を作成後に\nその調整を有効にすることで、看板のコピーを素早く配置できます。", "tweakeroo.config.feature_toggle.comment.tweakSnapAim": "スナップエイム調整を有効にし、プレイヤーを事前設定された正確なヨー回転に向かせます", "tweakeroo.config.feature_toggle.comment.tweakSnapAimLock": "スナップエイムロックを有効にし、ヨーおよび/またはピッチ回転を\n現在スナップされた値にロックします", "tweakeroo.config.feature_toggle.comment.tweakSneak_1_15_2": "1.15.2のスニーク動作を復元します", "tweakeroo.config.feature_toggle.comment.tweakSpectatorTeleport": "スペクテイターが他のスペクテイターにテレポートできるようにします。\nこれは元々nessieのusefulmodからのものです。", "tweakeroo.config.feature_toggle.comment.tweakStructureBlockLimit": "ストラクチャーブロック制限の上書きを許可します。\n新しい制限はGeneric -> structureBlockMaxSizeで設定されます", "tweakeroo.config.feature_toggle.comment.tweakSwapAlmostBrokenTools": "有効にすると、手に持った壊れそうな耐久性のあるアイテムが\n新しいものと交換されます", "tweakeroo.config.feature_toggle.comment.tweakTabCompleteCoordinate": "有効にすると、ブロックを見ていない時の座標のタブ補完で\n~文字を追加する代わりにプレイヤーの位置を使用します。", "tweakeroo.config.feature_toggle.comment.tweakToolSwitch": "ターゲットブロックに対する効果的なツールへの自動切り替えを有効にします", "tweakeroo.config.feature_toggle.comment.tweakWaterVisibility": "有効にすると、水中呼吸と水中採掘のエンチャントレベルにより、\n水中での視認性が大幅に向上します。", "tweakeroo.config.feature_toggle.comment.tweakWeaponSwitch": "ターゲットエンティティに対する武器への自動切り替えを有効にします", "tweakeroo.config.feature_toggle.comment.tweakYMirror": "ブロック境界内でターゲットのy位置をミラーリングします。\nこれは基本的に、例えば別のハーフブロックに対して配置する必要がある場合に、\nハーフブロックや階段を通常とは逆の上/下状態で配置するためのものです。", "tweakeroo.config.feature_toggle.comment.tweakZoom": "ズームホットキーを使用してズームインすることを有効にします", "tweakeroo.config.feature_toggle.name.tweakAccurateBlockPlacement": "正確ブロック配置", "tweakeroo.config.feature_toggle.name.tweakAfterClicker": "アフタークリッカー", "tweakeroo.config.feature_toggle.name.tweakAimLock": "エイムロック", "tweakeroo.config.feature_toggle.name.tweakAngelBlock": "エンジェルブロック", "tweakeroo.config.feature_toggle.name.tweakAreaSelector": "エリアセレクター", "tweakeroo.config.feature_toggle.name.tweakAutoSwitchElytra": "エリトラ自動切替", "tweakeroo.config.feature_toggle.name.tweakBlockReachOverride": "§6ブロックリーチ上書き§r", "tweakeroo.config.feature_toggle.name.tweakBlockTypeBreakRestriction": "ブロックタイプ破壊制限", "tweakeroo.config.feature_toggle.name.tweakBreakingGrid": "破壊グリッド", "tweakeroo.config.feature_toggle.name.tweakBreakingRestriction": "破壊制限", "tweakeroo.config.feature_toggle.name.tweakBundleDisplay": "バンドル表示", "tweakeroo.config.feature_toggle.name.tweakChatBackgroundColor": "チャット背景色", "tweakeroo.config.feature_toggle.name.tweakChatPersistentText": "チャット永続テキスト", "tweakeroo.config.feature_toggle.name.tweakChatTimestamp": "チャットタイムスタンプ", "tweakeroo.config.feature_toggle.name.tweakCommandBlockExtraFields": "コマンドブロック追加フィールド", "tweakeroo.config.feature_toggle.name.tweakCreativeExtraItems": "クリエイティブ追加アイテム", "tweakeroo.config.feature_toggle.name.tweakCustomFlatPresets": "カスタムフラットプリセット", "tweakeroo.config.feature_toggle.name.tweakCustomFlyDeceleration": "カスタム飛行減速", "tweakeroo.config.feature_toggle.name.tweakCustomInventoryScreenScale": "カスタムインベントリ画面スケール", "tweakeroo.config.feature_toggle.name.tweakDarknessVisibility": "暗闇視認性", "tweakeroo.config.feature_toggle.name.tweakElytraCamera": "エリトラカメラ", "tweakeroo.config.feature_toggle.name.tweakEmptyShulkerBoxesStack": "§6空シュルカーボックススタック§r", "tweakeroo.config.feature_toggle.name.tweakEntityReachOverride": "§6エンティティリーチ上書き§r", "tweakeroo.config.feature_toggle.name.tweakEntityTypeAttackRestriction": "エンティティタイプ攻撃制限", "tweakeroo.config.feature_toggle.name.tweakExplosionReducedParticles": "爆発パーティクル削減", "tweakeroo.config.feature_toggle.name.tweakF3Cursor": "F3カーソル", "tweakeroo.config.feature_toggle.name.tweakFakeSneakPlacement": "偽スニーク配置", "tweakeroo.config.feature_toggle.name.tweakFakeSneaking": "偽スニーク", "tweakeroo.config.feature_toggle.name.tweakFastBlockPlacement": "高速ブロック配置", "tweakeroo.config.feature_toggle.name.tweakFastLeftClick": "高速左クリック", "tweakeroo.config.feature_toggle.name.tweakFastRightClick": "高速右クリック", "tweakeroo.config.feature_toggle.name.tweakFillCloneLimit": "§6Fill Clone制限§r", "tweakeroo.config.feature_toggle.name.tweakFlexibleBlockPlacement": "フレキシブルブロック配置", "tweakeroo.config.feature_toggle.name.tweakFlySpeed": "飛行速度", "tweakeroo.config.feature_toggle.name.tweakFreeCamera": "フリーカメラ", "tweakeroo.config.feature_toggle.name.tweakGammaOverride": "ガンマ上書き", "tweakeroo.config.feature_toggle.name.tweakHandRestock": "ハンド補充", "tweakeroo.config.feature_toggle.name.tweakHangableEntityBypass": "掛けられるエンティティバイパス", "tweakeroo.config.feature_toggle.name.tweakHoldAttack": "攻撃ホールド", "tweakeroo.config.feature_toggle.name.tweakHoldUse": "使用ホールド", "tweakeroo.config.feature_toggle.name.tweakHotbarScroll": "ホットバースクロール", "tweakeroo.config.feature_toggle.name.tweakHotbarSlotCycle": "ホットバースロット循環", "tweakeroo.config.feature_toggle.name.tweakHotbarSlotRandomizer": "ホットバースロットランダム化", "tweakeroo.config.feature_toggle.name.tweakHotbarSwap": "ホットバー交換", "tweakeroo.config.feature_toggle.name.tweakInventoryPreview": "§6インベントリプレビュー§r", "tweakeroo.config.feature_toggle.name.tweakItemUnstackingProtection": "アイテムアンスタック保護", "tweakeroo.config.feature_toggle.name.tweakLavaVisibility": "溶岩視認性", "tweakeroo.config.feature_toggle.name.tweakMapPreview": "地図プレビュー", "tweakeroo.config.feature_toggle.name.tweakMovementKeysLast": "移動キー最後", "tweakeroo.config.feature_toggle.name.tweakPeriodicAttack": "定期攻撃", "tweakeroo.config.feature_toggle.name.tweakPeriodicHoldAttack": "定期攻撃ホールド", "tweakeroo.config.feature_toggle.name.tweakPeriodicHoldUse": "定期使用ホールド", "tweakeroo.config.feature_toggle.name.tweakPeriodicUse": "定期使用", "tweakeroo.config.feature_toggle.name.tweakPermanentSneak": "永続スニーク", "tweakeroo.config.feature_toggle.name.tweakPermanentSprint": "永続スプリント", "tweakeroo.config.feature_toggle.name.tweakPickBeforePlace": "配置前ピック", "tweakeroo.config.feature_toggle.name.tweakPlacementGrid": "配置グリッド", "tweakeroo.config.feature_toggle.name.tweakPlacementLimit": "配置制限", "tweakeroo.config.feature_toggle.name.tweakPlacementRestriction": "配置制限", "tweakeroo.config.feature_toggle.name.tweakPlacementRestrictionFirst": "配置制限最初", "tweakeroo.config.feature_toggle.name.tweakPlacementRestrictionHand": "配置制限ハンド", "tweakeroo.config.feature_toggle.name.tweakPlayerInventoryPeek": "プレイヤーインベントリピーク", "tweakeroo.config.feature_toggle.name.tweakPlayerListAlwaysVisible": "プレイヤーリスト常時表示", "tweakeroo.config.feature_toggle.name.tweakPotionWarning": "ポーション警告", "tweakeroo.config.feature_toggle.name.tweakPrintDeathCoordinates": "死亡座標表示", "tweakeroo.config.feature_toggle.name.tweakRenderEdgeChunks": "エッジチャンクレンダリング", "tweakeroo.config.feature_toggle.name.tweakRenderInvisibleEntities": "透明エンティティレンダリング", "tweakeroo.config.feature_toggle.name.tweakRenderLimitEntities": "エンティティレンダリング制限", "tweakeroo.config.feature_toggle.name.tweakRepairMode": "修理モード", "tweakeroo.config.feature_toggle.name.tweakSculkPulseLength": "§6スカルクパルス長§r", "tweakeroo.config.feature_toggle.name.tweakSelectiveBlocksRenderOutline": "選択ブロックレンダリングアウトライン", "tweakeroo.config.feature_toggle.name.tweakSelectiveBlocksRendering": "選択ブロックレンダリング", "tweakeroo.config.feature_toggle.name.tweakServerDataSync": "サーバーデータ同期", "tweakeroo.config.feature_toggle.name.tweakServerDataSyncBackup": "サーバーデータ同期バックアップ", "tweakeroo.config.feature_toggle.name.tweakShulkerBoxDisplay": "シュルカーボックス表示", "tweakeroo.config.feature_toggle.name.tweakSignCopy": "看板コピー", "tweakeroo.config.feature_toggle.name.tweakSnapAim": "スナップエイム", "tweakeroo.config.feature_toggle.name.tweakSnapAimLock": "スナップエイムロック", "tweakeroo.config.feature_toggle.name.tweakSneak_1_15_2": "スニーク1.15.2", "tweakeroo.config.feature_toggle.name.tweakSpectatorTeleport": "スペクテイターテレポート", "tweakeroo.config.feature_toggle.name.tweakStructureBlockLimit": "§6ストラクチャーブロック制限§r", "tweakeroo.config.feature_toggle.name.tweakSwapAlmostBrokenTools": "破損寸前ツール交換", "tweakeroo.config.feature_toggle.name.tweakTabCompleteCoordinate": "タブ補完座標", "tweakeroo.config.feature_toggle.name.tweakToolSwitch": "ツール切替", "tweakeroo.config.feature_toggle.name.tweakWaterVisibility": "水中視認性", "tweakeroo.config.feature_toggle.name.tweakWeaponSwitch": "武器切替", "tweakeroo.config.feature_toggle.name.tweakYMirror": "Yミラー", "tweakeroo.config.feature_toggle.name.tweakZoom": "ズーム", "tweakeroo.config.feature_toggle.prettyName.tweakAccurateBlockPlacement": "正確ブロック配置", "tweakeroo.config.feature_toggle.prettyName.tweakAfterClicker": "アフタークリッカー", "tweakeroo.config.feature_toggle.prettyName.tweakAimLock": "エイムロック", "tweakeroo.config.feature_toggle.prettyName.tweakAngelBlock": "エンジェルブロック", "tweakeroo.config.feature_toggle.prettyName.tweakAreaSelector": "エリアセレクター", "tweakeroo.config.feature_toggle.prettyName.tweakAutoSwitchElytra": "エリトラ自動切替", "tweakeroo.config.feature_toggle.prettyName.tweakBlockReachOverride": "ブロックリーチ上書き", "tweakeroo.config.feature_toggle.prettyName.tweakBlockTypeBreakRestriction": "ブロックタイプ破壊制限", "tweakeroo.config.feature_toggle.prettyName.tweakBreakingGrid": "破壊グリッド", "tweakeroo.config.feature_toggle.prettyName.tweakBreakingRestriction": "破壊制限", "tweakeroo.config.feature_toggle.prettyName.tweakBundleDisplay": "バンドル表示", "tweakeroo.config.feature_toggle.prettyName.tweakChatBackgroundColor": "チャット背景色", "tweakeroo.config.feature_toggle.prettyName.tweakChatPersistentText": "チャット永続テキスト", "tweakeroo.config.feature_toggle.prettyName.tweakChatTimestamp": "チャットタイムスタンプ", "tweakeroo.config.feature_toggle.prettyName.tweakCommandBlockExtraFields": "コマンドブロック追加フィールド", "tweakeroo.config.feature_toggle.prettyName.tweakCreativeExtraItems": "クリエイティブ追加アイテム", "tweakeroo.config.feature_toggle.prettyName.tweakCustomFlatPresets": "カスタムフラットプリセット", "tweakeroo.config.feature_toggle.prettyName.tweakCustomFlyDeceleration": "カスタム飛行減速", "tweakeroo.config.feature_toggle.prettyName.tweakCustomInventoryScreenScale": "カスタムインベントリ画面スケール", "tweakeroo.config.feature_toggle.prettyName.tweakDarknessVisibility": "暗闇視認性", "tweakeroo.config.feature_toggle.prettyName.tweakElytraCamera": "エリトラカメラ", "tweakeroo.config.feature_toggle.prettyName.tweakEmptyShulkerBoxesStack": "空シュルカーボックススタック", "tweakeroo.config.feature_toggle.prettyName.tweakEntityReachOverride": "エンティティリーチ上書き", "tweakeroo.config.feature_toggle.prettyName.tweakEntityTypeAttackRestriction": "エンティティタイプ攻撃制限", "tweakeroo.config.feature_toggle.prettyName.tweakExplosionReducedParticles": "爆発パーティクル削減", "tweakeroo.config.feature_toggle.prettyName.tweakF3Cursor": "F3カーソル", "tweakeroo.config.feature_toggle.prettyName.tweakFakeSneakPlacement": "偽スニーク配置", "tweakeroo.config.feature_toggle.prettyName.tweakFakeSneaking": "偽スニーク", "tweakeroo.config.feature_toggle.prettyName.tweakFastBlockPlacement": "高速ブロック配置", "tweakeroo.config.feature_toggle.prettyName.tweakFastLeftClick": "高速左クリック", "tweakeroo.config.feature_toggle.prettyName.tweakFastRightClick": "高速右クリック", "tweakeroo.config.feature_toggle.prettyName.tweakFillCloneLimit": "Fill Clone制限", "tweakeroo.config.feature_toggle.prettyName.tweakFlexibleBlockPlacement": "フレキシブルブロック配置", "tweakeroo.config.feature_toggle.prettyName.tweakFlySpeed": "飛行速度", "tweakeroo.config.feature_toggle.prettyName.tweakFreeCamera": "フリーカメラ", "tweakeroo.config.feature_toggle.prettyName.tweakGammaOverride": "ガンマ上書き", "tweakeroo.config.feature_toggle.prettyName.tweakHandRestock": "ハンド補充", "tweakeroo.config.feature_toggle.prettyName.tweakHangableEntityBypass": "掛けられるエンティティバイパス", "tweakeroo.config.feature_toggle.prettyName.tweakHoldAttack": "攻撃ホールド", "tweakeroo.config.feature_toggle.prettyName.tweakHoldUse": "使用ホールド", "tweakeroo.config.feature_toggle.prettyName.tweakHotbarScroll": "ホットバースクロール", "tweakeroo.config.feature_toggle.prettyName.tweakHotbarSlotCycle": "ホットバースロット循環", "tweakeroo.config.feature_toggle.prettyName.tweakHotbarSlotRandomizer": "ホットバースロットランダム化", "tweakeroo.config.feature_toggle.prettyName.tweakHotbarSwap": "ホットバー交換", "tweakeroo.config.feature_toggle.prettyName.tweakInventoryPreview": "インベントリプレビュー", "tweakeroo.config.feature_toggle.prettyName.tweakItemUnstackingProtection": "アイテムアンスタック保護", "tweakeroo.config.feature_toggle.prettyName.tweakLavaVisibility": "溶岩視認性", "tweakeroo.config.feature_toggle.prettyName.tweakMapPreview": "地図プレビュー", "tweakeroo.config.feature_toggle.prettyName.tweakMovementKeysLast": "移動キー最後", "tweakeroo.config.feature_toggle.prettyName.tweakPeriodicAttack": "定期攻撃", "tweakeroo.config.feature_toggle.prettyName.tweakPeriodicHoldAttack": "定期攻撃ホールド", "tweakeroo.config.feature_toggle.prettyName.tweakPeriodicHoldUse": "定期使用ホールド", "tweakeroo.config.feature_toggle.prettyName.tweakPeriodicUse": "定期使用", "tweakeroo.config.feature_toggle.prettyName.tweakPermanentSneak": "永続スニーク", "tweakeroo.config.feature_toggle.prettyName.tweakPermanentSprint": "永続スプリント", "tweakeroo.config.feature_toggle.prettyName.tweakPickBeforePlace": "配置前ピック", "tweakeroo.config.feature_toggle.prettyName.tweakPlacementGrid": "配置グリッド", "tweakeroo.config.feature_toggle.prettyName.tweakPlacementLimit": "配置制限", "tweakeroo.config.feature_toggle.prettyName.tweakPlacementRestriction": "配置制限", "tweakeroo.config.feature_toggle.prettyName.tweakPlacementRestrictionFirst": "配置制限最初", "tweakeroo.config.feature_toggle.prettyName.tweakPlacementRestrictionHand": "配置制限ハンド", "tweakeroo.config.feature_toggle.prettyName.tweakPlayerInventoryPeek": "プレイヤーインベントリピーク", "tweakeroo.config.feature_toggle.prettyName.tweakPlayerListAlwaysVisible": "プレイヤーリスト常時表示", "tweakeroo.config.feature_toggle.prettyName.tweakPotionWarning": "ポーション警告", "tweakeroo.config.feature_toggle.prettyName.tweakPrintDeathCoordinates": "死亡座標表示", "tweakeroo.config.feature_toggle.prettyName.tweakRenderEdgeChunks": "エッジチャンクレンダリング", "tweakeroo.config.feature_toggle.prettyName.tweakRenderInvisibleEntities": "透明エンティティレンダリング", "tweakeroo.config.feature_toggle.prettyName.tweakRenderLimitEntities": "エンティティレンダリング制限", "tweakeroo.config.feature_toggle.prettyName.tweakRepairMode": "修理モード", "tweakeroo.config.feature_toggle.prettyName.tweakSculkPulseLength": "スカルクパルス長", "tweakeroo.config.feature_toggle.prettyName.tweakSelectiveBlocksRenderOutline": "選択ブロックレンダリングアウトライン", "tweakeroo.config.feature_toggle.prettyName.tweakSelectiveBlocksRendering": "選択ブロックレンダリング", "tweakeroo.config.feature_toggle.prettyName.tweakServerDataSync": "サーバーデータ同期", "tweakeroo.config.feature_toggle.prettyName.tweakServerDataSyncBackup": "サーバーデータ同期バックアップ", "tweakeroo.config.feature_toggle.prettyName.tweakShulkerBoxDisplay": "シュルカーボックス表示", "tweakeroo.config.feature_toggle.prettyName.tweakSignCopy": "看板コピー", "tweakeroo.config.feature_toggle.prettyName.tweakSnapAim": "スナップエイム", "tweakeroo.config.feature_toggle.prettyName.tweakSnapAimLock": "スナップエイムロック", "tweakeroo.config.feature_toggle.prettyName.tweakSneak_1_15_2": "1.15.2スニーク", "tweakeroo.config.feature_toggle.prettyName.tweakSpectatorTeleport": "スペクテイターテレポート", "tweakeroo.config.feature_toggle.prettyName.tweakStructureBlockLimit": "ストラクチャーブロック制限", "tweakeroo.config.feature_toggle.prettyName.tweakSwapAlmostBrokenTools": "破損寸前ツール交換", "tweakeroo.config.feature_toggle.prettyName.tweakTabCompleteCoordinate": "タブ補完座標", "tweakeroo.config.feature_toggle.prettyName.tweakToolSwitch": "ツール切替", "tweakeroo.config.feature_toggle.prettyName.tweakWaterVisibility": "水中視認性", "tweakeroo.config.feature_toggle.prettyName.tweakWeaponSwitch": "武器切替", "tweakeroo.config.feature_toggle.prettyName.tweakYMirror": "Yミラー", "tweakeroo.config.feature_toggle.prettyName.tweakZoom": "カメラズーム", "tweakeroo.config.fixes.comment.elytraFix": "EarthcomputerとN<PERSON>ieによるエリトラ着地修正。\n展開修正は現在バニラにあるため、これは着地にのみ影響します。", "tweakeroo.config.fixes.comment.elytraSprintCancel": "エリトラ使用中にスプリントから外れるバニラのバグ（MC-279688）を修正します。\nこの設定は1.21.4でのみ必要で、1.21.5では修正されています。", "tweakeroo.config.fixes.comment.macHorizontalScroll": "Mac/OSXを使用している場合、hscrollmodと同じ修正/変更を適用しますが、\nmalilib系modのすべてのスクロール処理を壊すことはありません。", "tweakeroo.config.fixes.comment.ravagerClientBlockBreakFix": "ラヴェジャーがクライアント側でブロックを破壊することを修正し、\n迷惑なゴーストブロック/ブロック同期ずれを防ぎます", "tweakeroo.config.fixes.name.elytraFix": "エリトラ修正", "tweakeroo.config.fixes.name.elytraSprintCancel": "エリトラスプリントキャンセル", "tweakeroo.config.fixes.name.macHorizontalScroll": "Mac水平スクロール", "tweakeroo.config.fixes.name.ravagerClientBlockBreakFix": "ラヴェジャークライアントブロック破壊修正", "tweakeroo.config.generic.comment.accuratePlacementProtocol": "有効にすると、フレキシブルブロック配置と\n正確ブロック配置がCarpet modで実装されたプロトコルを使用します。\n§6注意：ホッパーや原木など、クリックしたブロック面のみを\n§6考慮するブロック以外のブロック回転が動作するために必要です。", "tweakeroo.config.generic.comment.accuratePlacementProtocolMode": "使用する「正確配置プロトコル」のタイプ。\n- Auto：シングルプレイヤーではv3を使用し、マルチプレイヤーでは\n  デフォルトでSlabs-onlyを使用しますが、サーバーに'carpet:hello'\n  パケットを送信するCarpet modがある場合はv2を使用します。\n- Version 3：Tweakeroo自体（シングルプレイヤー）またはServuxでサポート。\n- Version 2：Carpet modがあるサーバーと互換性があります\n  （skyrisingとDeadlyMCのQuickCarpet、\n  またはFabricCarpetに加えてCarpetExtra。\n  どちらの場合も'accurateBlockPlacement' Carpetルールを\n  サーバーで有効にする必要があります）。\n- Slabs only：上ハーフブロックのみ修正。Paperサーバーと互換性があります。\n- None：座標を変更しません。", "tweakeroo.config.generic.comment.afterClickerClickCount": "tweakAfterClickerが有効な時に\n配置されたブロックごとに行う右クリック数", "tweakeroo.config.generic.comment.angelBlockPlacementDistance": "tweakAngelBlockが有効な時に\nプレイヤーから空中にブロックを配置できる距離。\n5がサーバーが許可する最大値です。", "tweakeroo.config.generic.comment.areaSelectionUseAll": "選択に空気を含めるかどうか\nTweakForkのAndrew54757によって作成", "tweakeroo.config.generic.comment.blockReachDistance": "上書き調整が有効な場合に使用するブロックリーチ距離。\nゲームが許可する最大値は64です。\n§6サーバーで定義されたルールより\n§6[0.5 - 1.0]以上高い値で使用しないでください。", "tweakeroo.config.generic.comment.blockTypeBreakRestrictionWarn": "ブロックタイプ破壊制限機能がブロックの破壊を防ぐ時に\n表示する警告メッセージのタイプを選択します（ある場合）", "tweakeroo.config.generic.comment.breakingGridSize": "グリッド破壊モードのグリッド間隔サイズ。\n値を素早く調整するには、調整トグルキーバインドを\n押しながらスクロールしてください。", "tweakeroo.config.generic.comment.breakingRestrictionMode": "使用する破壊制限モード（ホットキー選択可能）", "tweakeroo.config.generic.comment.bundleDisplayBgColor": "バンドル表示の背景テクスチャを\nバンドルの染料色で着色/色付けを有効にします", "tweakeroo.config.generic.comment.bundleDisplayRequireShift": "バンドルプレビューにシフトを押すことが必要かどうか\n§6注意：これを無効にするとバニラバンドルツールチップが完全に無効になります。", "tweakeroo.config.generic.comment.bundleDisplayRowWidth": "バンドルプレビュー表示の行幅サイズを調整します。\nより小さいまたは大きい値はテクスチャ表示の問題を引き起こします。", "tweakeroo.config.generic.comment.chatBackgroundColor": "'tweakChatBackgroundColor'が有効な場合の\nチャットメッセージの背景色", "tweakeroo.config.generic.comment.chatTimeFormat": "tweakChatTimestampが有効な場合のチャットメッセージの時刻形式\nJava SimpleDateFormat形式指定子を使用します。", "tweakeroo.config.generic.comment.clientPlacementRotation": "シングルプレイヤーとクライアント側の配置回転を有効にします。\nCarpet modなしでシングルプレイヤーで正確配置が動作するなど", "tweakeroo.config.generic.comment.customInventoryGuiScale": "§etweakCustomInventoryScreenScale§rが有効な場合に\nインベントリ画面で使用するGUIスケール値。", "tweakeroo.config.generic.comment.debugLogging": "特定の問題やクラッシュをデバッグするために\nゲームコンソールでいくつかのデバッグログメッセージを有効にします。", "tweakeroo.config.generic.comment.darknessScaleOverrideValue": "'tweakDarknessVisibility'で使用される上書き値で、\n「暗化」画面効果を自動的に弱めます。\nこれは各効果パルスで画面が暗くなる量の\nパーセンテージ値です。", "tweakeroo.config.generic.comment.elytraCameraIndicator": "エリトラカメラモードがアクティブな時に\n実際のピッチ角インジケーターをレンダリングするかどうか", "tweakeroo.config.generic.comment.entityReachDistance": "上書き調整が有効な場合に使用するエンティティリーチ距離。\nゲームが許可する最大値は64です。\n§6サーバーで定義されたルールより\n§6[0.5 - 1.0]以上高い値で使用しないでください。", "tweakeroo.config.generic.comment.entityTypeAttackRestrictionWarn": "エンティティタイプ攻撃制限機能がエンティティの攻撃を防ぐ時に\n表示する警告メッセージのタイプを選択します（ある場合）", "tweakeroo.config.generic.comment.fastBlockPlacementCount": "高速ブロック配置調整で\nゲームティックごとに配置するブロックの最大数", "tweakeroo.config.generic.comment.fastLeftClickAllowTools": "高速左クリックがサバイバルでも\nツールアイテムを持っている間に動作することを許可します", "tweakeroo.config.generic.comment.fastLeftClickCount": "tweakFastLeftClickが有効で攻撃ボタンが押されている時に\nゲームティックごとに行う左クリック数", "tweakeroo.config.generic.comment.fastPlacementRememberOrientation": "有効にすると、高速ブロック配置機能は\n配置した最初のブロックの向きを常に記憶します。\nこれがないと、フレキシブルブロック配置が有効で\nアクティブな場合にのみ向きが記憶されます。", "tweakeroo.config.generic.comment.fastRightClickCount": "tweakFastRightClickが有効で使用ボタンが押されている時に\nゲームティックごとに行う右クリック数", "tweakeroo.config.generic.comment.fillCloneLimit": "それらを上書きする調整が有効な場合の\nシングルプレイヤーでの新しい/fillと/cloneブロック制限", "tweakeroo.config.generic.comment.flexibleBlockPlacementOverlayColor": "ブロック配置オーバーレイで現在指している\n領域の色", "tweakeroo.config.generic.comment.flyDecelerationFactor": "'customFlyDeceleration'調整が有効な場合に\nプレイヤーがどれだけ速く停止するかを調整します", "tweakeroo.config.generic.comment.flySpeedIncrement1": "増分1での飛行速度の変化量", "tweakeroo.config.generic.comment.flySpeedIncrement2": "増分2での飛行速度の変化量", "tweakeroo.config.generic.comment.flySpeedPreset1": "プリセット1の飛行速度", "tweakeroo.config.generic.comment.flySpeedPreset2": "プリセット2の飛行速度", "tweakeroo.config.generic.comment.flySpeedPreset3": "プリセット3の飛行速度", "tweakeroo.config.generic.comment.flySpeedPreset4": "プリセット4の飛行速度", "tweakeroo.config.generic.comment.freeCameraPlayerInputs": "有効にすると、フリーカメラモードでの攻撃と使用アクション\n（つまり左右クリック）が実際のプレイヤーに通されます。", "tweakeroo.config.generic.comment.freeCameraPlayerMovement": "有効にすると、フリーカメラモードでの移動入力が\nカメラの代わりに実際のクライアントプレイヤーを移動させます", "tweakeroo.config.generic.comment.gammaOverrideValue": "上書きオプションが有効な時に使用するガンマ値", "tweakeroo.config.generic.comment.handRestockPre": "有効にすると、ハンド補充が\nスタックがなくなる前に発生します", "tweakeroo.config.generic.comment.handRestockPreThreshold": "事前補充モードでハンド補充が発生する\nスタックサイズの閾値", "tweakeroo.config.generic.comment.hangableEntityBypassInverse": "hangableEntityTargetingBypass調整が有効な場合、\nこれは掛けられるエンティティ（アイテムフレームまたは絵画）を\nターゲットできるようにするために、プレイヤーがスニークする必要があるか\nスニークしない必要があるかを制御します。\n > true - スニーク = エンティティを無視/バイパス\n > false - スニーク = エンティティをターゲット", "tweakeroo.config.generic.comment.hotbarSlotCycleMax": "ホットバースロット循環調整が有効な場合に\n使用/循環する最後のホットバースロットです。\n基本的に、ここで設定された最大スロット番号を超えると\n循環は最初のスロットに戻ります。", "tweakeroo.config.generic.comment.hotbarSlotRandomizerMax": "ホットバースロットランダム化調整が有効な場合に\n使用する最後のホットバースロットです。基本的に、\nアイテム使用後に選択されたホットバースロットが\n1からこの最大スロットまでランダムに選ばれます。", "tweakeroo.config.generic.comment.hotbarSwapOverlayAlignment": "ホットバー交換オーバーレイの位置", "tweakeroo.config.generic.comment.hotbarSwapOverlayOffsetX": "ホットバー交換オーバーレイの水平オフセット", "tweakeroo.config.generic.comment.hotbarSwapOverlayOffsetY": "ホットバー交換オーバーレイの垂直オフセット", "tweakeroo.config.generic.comment.inventoryPreviewVillagerBGColor": "職業に基づく村人取引の背景色表示を\n有効/無効にします。", "tweakeroo.config.generic.comment.itemSwapDurabilityThreshold": "これは低耐久性アイテム交換機能の\n耐久性閾値（残り使用回数）です。\n総耐久性が低いアイテムはより低くなり、\n5%%残りで交換されることに注意してください。", "tweakeroo.config.generic.comment.itemUsePacketCheckBypass": "1.18.2で追加された新しい距離/座標チェックをバイパスします。\n\nそのチェックは「正確配置プロトコル」を破り、\n回転（または他のプロパティ）リクエストで配置されたブロックを\nゴーストブロックにしてしまいます。\n\n基本的にこれを無効にする必要はありません。\nそのチェックは1.18.2以前には存在しませんでした。", "tweakeroo.config.generic.comment.mapPreviewRequireShift": "地図プレビューにシフトを押すことが必要かどうか", "tweakeroo.config.generic.comment.mapPreviewSize": "レンダリングされる地図プレビューのサイズ", "tweakeroo.config.generic.comment.periodicAttackInterval": "自動攻撃（左クリック）間のゲームティック数", "tweakeroo.config.generic.comment.periodicAttackResetIntervalOnActivate": "マウススクロールホイールで調整された場合、\n無効化時に定期攻撃間隔をリセットします", "tweakeroo.config.generic.comment.periodicHoldAttackDuration": "攻撃を押し続けるゲームティック数", "tweakeroo.config.generic.comment.periodicHoldAttackInterval": "攻撃を押し始める（左クリック）間のゲームティック数", "tweakeroo.config.generic.comment.periodicHoldAttackResetIntervalOnActivate": "マウススクロールホイールで調整された場合、\n無効化時に定期ホールド攻撃間隔をリセットします", "tweakeroo.config.generic.comment.periodicHoldUseDuration": "使用を押し続けるゲームティック数", "tweakeroo.config.generic.comment.periodicHoldUseInterval": "使用を押し始める（右クリック）間のゲームティック数", "tweakeroo.config.generic.comment.periodicHoldUseResetIntervalOnActivate": "マウススクロールホイールで調整された場合、\n無効化時に定期ホールド使用間隔をリセットします", "tweakeroo.config.generic.comment.periodicUseInterval": "自動使用（右クリック）間のゲームティック数", "tweakeroo.config.generic.comment.periodicUseResetIntervalOnActivate": "マウススクロールホイールで調整された場合、\n無効化時に定期使用間隔をリセットします", "tweakeroo.config.generic.comment.permanentSneakAllowInGUIs": "trueの場合、永続スニーク調整は\nGUIが開いている間も動作します", "tweakeroo.config.generic.comment.placementGridSize": "グリッド配置モードのグリッド間隔サイズ。\n値を素早く調整するには、調整トグルキーバインドを\n押しながらスクロールしてください。", "tweakeroo.config.generic.comment.placementLimit": "tweakPlacementLimitが有効な場合に\n右クリックあたり最大で配置できるブロック数。\n値を素早く調整するには、調整トグルキーバインドを\n押しながらスクロールしてください。", "tweakeroo.config.generic.comment.placementRestrictionMode": "使用する配置制限モード（ホットキー選択可能）", "tweakeroo.config.generic.comment.placementRestrictionTiedToFast": "有効にすると、高速配置モードを切り替える時に\n配置制限モードがオン/オフ状態を切り替えます。", "tweakeroo.config.generic.comment.potionWarningBeneficialOnly": "「有益」とマークされたポーション効果がなくなることについてのみ警告します", "tweakeroo.config.generic.comment.potionWarningThreshold": "警告が表示され始めるポーション効果の\n残り持続時間（ティック単位）", "tweakeroo.config.generic.comment.rememberFlexibleFromClick": "有効にすると、使用キー（右クリック）が押されている限り、\nフレキシブルブロック配置ステータスが\n最初に配置されたブロックから記憶されます。\n基本的に、同じ向きですべてのブロックを高速配置するために\nすべてのフレキシブルアクティベーションキーを\n押し続ける必要がありません。", "tweakeroo.config.generic.comment.renderLimitItem": "フレームごとにレンダリングされるアイテムエンティティの最大数。\n通常の動作、つまりこの制限を無効にするには-1を使用してください。", "tweakeroo.config.generic.comment.renderLimitXPOrb": "フレームごとにレンダリングされるXPオーブエンティティの最大数。\n通常の動作、つまりこの制限を無効にするには-1を使用してください。", "tweakeroo.config.generic.comment.sculkSensorPulseLength": "'tweakSculkPulseLength'調整が有効な場合のスカルクセンサーのパルス長。", "tweakeroo.config.generic.comment.selectiveBlocksHideEntities": "選択ブロックレンダリングでエンティティを隠すかどうか\nTweakForkのAndrew54757によって作成", "tweakeroo.config.generic.comment.selectiveBlocksHideParticles": "選択ブロックレンダリングでパーティクルを隠すかどうか\nTweakForkのAndrew54757によって作成", "tweakeroo.config.generic.comment.selectiveBlocksNoHit": "Whether or not to disable targeting hidden blocks\nWritten by Andrew54757 under TweakFork", "tweakeroo.config.generic.comment.selectiveBlocksTrackPistons": "Whether or not to track piston movements for selective block rendering\nWritten by Andrew54757 under TweakFork", "tweakeroo.config.generic.comment.serverDataSyncCacheRefresh": "The Cache refresh value as a fraction of a second.\nThis value is the trigger for whenever requesting entity data\ngets refreshed from the server, even if it still exists in the Cache.\nThis should be set around 25% or less of\nthe \"entityDataSyncCacheTimeout\" value.\nA value of '0.05f' means 50 ms or once every game tick.", "tweakeroo.config.generic.comment.serverDataSyncCacheTimeout": "The Cache timeout value in seconds that\nthe Entity Cache keeps records for.\nA lower value means more frequent updates.", "tweakeroo.config.generic.comment.serverNbtRequestRate": "Limit request rate for server entity data syncer", "tweakeroo.config.generic.comment.shulkerDisplayBgColor": "Enables tinting/coloring the Shulker Box display\nbackground texture with the dye color of the box", "tweakeroo.config.generic.comment.shulkerDisplayEnderChest": "Enables Ender Chest display similar to the Shulker Box Display.", "tweakeroo.config.generic.comment.shulkerDisplayRequireShift": "Whether or not holding shift is required for the Shulker Box preview", "tweakeroo.config.generic.comment.slotSyncWorkaround": "This prevents the server from overriding the durability or\nstack size on items that are being used quickly for example\nwith the fast right click tweak.", "tweakeroo.config.generic.comment.slotSyncWorkaroundAlways": "Enables the slot sync workaround at all times when the use key\nis held, not only when using fast right click or fast block placement.\nThis is mainly for other mods that may quickly use items when\nholding down use, such as Litematica's Easy Place mode.", "tweakeroo.config.generic.comment.snapAimIndicator": "Whether or not to render the snap aim angle indicator", "tweakeroo.config.generic.comment.snapAimIndicatorColor": "The color for the snap aim indicator background", "tweakeroo.config.generic.comment.snapAimMode": "Snap aim mode: yaw, or pitch, or both", "tweakeroo.config.generic.comment.snapAimOnlyCloseToAngle": "If enabled, then the snap aim only snaps to the angle\nwhen the internal angle is within a certain distance of it.\nThe threshold can be set in snapAimThreshold", "tweakeroo.config.generic.comment.snapAimPitchOvershoot": "Whether or not to allow overshooting the pitch angle\nfrom the normal +/- 90 degrees up to +/- 180 degrees", "tweakeroo.config.generic.comment.snapAimPitchStep": "The pitch angle step of the snap aim tweak", "tweakeroo.config.generic.comment.snapAimThresholdPitch": "The angle threshold inside which the player rotation will\nbe snapped to the snap angle.", "tweakeroo.config.generic.comment.snapAimThresholdYaw": "The angle threshold inside which the player rotation will\nbe snapped to the snap angle.", "tweakeroo.config.generic.comment.snapAimYawStep": "The yaw angle step of the snap aim tweak", "tweakeroo.config.generic.comment.structureBlockMaxSize": "The maximum dimensions for a Structure Block's saved area", "tweakeroo.config.generic.comment.toolSwapBetterEnchants": "Consider a Tools' Enchantments and Rarity\nfor Tool swapping, only after comparing if\nit's the correct tool to use on the target.", "tweakeroo.config.generic.comment.toolSwapPreferSilkTouch": "Consider your final Silk Touch preference\nwith tools whenever all other results are\nequal (Block Breaking Speed, Enchantments, Materials, etc).\nThis is useful for determining the default\npickaxe to choose when SilkTouchFirst is not\napplicable for the block that you are mining.\n§6NOTE: When this is disabled, the mod will always prefer\n§6to use the non-silk touch tool during the\n§6comparison if one is found and matches\n§6all of the criteria.", "tweakeroo.config.generic.comment.toolSwapBambooUsesSwordFirst": "Check if the targeted block is Bamboo,\nand if so; prefer using a Sword instead\nof an Axe to mine it.", "tweakeroo.config.generic.comment.toolSwapNeedsShearsFirst": "Check if the block requires Shears first,\nsuch as wool, vines or cobwebs.\nThe blocks chosen are based on the new\n'§b#malilib:needs_shears§r' Block Tag\nsince this doesn't exist in Vanilla.\n§6NOTE: This feature is only activate when\n§6the block is not properly matched in Vanilla.", "tweakeroo.config.generic.comment.toolSwapSilkTouchFirst": "Check if the block requires Silk Touch first,\nsuch as glass or an ender chest.\nThe blocks chosen is based on the new\n'§b#malilib:needs_silk_touch§r' Block Tag\nsince this doesn't exist in Vanilla.", "tweakeroo.config.generic.comment.toolSwapSilkTouchOres": "Check if the block is an Ore Block via the\n'§b#malilib:ore_blocks§r' tag, and then use\nSilk Touch First on it.\n§6NOTE: Disable this to use fortune.§r", "tweakeroo.config.generic.comment.toolSwapSilkTouchOverride": "Check the Silk Touch Override list for\nblocks to apply SilkTouchFirst to.\n§6NOTE: This can work without 'toolSwapSilkTouchFirst'\n§6being enabled, but it is designed to work\n§6the same, and can co-exist with it on.", "tweakeroo.config.generic.comment.toolSwitchIgnoredSlots": "The slots where the Tool Switch tweak does not work when they are active.", "tweakeroo.config.generic.comment.toolSwitchableSlots": "The slots that the Tool Switch tweak is allowed to put tools to.\nNote that Tool Switch can also switch to other slots in the hotbar,\nif they already have the preferred tool, but it will only\nswap new tools to these slots", "tweakeroo.config.generic.comment.weaponSwapBetterEnchants": "Consider a Weapons' Enchantments and Rarity\nfor Weapon swapping, only after comparing if \nit's the correct Weapon to use on the target.", "tweakeroo.config.generic.comment.zoomAdjustMouseSensitivity": "If enabled, then the mouse sensitivity is reduced\nwhile the zoom feature is enabled and the zoom key is active", "tweakeroo.config.generic.comment.zoomFov": "The FOV value used for the zoom feature", "tweakeroo.config.generic.comment.zoomResetFovOnActivate": "Reset the Camera Zoom FOV upon Deactivation,\nif the Mouse Scroll Wheel was used to adjust it", "tweakeroo.config.generic.name.accuratePlacementProtocol": "accuratePlacementProtocol", "tweakeroo.config.generic.name.accuratePlacementProtocolMode": "accuratePlacementProtocolMode", "tweakeroo.config.generic.name.afterClickerClickCount": "afterClickerClickCount", "tweakeroo.config.generic.name.angelBlockPlacementDistance": "angelBlockPlacementDistance", "tweakeroo.config.generic.name.areaSelectionUseAll": "areaSelectionUseAll", "tweakeroo.config.generic.name.blockReachDistance": "blockReachDistance", "tweakeroo.config.generic.name.blockTypeBreakRestrictionWarn": "blockTypeBreakRestrictionWarn", "tweakeroo.config.generic.name.breakingGridSize": "breakingGridSize", "tweakeroo.config.generic.name.breakingRestrictionMode": "breakingRestrictionMode", "tweakeroo.config.generic.name.bundleDisplayBgColor": "bundleDisplayBgColor", "tweakeroo.config.generic.name.bundleDisplayRequireShift": "bundleDisplayRequireShift", "tweakeroo.config.generic.name.bundleDisplayRowWidth": "bundleDisplayRowWidth", "tweakeroo.config.generic.name.chatBackgroundColor": "chatBackgroundColor", "tweakeroo.config.generic.name.chatTimeFormat": "chatTimeFormat", "tweakeroo.config.generic.name.clientPlacementRotation": "clientPlacementRotation", "tweakeroo.config.generic.name.customInventoryGuiScale": "customInventoryGuiScale", "tweakeroo.config.generic.name.debugLogging": "debugLogging", "tweakeroo.config.generic.name.darknessScaleOverrideValue": "darknessScaleOverrideValue", "tweakeroo.config.generic.name.elytraCameraIndicator": "elytraCameraIndicator", "tweakeroo.config.generic.name.entityReachDistance": "entityReachDistance", "tweakeroo.config.generic.name.entityTypeAttackRestrictionWarn": "entityTypeAttackRestrictionWarn", "tweakeroo.config.generic.name.fastBlockPlacementCount": "fastBlockPlacementCount", "tweakeroo.config.generic.name.fastLeftClickAllowTools": "fastLeftClickAllowTools", "tweakeroo.config.generic.name.fastLeftClickCount": "fastLeftClickCount", "tweakeroo.config.generic.name.fastPlacementRememberOrientation": "fastPlacementRememberOrientation", "tweakeroo.config.generic.name.fastRightClickCount": "fastRightClickCount", "tweakeroo.config.generic.name.fillCloneLimit": "fillCloneLimit", "tweakeroo.config.generic.name.flexibleBlockPlacementOverlayColor": "flexibleBlockPlacementOverlayColor", "tweakeroo.config.generic.name.flyDecelerationFactor": "flyDecelerationFactor", "tweakeroo.config.generic.name.flySpeedIncrement1": "flySpeedIncrement1", "tweakeroo.config.generic.name.flySpeedIncrement2": "flySpeedIncrement2", "tweakeroo.config.generic.name.flySpeedPreset1": "flySpeedPreset1", "tweakeroo.config.generic.name.flySpeedPreset2": "flySpeedPreset2", "tweakeroo.config.generic.name.flySpeedPreset3": "flySpeedPreset3", "tweakeroo.config.generic.name.flySpeedPreset4": "flySpeedPreset4", "tweakeroo.config.generic.name.freeCameraPlayerInputs": "freeCameraPlayerInputs", "tweakeroo.config.generic.name.freeCameraPlayerMovement": "freeCameraPlayerMovement", "tweakeroo.config.generic.name.gammaOverrideValue": "gammaOverrideValue", "tweakeroo.config.generic.name.handRestockPre": "handRestockPre", "tweakeroo.config.generic.name.handRestockPreThreshold": "handRestockPreThreshold", "tweakeroo.config.generic.name.hangableEntityBypassInverse": "hangableEntityBypassInverse", "tweakeroo.config.generic.name.hotbarSlotCycleMax": "hotbarSlotCycleMax", "tweakeroo.config.generic.name.hotbarSlotRandomizerMax": "hotbarSlotRandomizerMax", "tweakeroo.config.generic.name.hotbarSwapOverlayAlignment": "hotbarSwapOverlayAlignment", "tweakeroo.config.generic.name.hotbarSwapOverlayOffsetX": "hotbarSwapOverlayOffsetX", "tweakeroo.config.generic.name.hotbarSwapOverlayOffsetY": "hotbarSwapOverlayOffsetY", "tweakeroo.config.generic.name.inventoryPreviewVillagerBGColor": "inventoryPreviewVillagerBGColor", "tweakeroo.config.generic.name.itemSwapDurabilityThreshold": "itemSwapDurabilityThreshold", "tweakeroo.config.generic.name.itemUsePacketCheckBypass": "itemUsePacketCheckBypass", "tweakeroo.config.generic.name.mapPreviewRequireShift": "mapPreviewRequireShift", "tweakeroo.config.generic.name.mapPreviewSize": "mapPreviewSize", "tweakeroo.config.generic.name.periodicAttackInterval": "periodicAttackInterval", "tweakeroo.config.generic.name.periodicAttackResetIntervalOnActivate": "periodicAttackResetIntervalOnActivate", "tweakeroo.config.generic.name.periodicHoldAttackDuration": "periodicHoldAttackDuration", "tweakeroo.config.generic.name.periodicHoldAttackInterval": "periodicHoldAttackInterval", "tweakeroo.config.generic.name.periodicHoldAttackResetIntervalOnActivate": "periodicHoldAttackResetIntervalOnActivate", "tweakeroo.config.generic.name.periodicHoldUseDuration": "periodicHoldUseDuration", "tweakeroo.config.generic.name.periodicHoldUseInterval": "periodicHoldUseInterval", "tweakeroo.config.generic.name.periodicHoldUseResetIntervalOnActivate": "periodicHoldUseResetIntervalOnActivate", "tweakeroo.config.generic.name.periodicUseInterval": "periodicUseInterval", "tweakeroo.config.generic.name.periodicUseResetIntervalOnActivate": "periodicUseResetIntervalOnActivate", "tweakeroo.config.generic.name.permanentSneakAllowInGUIs": "permanentSneakAllowInGUIs", "tweakeroo.config.generic.name.placementGridSize": "placementGridSize", "tweakeroo.config.generic.name.placementLimit": "placementLimit", "tweakeroo.config.generic.name.placementRestrictionMode": "placementRestrictionMode", "tweakeroo.config.generic.name.placementRestrictionTiedToFast": "placementRestrictionTiedToFast", "tweakeroo.config.generic.name.potionWarningBeneficialOnly": "potionWarningBeneficialOnly", "tweakeroo.config.generic.name.potionWarningThreshold": "potionWarningThreshold", "tweakeroo.config.generic.name.rememberFlexibleFromClick": "rememberFlexibleFromClick", "tweakeroo.config.generic.name.renderLimitItem": "renderLimitItem", "tweakeroo.config.generic.name.renderLimitXPOrb": "renderLimitXPOrb", "tweakeroo.config.generic.name.sculkSensorPulseLength": "sculkSensorPulseLength", "tweakeroo.config.generic.name.selectiveBlocksHideEntities": "selectiveBlocksHideEntities", "tweakeroo.config.generic.name.selectiveBlocksHideParticles": "selectiveBlocksHideParticles", "tweakeroo.config.generic.name.selectiveBlocksNoHit": "selectiveBlocksNoHit", "tweakeroo.config.generic.name.selectiveBlocksTrackPistons": "selectiveBlocksTrackPistons", "tweakeroo.config.generic.name.serverDataSyncCacheRefresh": "serverDataSyncCacheRefresh", "tweakeroo.config.generic.name.serverDataSyncCacheTimeout": "serverDataSyncCacheTimeout", "tweakeroo.config.generic.name.serverNbtRequestRate": "serverNbtRequestRate", "tweakeroo.config.generic.name.shulkerDisplayBgColor": "shulkerDisplayBgColor", "tweakeroo.config.generic.name.shulkerDisplayEnderChest": "shulkerDisplayEnderChest", "tweakeroo.config.generic.name.shulkerDisplayRequireShift": "shulkerDisplayRequireShift", "tweakeroo.config.generic.name.slotSyncWorkaround": "slotSyncWorkaround", "tweakeroo.config.generic.name.slotSyncWorkaroundAlways": "slotSyncWorkaroundAlways", "tweakeroo.config.generic.name.snapAimIndicator": "snapAimIndicator", "tweakeroo.config.generic.name.snapAimIndicatorColor": "snapAimIndicatorColor", "tweakeroo.config.generic.name.snapAimMode": "snapAimMode", "tweakeroo.config.generic.name.snapAimOnlyCloseToAngle": "snapAimOnlyCloseToAngle", "tweakeroo.config.generic.name.snapAimPitchOvershoot": "snapAimPitchOvershoot", "tweakeroo.config.generic.name.snapAimPitchStep": "snapAimPitchStep", "tweakeroo.config.generic.name.snapAimThresholdPitch": "snapAimT<PERSON><PERSON><PERSON>", "tweakeroo.config.generic.name.snapAimThresholdYaw": "snapAimThresholdYaw", "tweakeroo.config.generic.name.snapAimYawStep": "snapAimYawStep", "tweakeroo.config.generic.name.structureBlockMaxSize": "structureBlockMaxSize", "tweakeroo.config.generic.name.toolSwapBetterEnchants": "toolSwapBetterEnchants", "tweakeroo.config.generic.name.toolSwapPreferSilkTouch": "toolSwapPreferSilkTouch", "tweakeroo.config.generic.name.toolSwapBambooUsesSwordFirst": "toolSwapBambooUsesSwordFirst", "tweakeroo.config.generic.name.toolSwapNeedsShearsFirst": "toolSwapNeedsShearsFirst", "tweakeroo.config.generic.name.toolSwapSilkTouchFirst": "toolSwapSilkTouchFirst", "tweakeroo.config.generic.name.toolSwapSilkTouchOres": "toolSwapSilkTouchOres", "tweakeroo.config.generic.name.toolSwapSilkTouchOverride": "toolSwapSilkTouchOverride", "tweakeroo.config.generic.name.toolSwitchIgnoredSlots": "toolSwitchIgnoredSlots", "tweakeroo.config.generic.name.toolSwitchableSlots": "toolSwitchableSlots", "tweakeroo.config.generic.name.weaponSwapBetterEnchants": "weaponSwapBetterEnchants", "tweakeroo.config.generic.name.zoomAdjustMouseSensitivity": "zoomAdjustMouseSensitivity", "tweakeroo.config.generic.name.zoomFov": "zoomFov", "tweakeroo.config.generic.name.zoomResetFovOnActivate": "zoomResetFovOnActivate", "tweakeroo.config.generic.prettyName.freeCameraPlayerInputs": "Free Camera Player Inputs", "tweakeroo.config.generic.prettyName.freeCameraPlayerMovement": "Free Camera Player Movement", "tweakeroo.config.generic.prettyName.toolSwapBetterEnchants": "Tool Swap Better Enchants", "tweakeroo.config.generic.prettyName.toolSwapBambooUsesSwordFirst": "Tool Swap Bamboo Uses Sword First", "tweakeroo.config.generic.prettyName.toolSwapNeedsShearsFirst": "Tool Swap Needs Shears First", "tweakeroo.config.generic.prettyName.toolSwapSilkTouchFirst": "Tool Swap Silk Touch First", "tweakeroo.config.generic.prettyName.toolSwapPreferSilkTouch": "Tool Swap Prefer Silk Touch", "tweakeroo.config.generic.prettyName.toolSwapSilkTouchOres": "Tool Swap Silk Touch Ores", "tweakeroo.config.generic.prettyName.toolSwapSilkTouchOverride": "Tool Swap Silk Touch Override", "tweakeroo.config.generic.prettyName.weaponSwapBetterEnchants": "Weapon Swap Better Enchants", "tweakeroo.config.hotkey.comment.accurateBlockPlacementInto": "The key to activate the accurate block placement\nmode/overlay for placing the block facing\ninto the clicked block face", "tweakeroo.config.hotkey.comment.accurateBlockPlacementReverse": "The key to activate the accurate block placement\nmode/overlay for placing the block facing\nthe opposite way from what it would be otherwise", "tweakeroo.config.hotkey.comment.areaSelectionAddToList": "Add selected blocks to list\nWritten by Andrew54757 under TweakFork", "tweakeroo.config.hotkey.comment.areaSelectionOffset": "The key to offset selection pos\nWritten by Andrew54757 under TweakFork", "tweakeroo.config.hotkey.comment.areaSelectionRemoveFromList": "remove selected blocks from list\nWritten by Andrew54757 under TweakFork", "tweakeroo.config.hotkey.comment.breakingRestrictionModeColumn": "Switch the Breaking Restriction mode to the Column mode", "tweakeroo.config.hotkey.comment.breakingRestrictionModeDiagonal": "Switch the Breaking Restriction mode to the Diagonal mode", "tweakeroo.config.hotkey.comment.breakingRestrictionModeFace": "Switch the Breaking Restriction mode to the Face mode", "tweakeroo.config.hotkey.comment.breakingRestrictionModeLayer": "Switch the Breaking Restriction mode to the Layer mode", "tweakeroo.config.hotkey.comment.breakingRestrictionModeLine": "Switch the Breaking Restriction mode to the Line mode", "tweakeroo.config.hotkey.comment.breakingRestrictionModePlane": "Switch the Breaking Restriction mode to the Plane mode", "tweakeroo.config.hotkey.comment.copySignText": "Copies the text from an already-placed sign.\nThat text can be used with the tweakSignCopy tweak.", "tweakeroo.config.hotkey.comment.elytraCamera": "The key to lock the current real player rotations, only allowing the\ninputs (mouse) to affect separate \"camera rotations\" used only for the rendering\nwhile this key is active.\nMeant for freely looking down/around while elytra flying straight.", "tweakeroo.config.hotkey.comment.flexibleBlockPlacementAdjacent": "The key to activate the flexible block placement\nmode/overlay for placing the block in an adjacent position", "tweakeroo.config.hotkey.comment.flexibleBlockPlacementOffset": "The key to activate the flexible block placement\nmode/overlay for placing the block in a\noffset or diagonal position", "tweakeroo.config.hotkey.comment.flexibleBlockPlacementRotation": "The key to activate the flexible block placement\nmode/overlay for placing the block with\na rotation/facing", "tweakeroo.config.hotkey.comment.flyIncrement1": "Change fly speed by increment 1", "tweakeroo.config.hotkey.comment.flyIncrement2": "Change fly speed by increment 2", "tweakeroo.config.hotkey.comment.flyPreset1": "Switch to fly preset 1", "tweakeroo.config.hotkey.comment.flyPreset2": "Switch to fly preset 2", "tweakeroo.config.hotkey.comment.flyPreset3": "Switch to fly preset 3", "tweakeroo.config.hotkey.comment.flyPreset4": "Switch to fly preset 4", "tweakeroo.config.hotkey.comment.freeCameraPlayerInputs": "Toggle the Generic -> freeCameraPlayerInputs option", "tweakeroo.config.hotkey.comment.freeCameraPlayerMovement": "Toggle the Generic -> freeCameraPlayerMovement option", "tweakeroo.config.hotkey.comment.hotbarScroll": "The key to hold to allow scrolling the hotbar\nthrough the player inventory rows", "tweakeroo.config.hotkey.comment.hotbarSwap1": "Swap the hotbar with the top-most inventory row", "tweakeroo.config.hotkey.comment.hotbarSwap2": "Swap the hotbar with the middle inventory row", "tweakeroo.config.hotkey.comment.hotbarSwap3": "Swap the hotbar with the bottom-most inventory row", "tweakeroo.config.hotkey.comment.hotbarSwapBase": "The base key to show the hotbar/inventory overlay", "tweakeroo.config.hotkey.comment.inventoryPreview": "The key to activate the inventory preview feature", "tweakeroo.config.hotkey.comment.inventoryPreviewToggleScreen": "Open a screen for inventory preview\nYou can use your mouse to see tooltips", "tweakeroo.config.hotkey.comment.openConfigGui": "The key open the in-game config GUI", "tweakeroo.config.hotkey.comment.placementRestrictionModeColumn": "Switch the Placement Restriction mode to the Column mode", "tweakeroo.config.hotkey.comment.placementRestrictionModeDiagonal": "Switch the Placement Restriction mode to the Diagonal mode", "tweakeroo.config.hotkey.comment.placementRestrictionModeFace": "Switch the Placement Restriction mode to the Face mode", "tweakeroo.config.hotkey.comment.placementRestrictionModeLayer": "Switch the Placement Restriction mode to the Layer mode", "tweakeroo.config.hotkey.comment.placementRestrictionModeLine": "Switch the Placement Restriction mode to the Line mode", "tweakeroo.config.hotkey.comment.placementRestrictionModePlane": "Switch the Placement Restriction mode to the Plane mode", "tweakeroo.config.hotkey.comment.placementYMirror": "The key to mirror the targeted y-position within the block", "tweakeroo.config.hotkey.comment.playerInventoryPeek": "The key to activate the player inventory peek/preview feature", "tweakeroo.config.hotkey.comment.sitDownNearbyPets": "Makes all nearby pets sit down", "tweakeroo.config.hotkey.comment.skipAllRendering": "Toggles skipping _all_ rendering", "tweakeroo.config.hotkey.comment.skipWorldRendering": "Toggles skipping world rendering", "tweakeroo.config.hotkey.comment.standUpNearbyPets": "Makes all nearby pets stand up", "tweakeroo.config.hotkey.comment.swapElytraChestplate": "Swaps the currently equipped item in the chest slot between an Elytra and a Chest Plate", "tweakeroo.config.hotkey.comment.toggleAccuratePlacementProtocol": "Toggles the value of the Generic -> 'accuratePlacementProtocol' option", "tweakeroo.config.hotkey.comment.toggleGrabCursor": "Grabs or ungrabs the mouse cursor, depending on the current state", "tweakeroo.config.hotkey.comment.toolPick": "Switches to the effective tool for the targeted block", "tweakeroo.config.hotkey.comment.writeMapsAsImages": "Writes all the currently available maps as images\nto the 'config/tweakeroo/map_images/<worldname>/' directory", "tweakeroo.config.hotkey.comment.zoomActivate": "Zoom activation hotkey\nWritten by Andrew54757 under TweakFork", "tweakeroo.config.hotkey.name.accurateBlockPlacementInto": "accurateBlockPlacementInto", "tweakeroo.config.hotkey.name.accurateBlockPlacementReverse": "accurateBlockPlacementReverse", "tweakeroo.config.hotkey.name.areaSelectionAddToList": "areaSelectionAddToList", "tweakeroo.config.hotkey.name.areaSelectionOffset": "areaSelectionOffset", "tweakeroo.config.hotkey.name.areaSelectionRemoveFromList": "areaSelectionRemoveFromList", "tweakeroo.config.hotkey.name.breakingRestrictionModeColumn": "breakingRestrictionModeColumn", "tweakeroo.config.hotkey.name.breakingRestrictionModeDiagonal": "breakingRestrictionModeDiagonal", "tweakeroo.config.hotkey.name.breakingRestrictionModeFace": "breakingRestrictionModeFace", "tweakeroo.config.hotkey.name.breakingRestrictionModeLayer": "breakingRestrictionModeLayer", "tweakeroo.config.hotkey.name.breakingRestrictionModeLine": "breakingRestrictionModeLine", "tweakeroo.config.hotkey.name.breakingRestrictionModePlane": "breakingRestrictionModePlane", "tweakeroo.config.hotkey.name.copySignText": "copySignText", "tweakeroo.config.hotkey.name.elytraCamera": "elytraCamera", "tweakeroo.config.hotkey.name.flexibleBlockPlacementAdjacent": "flexibleBlockPlacementAdjacent", "tweakeroo.config.hotkey.name.flexibleBlockPlacementOffset": "flexibleBlockPlacementOffset", "tweakeroo.config.hotkey.name.flexibleBlockPlacementRotation": "flexibleBlockPlacementRotation", "tweakeroo.config.hotkey.name.flyIncrement1": "flyIncrement1", "tweakeroo.config.hotkey.name.flyIncrement2": "flyIncrement2", "tweakeroo.config.hotkey.name.flyPreset1": "flyPreset1", "tweakeroo.config.hotkey.name.flyPreset2": "flyPreset2", "tweakeroo.config.hotkey.name.flyPreset3": "flyPreset3", "tweakeroo.config.hotkey.name.flyPreset4": "flyPreset4", "tweakeroo.config.hotkey.name.freeCameraPlayerInputs": "freeCameraPlayerInputs", "tweakeroo.config.hotkey.name.freeCameraPlayerMovement": "freeCameraPlayerMovement", "tweakeroo.config.hotkey.name.hotbarScroll": "hotbarScroll", "tweakeroo.config.hotkey.name.hotbarSwap1": "hotbarSwap1", "tweakeroo.config.hotkey.name.hotbarSwap2": "hotbarSwap2", "tweakeroo.config.hotkey.name.hotbarSwap3": "hotbarSwap3", "tweakeroo.config.hotkey.name.hotbarSwapBase": "hotbarSwapBase", "tweakeroo.config.hotkey.name.inventoryPreview": "inventoryPreview", "tweakeroo.config.hotkey.name.inventoryPreviewToggleScreen": "inventoryPreviewToggleScreen", "tweakeroo.config.hotkey.name.openConfigGui": "openConfigGui", "tweakeroo.config.hotkey.name.placementRestrictionModeColumn": "placementRestrictionModeColumn", "tweakeroo.config.hotkey.name.placementRestrictionModeDiagonal": "placementRestrictionModeDiagonal", "tweakeroo.config.hotkey.name.placementRestrictionModeFace": "placementRestrictionModeFace", "tweakeroo.config.hotkey.name.placementRestrictionModeLayer": "placementRestrictionModeLayer", "tweakeroo.config.hotkey.name.placementRestrictionModeLine": "placementRestrictionModeLine", "tweakeroo.config.hotkey.name.placementRestrictionModePlane": "placementRestrictionModePlane", "tweakeroo.config.hotkey.name.placementYMirror": "placementYMirror", "tweakeroo.config.hotkey.name.playerInventoryPeek": "playerInventoryPeek", "tweakeroo.config.hotkey.name.sitDownNearbyPets": "sitDownNearbyPets", "tweakeroo.config.hotkey.name.skipAllRendering": "skip<PERSON><PERSON><PERSON><PERSON>ing", "tweakeroo.config.hotkey.name.skipWorldRendering": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tweakeroo.config.hotkey.name.standUpNearbyPets": "standUpNearbyPets", "tweakeroo.config.hotkey.name.swapElytraChestplate": "swapElytraChestplate", "tweakeroo.config.hotkey.name.toggleAccuratePlacementProtocol": "toggleAccuratePlacementProtocol", "tweakeroo.config.hotkey.name.toggleGrabCursor": "toggleGrabCursor", "tweakeroo.config.hotkey.name.toolPick": "toolPick", "tweakeroo.config.hotkey.name.writeMapsAsImages": "writeMapsAsImages", "tweakeroo.config.hotkey.name.zoomActivate": "zoomActivate", "tweakeroo.config.internal.comment.darknessScaleValueOriginal": "The original darkness scale value, before the 'tweak darkness visibility' was enabled", "tweakeroo.config.internal.comment.flySpeedPreset": "This is just for the mod internally to track the\ncurrently selected fly speed preset", "tweakeroo.config.internal.comment.gammaValueOriginal": "The original gamma value, before the gamma override was enabled", "tweakeroo.config.internal.comment.hotbarScrollCurrentRow": "This is just for the mod internally to track the\n\"current hotbar row\" for the hotbar scrolling feature", "tweakeroo.config.internal.comment.slimeBlockSlipperinessOriginal": "The original slipperiness value of Slime Blocks", "tweakeroo.config.internal.comment.snapAimLastPitch": "The last snapped-to pitch value", "tweakeroo.config.internal.comment.snapAimLastYaw": "The last snapped-to yaw value", "tweakeroo.config.internal.name.darknessScaleValueOriginal": "darknessScaleValueOriginal", "tweakeroo.config.internal.name.flySpeedPreset": "flySpeedPreset", "tweakeroo.config.internal.name.gammaValueOriginal": "gammaValueOriginal", "tweakeroo.config.internal.name.hotbarScrollCurrentRow": "hotbarScrollCurrentRow", "tweakeroo.config.internal.name.slimeBlockSlipperinessOriginal": "slimeBlockSlipperinessOriginal", "tweakeroo.config.internal.name.snapAimLastPitch": "snapAimLastPitch", "tweakeroo.config.internal.name.snapAimLastYaw": "snapAimLastYaw", "tweakeroo.config.lists.comment.blockTypeBreakRestrictionBlackList": "The blocks that are NOT allowed to be broken while the Block Break Restriction tweak is enabled,\nif the blockBreakRestrictionListType is set to Black List", "tweakeroo.config.lists.comment.blockTypeBreakRestrictionListType": "The restriction list type for the Block Type Break Restriction tweak", "tweakeroo.config.lists.comment.blockTypeBreakRestrictionWhiteList": "The only blocks that can be broken while the Block Break Restriction tweak is enabled,\nif the blockBreakRestrictionListType is set to White List", "tweakeroo.config.lists.comment.creativeExtraItems": "Extra items that should be appended to the creative inventory.\nCurrently these will appear in the Transportation category.\nIn the future the group per added item will be customizable.", "tweakeroo.config.lists.comment.entityTypeAttackRestrictionBlackList": "The entities that are NOT allowed to be attacked while the Entity Attack Restriction tweak is enabled,\nif the entityAttackRestrictionListType is set to Black List", "tweakeroo.config.lists.comment.entityTypeAttackRestrictionListType": "The restriction list type for the Entity Type Attack Restriction tweak", "tweakeroo.config.lists.comment.entityTypeAttackRestrictionWhiteList": "The only entities that can be attacked while the Entity Attack Restriction tweak is enabled,\nif the entityAttackRestrictionListType is set to White List", "tweakeroo.config.lists.comment.entityWeaponMapping": "Mapping for what weapon should be used with the\n'tweakWeaponSwitch' tweak.\n'<default>' will be used when no other mapping is defined.\n'<ignore>' will not trigger a weapon switch.", "tweakeroo.config.lists.comment.fastPlacementItemBlackList": "The items that are NOT allowed to be used for the Fast Block Placement tweak,\nif the fastPlacementItemListType is set to Black List", "tweakeroo.config.lists.comment.fastPlacementItemListType": "The item restriction type for the Fast Block Placement tweak", "tweakeroo.config.lists.comment.fastPlacementItemWhiteList": "The items that are allowed to be used for the Fast Block Placement tweak,\nif the fastPLacementItemListType is set to White List", "tweakeroo.config.lists.comment.fastRightClickBlackList": "The items that are NOT allowed to be used for the Fast Right Click tweak,\nif the fastRightClickListType is set to Black List", "tweakeroo.config.lists.comment.fastRightClickBlockBlackList": "The blocks that are NOT allowed to be right clicked on with\nthe Fast Right Click tweak, if the fastRightClickBlockListType is set to Black List", "tweakeroo.config.lists.comment.fastRightClickBlockListType": "The targeted block restriction type for the Fast Right Click tweak", "tweakeroo.config.lists.comment.fastRightClickBlockWhiteList": "The blocks that are allowed to be right clicked on with\nthe Fast Right Click tweak, if the fastRightClickBlockListType is set to White List", "tweakeroo.config.lists.comment.fastRightClickListType": "The item restriction type for the Fast Right Click tweak", "tweakeroo.config.lists.comment.fastRightClickWhiteList": "The items that are allowed to be used for the Fast Right Click tweak,\nif the fastRightClickListType is set to White List", "tweakeroo.config.lists.comment.flatWorldPresets": "Custom flat world preset strings.\nThese are in the format: name;blocks_string;biome;generation_features;icon_item\nThe blocks string format is the vanilla format, such as: 62*minecraft:dirt,minecraft:grass\nThe biome can be the registry name, or the int ID\nThe icon item name format is minecraft:iron_nugget", "tweakeroo.config.lists.comment.handRestockBlackList": "The items that are NOT allowed to be restocked with the Hand Restock tweak,\nif the handRestockListType is set to Black List", "tweakeroo.config.lists.comment.handRestockListType": "The restriction list type for the Hand Restock tweak", "tweakeroo.config.lists.comment.handRestockWhiteList": "The only allowed items that can be restocked with the Hand Restock tweak,\nif the handRestockListType is set to White List", "tweakeroo.config.lists.comment.potionWarningBlackList": "The potion effects that will not be warned about", "tweakeroo.config.lists.comment.potionWarningListType": "The list type for potion warning effects", "tweakeroo.config.lists.comment.potionWarningWhiteList": "The only potion effects that will be warned about", "tweakeroo.config.lists.comment.silkTouchOverride": "When using tweak tool switch, add blocks or\nblock tags to this list to apply SilkTouchFirst to,\nin addition to using the '§b#malilib:needs_silk_touch§r' Block Tag\nbased 'toolSwapSilkTouchFirst' method when\n'toolSwapSilkTouchOverride' is enabled.\n\nA useful example, would be adding\n'§bminecraft:stone§r' to this list.\n\nThe Block Tag exists, so that you don't need\nto add dozens of blocks to a list like this.\nBut; You can configure this list to work this way\nby disabling 'toolSwapSilkTouchFirst' and only enabling\n`toolSwapSilkTouchOverride` instead.\nYou may then want to make use of some of\nthe existing `§b#malilib:§r` or '§b#minecraft:§r' block tags here;\nsuch as adding '§b#malilib:glass_blocks§r', for example\ninstead of typing in all 18 glass blocks one by one.", "tweakeroo.config.lists.comment.repairModeSlots": "The slots the repair mode should use\nValid values: mainhand, offhand, head, chest, legs, feet", "tweakeroo.config.lists.comment.selectiveBlocksBlacklist": "The block positions you want to blacklist\nWritten by Andrew54757 under TweakFork", "tweakeroo.config.lists.comment.selectiveBlocksListType": "The list type for selective blocks tweak\nWritten by Andrew54757 under TweakFork", "tweakeroo.config.lists.comment.selectiveBlocksWhitelist": "The block positions you want to whitelist\nWritten by Andrew54757 under TweakFork", "tweakeroo.config.lists.comment.unstackingItems": "The items that should be considered for the\n'tweakItemUnstackingProtection' tweak", "tweakeroo.config.lists.name.blockTypeBreakRestrictionBlackList": "blockTypeBreakRestrictionBlackList", "tweakeroo.config.lists.name.blockTypeBreakRestrictionListType": "blockTypeBreakRestrictionListType", "tweakeroo.config.lists.name.blockTypeBreakRestrictionWhiteList": "blockTypeBreakRestrictionWhiteList", "tweakeroo.config.lists.name.creativeExtraItems": "creativeExtraItems", "tweakeroo.config.lists.name.entityTypeAttackRestrictionBlackList": "entityTypeAttackRestrictionBlackList", "tweakeroo.config.lists.name.entityTypeAttackRestrictionListType": "entityTypeAttackRestrictionListType", "tweakeroo.config.lists.name.entityTypeAttackRestrictionWhiteList": "entityTypeAttackRestrictionWhiteList", "tweakeroo.config.lists.name.entityWeaponMapping": "entityWeaponMapping", "tweakeroo.config.lists.name.fastPlacementItemBlackList": "fastPlacementItemBlackList", "tweakeroo.config.lists.name.fastPlacementItemListType": "fastPlacementItemListType", "tweakeroo.config.lists.name.fastPlacementItemWhiteList": "fastPlacementItemWhiteList", "tweakeroo.config.lists.name.fastRightClickBlackList": "fastRightClickBlackList", "tweakeroo.config.lists.name.fastRightClickBlockBlackList": "fastRightClickBlockBlackList", "tweakeroo.config.lists.name.fastRightClickBlockListType": "fastRightClickBlockListType", "tweakeroo.config.lists.name.fastRightClickBlockWhiteList": "fastRightClickBlockWhiteList", "tweakeroo.config.lists.name.fastRightClickListType": "fastRightClickListType", "tweakeroo.config.lists.name.fastRightClickWhiteList": "fastRightClickWhiteList", "tweakeroo.config.lists.name.flatWorldPresets": "flatWorldPresets", "tweakeroo.config.lists.name.handRestockBlackList": "handRestockBlackList", "tweakeroo.config.lists.name.handRestockListType": "handRestockListType", "tweakeroo.config.lists.name.handRestockWhiteList": "handRestockWhiteList", "tweakeroo.config.lists.name.potionWarningBlackList": "potionWarningBlackList", "tweakeroo.config.lists.name.potionWarningListType": "potionWarningListType", "tweakeroo.config.lists.name.potionWarningWhiteList": "potionWarningWhiteList", "tweakeroo.config.lists.name.silkTouchOverride": "silkTouchOverride", "tweakeroo.config.lists.name.repairModeSlots": "repairModeSlots", "tweakeroo.config.lists.name.selectiveBlocksBlacklist": "selectiveBlocksBlacklist", "tweakeroo.config.lists.name.selectiveBlocksListType": "selectiveBlocksListType", "tweakeroo.config.lists.name.selectiveBlocksWhitelist": "selective<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tweakeroo.config.lists.name.unstackingItems": "unstackingItems", "tweakeroo.gui.button.config_gui.disables": "Yeets", "tweakeroo.gui.button.config_gui.fixes": "Fixes", "tweakeroo.gui.button.config_gui.generic": "Generic", "tweakeroo.gui.button.config_gui.generic_hotkeys": "Hotkeys", "tweakeroo.gui.button.config_gui.generic_config_hotkeys": "Generic Hotkeys", "tweakeroo.gui.button.config_gui.lists": "Lists", "tweakeroo.gui.button.config_gui.placement": "Placement stuff", "tweakeroo.gui.button.config_gui.tweaks": "Tweaks", "tweakeroo.gui.button.misc.command_block.hover.update_execution": "Whether or not multiple triggers per game tick are allowed", "tweakeroo.gui.button.misc.command_block.set_name": "Set name", "tweakeroo.gui.button.misc.command_block.update_execution.looping": "Looping", "tweakeroo.gui.button.misc.command_block.update_execution.off": "Loops: §cOFF", "tweakeroo.gui.button.misc.command_block.update_execution.on": "Loops: §aON", "tweakeroo.gui.label.easy_place_protocol.auto": "Auto", "tweakeroo.gui.label.easy_place_protocol.none": "None", "tweakeroo.gui.label.easy_place_protocol.slabs_only": "Slabs only", "tweakeroo.gui.label.easy_place_protocol.v2": "Version 2", "tweakeroo.gui.label.easy_place_protocol.v3": "Version 3", "tweakeroo.gui.title.configs": "Tweakeroo Configs - %s", "tweakeroo.hotkeys.category.disable_toggle_hotkeys": "Disable Toggle Hotkeys", "tweakeroo.hotkeys.category.generic_hotkeys": "Generic hotkeys", "tweakeroo.hotkeys.category.tweak_toggle_hotkeys": "Tweak toggle hotkeys", "tweakeroo.label.config_comment.single_player_only": "§6Note: This feature works either at all or§r\n§6at least fully only in single player§r", "tweakeroo.label.placement_restriction_mode.column": "Column", "tweakeroo.label.placement_restriction_mode.diagonal": "Diagonal", "tweakeroo.label.placement_restriction_mode.face": "Face", "tweakeroo.label.placement_restriction_mode.layer": "Layer", "tweakeroo.label.placement_restriction_mode.line": "Line", "tweakeroo.label.placement_restriction_mode.plane": "Plane", "tweakeroo.label.snap_aim_mode.both": "Yaw & Pitch", "tweakeroo.label.snap_aim_mode.pitch": "Pitch", "tweakeroo.label.snap_aim_mode.yaw": "Yaw", "tweakeroo.message.death_coordinates": "§6Y<PERSON> died§r @ [§b%d, %d, %d§r] in §a%s", "tweakeroo.message.focusing_game": "§6Focusing§f the game (grabbing the cursor)", "tweakeroo.message.potion_effects_running_out": "§6!! WARNING - %s potion effect(s) about to run out in %s seconds !!§r", "tweakeroo.message.repair_mode.swapped_repairable_item_to_slot": "Swapped a repairable item to %s slot", "tweakeroo.message.set_after_clicker_count_to": "Set After Clicker count to %s", "tweakeroo.message.set_breaking_grid_size_to": "Set Breaking Grid size to %s", "tweakeroo.message.set_breaking_restriction_mode_to": "Set Breaking Restriction mode to %s", "tweakeroo.message.set_fly_speed_preset_to": "Switched to fly speed preset %s (speed: %s)", "tweakeroo.message.set_fly_speed_to": "Set fly speed of preset %s to %s", "tweakeroo.message.set_hotbar_slot_cycle_max_to": "Set Hotbar Slot Cycle max slot to %s", "tweakeroo.message.set_hotbar_slot_randomizer_max_to": "Set Hotbar Slot Randomizer max slot to %s", "tweakeroo.message.set_periodic_attack_interval_to": "Set Periodic Attack Interval to %s", "tweakeroo.message.set_periodic_hold_attack_interval_to": "Set Periodic Hold Attack Interval to %s", "tweakeroo.message.set_periodic_hold_use_interval_to": "Set Periodic Hold Use Interval to %s", "tweakeroo.message.set_periodic_use_interval_to": "Set Periodic Use Interval to %s", "tweakeroo.message.set_placement_grid_size_to": "Set Placement Grid size to %s", "tweakeroo.message.set_placement_limit_to": "Set Placement Limit to %s", "tweakeroo.message.set_placement_restriction_mode_to": "Set Placement Restriction mode to %s", "tweakeroo.message.set_snap_aim_pitch_step_to": "Set Snap Aim pitch step to %s", "tweakeroo.message.set_snap_aim_yaw_step_to": "Set Snap Aim yaw step to %s", "tweakeroo.message.set_zoom_fov_to": "Set Camera Zoom FOV to %s", "tweakeroo.message.sign_text_copied": "Sign text copied", "tweakeroo.message.snapped_to_pitch": "Snapped to pitch %s", "tweakeroo.message.snapped_to_yaw": "Snapped to yaw %s", "tweakeroo.message.swapped_low_durability_item_for_better_durability": "Swapped a low durability item for one with more durability", "tweakeroo.message.swapped_low_durability_item_for_dummy_item": "Swapped a low durability item for a dummy item", "tweakeroo.message.swapped_low_durability_item_off_players_hand": "Swapped a low durability item off the player's hand", "tweakeroo.message.toggled": "Toggled %s %s", "tweakeroo.message.toggled_after_clicker_on": "Toggled After Clicker tweak %s, clicks: %s", "tweakeroo.message.toggled_breaking_grid_on": "Toggled Breaking Grid tweak %s, grid interval: %s", "tweakeroo.message.toggled_fast_placement_mode_on": "Toggled Fast Placement mode %s, mode: %s", "tweakeroo.message.toggled_fly_speed_on": "Toggled Fly Speed tweak %s, preset: %s, speed: %s", "tweakeroo.message.toggled_periodic": "Toggled %s %s, speed: %s", "tweakeroo.message.toggled_placement_grid_on": "Toggled Placement Grid tweak %s, grid interval: %s", "tweakeroo.message.toggled_placement_limit_on": "Toggled Placement Limit tweak %s, limit: %s", "tweakeroo.message.toggled_slot_cycle_on": "Toggled Hotbar Slot Cycle tweak %s, max slot: %s", "tweakeroo.message.toggled_slot_randomizer_on": "Toggled Hotbar Slot Randomizer tweak %s, max slot: %s", "tweakeroo.message.toggled_snap_aim_on_both": "Toggled Yaw & Pitch Snap Aim %s, with yaw step %s, pitch step %s", "tweakeroo.message.toggled_snap_aim_on_pitch": "Toggled Pitch Snap Aim %s, with pitch step size %s degrees", "tweakeroo.message.toggled_snap_aim_on_yaw": "Toggled Yaw Snap Aim %s, with yaw step size %s degrees", "tweakeroo.message.toggled_zoom_activate_off": "Camera Zoom Deactivated, FOV: %s", "tweakeroo.message.toggled_zoom_activate_on": "Camera Zoom Activated, FOV: %s", "tweakeroo.message.toggled_zoom_on": "Toggled Camera Zoom %s, FOV: %s", "tweakeroo.message.unfocusing_game": "§6Un-focusing§f the game (un-grabbing the cursor)", "tweakeroo.message.value.off": "OFF", "tweakeroo.message.value.on": "ON", "tweakeroo.message.warning.block_type_break_restriction": "§6Block breaking prevented by Block Type Break Restriction tweak", "tweakeroo.message.warning.entity_type_attack_restriction": "§6Entity attack prevented by Entity Type Attack Restriction tweak"}