{"modmenu.descriptionTranslation.tweakeroo": "設定可能なクライアントサイドの調整機能を多数追加します", "tweakeroo.config.disable.comment.disableArmorStandRendering": "すべてのアーマースタンドエンティティのレンダリングを無効にします", "tweakeroo.config.disable.comment.disableAtmosphericFog": "プレイヤーと描画距離フォグの間に存在する\nすべての大気フォグを無効にします。", "tweakeroo.config.disable.comment.disableAxeStripping": "斧での原木の皮剥ぎを無効にします", "tweakeroo.config.disable.comment.disableBatSpawning": "シングルプレイヤーでのコウモリのスポーンを無効にします", "tweakeroo.config.disable.comment.disableBeaconBeamRendering": "ビーコンビームのレンダリングを無効にします", "tweakeroo.config.disable.comment.disableBlockBreakCooldown": "ブロック破壊のクールダウンを無効にします。\nブロックを破壊する間の時間を短縮します", "tweakeroo.config.disable.comment.disableBlockBreakingParticles": "ブロック破壊パーティクルを削除します。\n（これは元々nessieのusefulmodからのものです。）", "tweakeroo.config.disable.comment.disableBossBar": "ボスバーのレンダリングを無効にします", "tweakeroo.config.disable.comment.disableBossFog": "ボスモブが引き起こすフォグを削除します", "tweakeroo.config.disable.comment.disableChunkRendering": "チャンクの（再）レンダリングを無効にします。これにより、ブロックの変更が\n再び有効にしてF3 + Aでワールドレンダリングを更新するまで見えなくなります。\nブロックの変更があまり関係ない状況で、多くのブロック変更がある場所での\n低FPSの改善に役立つ場合があります。", "tweakeroo.config.disable.comment.disableClientEntityUpdates": "プレイヤーエンティティ以外のすべてのクライアント側エンティティ更新を無効にします。\nこれは主に過剰なエンティティ数に関連する問題を修正するために\n何かを行う必要がある状況を対象としています", "tweakeroo.config.disable.comment.disableClientLightUpdates": "すべてのクライアント側光源更新を無効にします", "tweakeroo.config.disable.comment.disableConstantChunkSaving": "通常の自動保存サイクルに加えて、\nゲームが毎ティック最大20チャンクを保存することを無効にします。", "tweakeroo.config.disable.comment.disableCreativeMenuInfestedBlocks": "クリエイティブ検索インベントリから虫食い石ブロックを削除します", "tweakeroo.config.disable.comment.disableDeadMobRendering": "死んだモブ（体力が0のエンティティ）のレンダリングを防ぎます", "tweakeroo.config.disable.comment.disableDeadMobTargeting": "体力が0のエンティティをターゲットすることを防ぎます。\nこれにより、例えば既に死んだモブを攻撃することを修正します。", "tweakeroo.config.disable.comment.disableDoubleTapSprint": "前進キーのダブルタップスプリントを無効にします", "tweakeroo.config.disable.comment.disableEntityRendering": "プレイヤーエンティティ以外のすべてのエンティティレンダリングを無効にします。\nこれは主に過剰なエンティティ数に関連する問題を修正するために\n何かを行う必要がある状況を対象としています", "tweakeroo.config.disable.comment.disableEntityTicking": "プレイヤーエンティティ以外のすべてのエンティティのティックを防ぎます", "tweakeroo.config.disable.comment.disableFallingBlockEntityRendering": "有効にすると、落下ブロックエンティティが全くレンダリングされなくなります", "tweakeroo.config.disable.comment.disableFirstPersonEffectParticles": "一人称視点でのポーション効果パーティクル/渦巻きを削除します\n（プレイヤー自身からの）", "tweakeroo.config.disable.comment.disableInventoryEffectRendering": "インベントリGUIからポーション効果のレンダリングを削除します", "tweakeroo.config.disable.comment.disableItemSwitchRenderCooldown": "trueの場合、持っているアイテムを切り替えたり使用したりする際の\nクールダウン/装備アニメーションがなくなります。", "tweakeroo.config.disable.comment.disableMobSpawnerMobRendering": "モブスポナーからのエンティティレンダリングを削除します", "tweakeroo.config.disable.comment.disableNauseaEffect": "吐き気の視覚効果を無効にします", "tweakeroo.config.disable.comment.disableNetherFog": "ネザーのフォグを削除します", "tweakeroo.config.disable.comment.disableNetherPortalSound": "ネザーポータルの音を無効にします", "tweakeroo.config.disable.comment.disableObserver": "オブザーバーが全く作動しないようにします", "tweakeroo.config.disable.comment.disableOffhandRendering": "オフハンドアイテムのレンダリングを無効にします", "tweakeroo.config.disable.comment.disableParticles": "すべてのパーティクルを無効にします", "tweakeroo.config.disable.comment.disablePortalGuiClosing": "有効にすると、ネザーポータル内でもGUIを開くことができます", "tweakeroo.config.disable.comment.disableRainEffects": "雨のレンダリングと音を無効にします", "tweakeroo.config.disable.comment.disableRenderDistanceFog": "描画距離周辺で増加するフォグを無効にします", "tweakeroo.config.disable.comment.disableRenderingScaffolding": "足場ブロックのレンダリングを無効にします", "tweakeroo.config.disable.comment.disableScoreboardRendering": "サイドバーのスコアボードレンダリングを削除します", "tweakeroo.config.disable.comment.disableShovelPathing": "シャベルで草などを道ブロックに変換することを無効にします", "tweakeroo.config.disable.comment.disableShulkerBoxTooltip": "シュルカーボックスの内容物のバニラテキストツールチップを無効にします", "tweakeroo.config.disable.comment.disableSignGui": "看板編集GUIが開くことを防ぎます", "tweakeroo.config.disable.comment.disableSkyDarkness": "y = 63以下での空の暗さを無効にします\n\n（閾値yを世界の底から2ブロック下に移動することで）", "tweakeroo.config.disable.comment.disableSlimeBlockSlowdown": "スライムブロック上を歩く際の減速を削除します。\n（これは元々nessieのusefulmodからのものです。）", "tweakeroo.config.disable.comment.disableStatusEffectHud": "ステータス効果HUDのレンダリングを無効にします（通常は\n画面の右上角にあります）", "tweakeroo.config.disable.comment.disableTileEntityRendering": "すべてのタイルエンティティレンダラーのレンダリングを防ぎます", "tweakeroo.config.disable.comment.disableTileEntityTicking": "すべてのタイルエンティティのティックを防ぎます", "tweakeroo.config.disable.comment.disableVillagerTradeLocking": "村人の取引がロックされることを防ぎます。レシピの使用回数が増加する際に\n最大使用回数も常に増加させることで実現します", "tweakeroo.config.disable.comment.disableWallUnsprint": "壁に触れてもスプリントモードから外れません", "tweakeroo.config.disable.comment.disableWorldViewBob": "世界の視点ボブ揺れ効果を無効にしますが、手は除きます\nIrisがインストールされている場合、この設定は失敗します。", "tweakeroo.config.disable.name.disableArmorStandRendering": "アーマースタンドレンダリング無効", "tweakeroo.config.disable.name.disableAtmosphericFog": "大気フォグ無効", "tweakeroo.config.disable.name.disableAxeStripping": "斧皮剥ぎ無効", "tweakeroo.config.disable.name.disableBatSpawning": "コウモリスポーン無効", "tweakeroo.config.disable.name.disableBeaconBeamRendering": "ビーコンビームレンダリング無効", "tweakeroo.config.disable.name.disableBlockBreakCooldown": "ブロック破壊クールダウン無効", "tweakeroo.config.disable.name.disableBlockBreakingParticles": "ブロック破壊パーティクル無効", "tweakeroo.config.disable.name.disableBossBar": "ボスバー無効", "tweakeroo.config.disable.name.disableBossFog": "ボスフォグ無効", "tweakeroo.config.disable.name.disableChunkRendering": "チャンクレンダリング無効", "tweakeroo.config.disable.name.disableClientEntityUpdates": "クライアントエンティティ更新無効", "tweakeroo.config.disable.name.disableClientLightUpdates": "クライアント光源更新無効", "tweakeroo.config.disable.name.disableConstantChunkSaving": "常時チャンク保存無効", "tweakeroo.config.disable.name.disableCreativeMenuInfestedBlocks": "クリエイティブメニュー虫食いブロック無効", "tweakeroo.config.disable.name.disableDeadMobRendering": "死亡モブレンダリング無効", "tweakeroo.config.disable.name.disableDeadMobTargeting": "死亡モブターゲット無効", "tweakeroo.config.disable.name.disableDoubleTapSprint": "ダブルタップスプリント無効", "tweakeroo.config.disable.name.disableEntityRendering": "エンティティレンダリング無効", "tweakeroo.config.disable.name.disableEntityTicking": "エンティティティック無効", "tweakeroo.config.disable.name.disableFallingBlockEntityRendering": "落下ブロックエンティティレンダリング無効", "tweakeroo.config.disable.name.disableFirstPersonEffectParticles": "一人称効果パーティクル無効", "tweakeroo.config.disable.name.disableInventoryEffectRendering": "インベントリ効果レンダリング無効", "tweakeroo.config.disable.name.disableItemSwitchRenderCooldown": "アイテム切替レンダリングクールダウン無効", "tweakeroo.config.disable.name.disableMobSpawnerMobRendering": "モブスポナーモブレンダリング無効", "tweakeroo.config.disable.name.disableNauseaEffect": "吐き気効果無効", "tweakeroo.config.disable.name.disableNetherFog": "ネザーフォグ無効", "tweakeroo.config.disable.name.disableNetherPortalSound": "ネザーポータル音無効", "tweakeroo.config.disable.name.disableObserver": "オブザーバー無効", "tweakeroo.config.disable.name.disableOffhandRendering": "オフハンドレンダリング無効", "tweakeroo.config.disable.name.disableParticles": "パーティクル無効", "tweakeroo.config.disable.name.disablePortalGuiClosing": "ポータルGUI閉じる無効", "tweakeroo.config.disable.name.disableRainEffects": "雨効果無効", "tweakeroo.config.disable.name.disableRenderDistanceFog": "描画距離フォグ無効", "tweakeroo.config.disable.name.disableRenderingScaffolding": "足場レンダリング無効", "tweakeroo.config.disable.name.disableScoreboardRendering": "スコアボードレンダリング無効", "tweakeroo.config.disable.name.disableShovelPathing": "シャベル道作り無効", "tweakeroo.config.disable.name.disableShulkerBoxTooltip": "シュルカーボックスツールチップ無効", "tweakeroo.config.disable.name.disableSignGui": "看板GUI無効", "tweakeroo.config.disable.name.disableSkyDarkness": "空の暗さ無効", "tweakeroo.config.disable.name.disableSlimeBlockSlowdown": "スライムブロック減速無効", "tweakeroo.config.disable.name.disableStatusEffectHud": "ステータス効果HUD無効", "tweakeroo.config.disable.name.disableTileEntityRendering": "タイルエンティティレンダリング無効", "tweakeroo.config.disable.name.disableTileEntityTicking": "タイルエンティティティック無効", "tweakeroo.config.disable.name.disableVillagerTradeLocking": "村人取引ロック無効", "tweakeroo.config.disable.name.disableWallUnsprint": "壁スプリント解除無効", "tweakeroo.config.disable.name.disableWorldViewBob": "世界視点ボブ無効", "tweakeroo.config.disable.prettyName.disableArmorStandRendering": "アーマースタンドレンダリング無効", "tweakeroo.config.disable.prettyName.disableAtmosphericFog": "大気フォグ無効", "tweakeroo.config.disable.prettyName.disableAxeStripping": "斧皮剥ぎ無効", "tweakeroo.config.disable.prettyName.disableBatSpawning": "コウモリスポーン無効", "tweakeroo.config.disable.prettyName.disableBeaconBeamRendering": "ビーコンビームレンダリング無効", "tweakeroo.config.disable.prettyName.disableBlockBreakCooldown": "ブロック破壊クールダウン無効", "tweakeroo.config.disable.prettyName.disableBlockBreakingParticles": "ブロック破壊パーティクル無効", "tweakeroo.config.disable.prettyName.disableBossBar": "ボスバー無効", "tweakeroo.config.disable.prettyName.disableBossFog": "ボスフォグ無効", "tweakeroo.config.disable.prettyName.disableChunkRendering": "チャンクレンダリング無効", "tweakeroo.config.disable.prettyName.disableClientEntityUpdates": "クライアントエンティティ更新無効", "tweakeroo.config.disable.prettyName.disableClientLightUpdates": "クライアント光源更新無効", "tweakeroo.config.disable.prettyName.disableConstantChunkSaving": "常時チャンク保存無効", "tweakeroo.config.disable.prettyName.disableCreativeMenuInfestedBlocks": "クリエイティブメニュー虫食いブロック無効", "tweakeroo.config.disable.prettyName.disableDeadMobRendering": "死亡モブレンダリング無効", "tweakeroo.config.disable.prettyName.disableDeadMobTargeting": "死亡モブターゲット無効", "tweakeroo.config.disable.prettyName.disableDoubleTapSprint": "ダブルタップスプリント無効", "tweakeroo.config.disable.prettyName.disableEntityRendering": "エンティティレンダリング無効", "tweakeroo.config.disable.prettyName.disableEntityTicking": "エンティティティック無効", "tweakeroo.config.disable.prettyName.disableFallingBlockEntityRendering": "落下ブロックエンティティレンダリング無効", "tweakeroo.config.disable.prettyName.disableFirstPersonEffectParticles": "一人称効果パーティクル無効", "tweakeroo.config.disable.prettyName.disableInventoryEffectRendering": "インベントリ効果レンダリング無効", "tweakeroo.config.disable.prettyName.disableItemSwitchRenderCooldown": "アイテム切替レンダリングクールダウン無効", "tweakeroo.config.disable.prettyName.disableMobSpawnerMobRendering": "モブスポナーモブレンダリング無効", "tweakeroo.config.disable.prettyName.disableNauseaEffect": "吐き気効果無効", "tweakeroo.config.disable.prettyName.disableNetherFog": "ネザーフォグ無効", "tweakeroo.config.disable.prettyName.disableNetherPortalSound": "ネザーポータル音無効", "tweakeroo.config.disable.prettyName.disableObserver": "オブザーバー無効", "tweakeroo.config.disable.prettyName.disableOffhandRendering": "オフハンドレンダリング無効", "tweakeroo.config.disable.prettyName.disableParticles": "パーティクル無効", "tweakeroo.config.disable.prettyName.disablePortalGuiClosing": "ポータルGUI閉じる無効", "tweakeroo.config.disable.prettyName.disableRainEffects": "雨効果無効", "tweakeroo.config.disable.prettyName.disableRenderDistanceFog": "描画距離フォグ無効", "tweakeroo.config.disable.prettyName.disableRenderingScaffolding": "足場レンダリング無効", "tweakeroo.config.disable.prettyName.disableScoreboardRendering": "スコアボードレンダリング無効", "tweakeroo.config.disable.prettyName.disableShovelPathing": "シャベル道作り無効", "tweakeroo.config.disable.prettyName.disableShulkerBoxTooltip": "シュルカーボックスツールチップ無効", "tweakeroo.config.disable.prettyName.disableSignGui": "看板GUI無効", "tweakeroo.config.disable.prettyName.disableSkyDarkness": "空の暗さ無効", "tweakeroo.config.disable.prettyName.disableSlimeBlockSlowdown": "スライムブロック減速無効", "tweakeroo.config.disable.prettyName.disableStatusEffectHud": "ステータス効果HUD無効", "tweakeroo.config.disable.prettyName.disableTileEntityRendering": "タイルエンティティレンダリング無効", "tweakeroo.config.disable.prettyName.disableTileEntityTicking": "タイルエンティティティック無効", "tweakeroo.config.disable.prettyName.disableVillagerTradeLocking": "村人取引ロック無効", "tweakeroo.config.disable.prettyName.disableWallUnsprint": "壁スプリント解除無効", "tweakeroo.config.disable.prettyName.disableWorldViewBob": "世界視点ボブ無効", "tweakeroo.config.feature_toggle.comment.tweakAccurateBlockPlacement": "Carpetmodに似た、より簡単なフレキシブル配置を有効にします。\n基本的にクリックしたブロック面に向かって、または\nそこから離れる方向に配置します。", "tweakeroo.config.feature_toggle.comment.tweakAfterClicker": "「アフタークリッカー」調整を有効にします。配置したばかりの\nブロックに自動的に右クリックを行います。\nリピーター（遅延設定）などに便利です。\n値を素早く調整するには、調整トグルキーバインドを\n押しながらスクロールしてください。", "tweakeroo.config.feature_toggle.comment.tweakAimLock": "エイムロックを有効にし、ヨーとピッチの回転を\n現在の値にロックします。\nこれはスナップエイムロックとは別で、\nスナップされた値にロックするものです。\nこれにより「自由に」現在の値にロックできます。", "tweakeroo.config.feature_toggle.comment.tweakAngelBlock": "「エンジェルブロック」調整を有効にします。\nクリエイティブモードで空中にブロックを配置できます。\n「<PERSON>lotato」技術によって動作します。", "tweakeroo.config.feature_toggle.comment.tweakAreaSelector": "エリアセレクターを有効にします\nTweakForkのAndrew54757によって作成", "tweakeroo.config.feature_toggle.comment.tweakAutoSwitchElytra": "落下時に自動的にエリトラに切り替え、\n着地時に前の胸装備に戻します。", "tweakeroo.config.feature_toggle.comment.tweakBlockReachOverride": "ブロックリーチ距離を\nGeneric -> blockReachDistanceで設定された値で上書きします", "tweakeroo.config.feature_toggle.comment.tweakBlockTypeBreakRestriction": "（手動で）破壊できるブロックを制限します。\nListsカテゴリの対応する'blockBreakRestriction*'設定を参照してください。", "tweakeroo.config.feature_toggle.comment.tweakBreakingGrid": "有効にすると、設定可能な間隔で\nグリッドパターンでのみブロックを破壊できます。\n間隔を素早く調整するには、調整トグルキーバインドを\n押しながらスクロールしてください。", "tweakeroo.config.feature_toggle.comment.tweakBreakingRestriction": "破壊制限モードを有効にします\n（平面、レイヤー、面、列、線、対角線）。\n基本的に攻撃キーを押している間、\nそれらのパターンでのみブロックを破壊できます。", "tweakeroo.config.feature_toggle.comment.tweakBundleDisplay": "バンドルアイテムにカーソルを合わせてシフトを押している時に\nバンドルの内容のプレビューをレンダリングします", "tweakeroo.config.feature_toggle.comment.tweakChatBackgroundColor": "デフォルトのチャット背景色を\nGenerics -> 'chatBackgroundColor'の色で上書きします", "tweakeroo.config.feature_toggle.comment.tweakChatPersistentText": "チャット入力テキストフィールドのテキストを保存し、\nチャットが再び開かれた時に復元します", "tweakeroo.config.feature_toggle.comment.tweakChatTimestamp": "チャットメッセージにタイムスタンプを追加します", "tweakeroo.config.feature_toggle.comment.tweakCommandBlockExtraFields": "コマンドブロックGUIに追加フィールドを追加します。\nコマンドブロックの名前設定と統計結果の表示用です", "tweakeroo.config.feature_toggle.comment.tweakCreativeExtraItems": "アイテムグループにカスタムアイテムを追加します。\nグループに追加するアイテムを制御するにはLists -> 'creativeExtraItems'を参照してください。\n注意：現在これらは交通グループに追加されます\n（最もアイテムが少ないため）が、将来的には\n追加アイテムごとにグループを設定可能になります", "tweakeroo.config.feature_toggle.comment.tweakCustomFlatPresets": "リストにカスタムフラットワールドプリセットを追加できます。\nプリセットはLists -> flatWorldPresetsで定義されます", "tweakeroo.config.feature_toggle.comment.tweakCustomFlyDeceleration": "クリエイティブまたはスペクテイターモードでの飛行減速を変更できます。\nこれは主により速い減速、つまり移動キーを離した時の\n「滑り」を少なくするためのものです。\nGeneric -> flyDecelerationRampValueを参照してください", "tweakeroo.config.feature_toggle.comment.tweakCustomInventoryScreenScale": "任意のインベントリ画面でカスタムGUIスケールを使用できます。\nスケール値についてはGeneric -> §ecustomInventoryGuiScale§rを参照してください", "tweakeroo.config.feature_toggle.comment.tweakDarknessVisibility": "有効にすると、暗闇ステータス効果の影響を受けている間の\n視認性が向上します。", "tweakeroo.config.feature_toggle.comment.tweakElytraCamera": "'elytraCamera'アクティベーションキーを押している間、実際のプレイヤーの回転をロックできます。\nコントロールはレンダリング/カメラ用の別の「カメラ回転」にのみ影響します。\nエリトラで真っ直ぐ飛行しながら下や周りを見回すためのものです。", "tweakeroo.config.feature_toggle.comment.tweakEmptyShulkerBoxesStack": "空のシュルカーボックスを64個までスタックできるようにします。\n注意：インベントリ内でもスタックします！\nサーバーでは、同じことを行うmodがない限り\n同期ずれ/不具合を引き起こします。\nシングルプレイヤーではシュルカーボックスベースのシステム動作が変わります。", "tweakeroo.config.feature_toggle.comment.tweakEntityReachOverride": "エンティティリーチ距離を\nGeneric -> entityReachDistanceで設定された値で上書きします", "tweakeroo.config.feature_toggle.comment.tweakEntityTypeAttackRestriction": "（手動で）攻撃できるエンティティを制限します。\nListsカテゴリの対応する'entityAttackRestriction*'設定を参照してください。", "tweakeroo.config.feature_toggle.comment.tweakExplosionReducedParticles": "有効にすると、すべての爆発パーティクルが\nEXPLOSION_LARGEやEXPLOSION_HUGEパーティクルの代わりに\nEXPLOSION_NORMALパーティクルを使用します", "tweakeroo.config.feature_toggle.comment.tweakF3Cursor": "F3画面カーソルを常にレンダリングします", "tweakeroo.config.feature_toggle.comment.tweakFakeSneakPlacement": "この調整は、実際にクリックしたブロックから\n隣接する空気ブロックにクリック位置をオフセットします。\nこれにより基本的に、インベントリGUIを開くなどの\nクリックアクションがあるブロックに対して、\nスニークせずにブロックを配置できます。これは実際には\nスニークを偽装するわけではなく、見た目の効果が似ているだけです。", "tweakeroo.config.feature_toggle.comment.tweakFakeSneaking": "「偽スニーク」を有効にします。つまり、移動速度を\n遅くすることなく端から落ちることを防ぎます", "tweakeroo.config.feature_toggle.comment.tweakFastBlockPlacement": "新しいブロックにカーソルを移動する際の\n高速/便利なブロック配置を有効にします", "tweakeroo.config.feature_toggle.comment.tweakFastLeftClick": "攻撃ボタン（左クリック）を押している間の\n自動高速左クリックを有効にします。\nティックあたりのクリック数はGeneric設定で設定されます。", "tweakeroo.config.feature_toggle.comment.tweakFastRightClick": "使用ボタン（右クリック）を押している間の\n自動高速右クリックを有効にします。\nティックあたりのクリック数はGeneric設定で設定されます。", "tweakeroo.config.feature_toggle.comment.tweakFillCloneLimit": "シングルプレイヤーでの/fillと/cloneコマンドの\nブロック制限の上書きを有効にします。\n新しい制限はGeneric設定の\n'fillCloneLimit'設定値で設定できます", "tweakeroo.config.feature_toggle.comment.tweakFlexibleBlockPlacement": "それらのモード用のホットキーを押している間、\n異なる向きやオフセットでブロックを配置できます。", "tweakeroo.config.feature_toggle.comment.tweakFlySpeed": "クリエイティブまたはスペクテイターモードでの飛行速度の上書きと\nいくつかのプリセットの使用を有効にします", "tweakeroo.config.feature_toggle.comment.tweakFreeCamera": "スペクテイターモードに似たフリーカメラモードを有効にしますが、\nプレイヤーは最初にフリーカメラモードを\nアクティベートした場所に留まります", "tweakeroo.config.feature_toggle.comment.tweakGammaOverride": "ビデオ設定のガンマ値を\nGeneric設定で設定された値で上書きします", "tweakeroo.config.feature_toggle.comment.tweakHandRestock": "前のスタックがなくなった時に\nメインハンドまたはオフハンドに新しいスタックを交換します", "tweakeroo.config.feature_toggle.comment.tweakHangableEntityBypass": "掛けられるエンティティ（アイテムフレームと絵画）をターゲットしないようにします。\nGeneric -> hangableEntityBypassInverseオプションを使用して、\nエンティティをターゲットできるようにするためにスニークする必要があるか、\nスニークしない必要があるかを制御できます。", "tweakeroo.config.feature_toggle.comment.tweakHoldAttack": "攻撃ボタンを押し続けることをエミュレートします", "tweakeroo.config.feature_toggle.comment.tweakHoldUse": "使用ボタンを押し続けることをエミュレートします", "tweakeroo.config.feature_toggle.comment.tweakHotbarScroll": "スクロールによるホットバー交換機能を有効にします", "tweakeroo.config.feature_toggle.comment.tweakHotbarSlotCycle": "各ブロック配置後に選択されたホットバースロットを\n設定された最大スロット番号まで循環させます。\n値を素早く調整するには、調整トグルキーバインドを\n押しながらスクロールしてください。", "tweakeroo.config.feature_toggle.comment.tweakHotbarSlotRandomizer": "各ブロック配置後に選択されたホットバースロットを\n設定された最大スロット番号までランダム化します。\n値を素早く調整するには、調整トグルキーバインドを\n押しながらスクロールしてください。", "tweakeroo.config.feature_toggle.comment.tweakHotbarSwap": "ホットキーによるホットバー交換機能を有効にします", "tweakeroo.config.feature_toggle.comment.tweakInventoryPreview": "インベントリを持つブロックまたはエンティティにカーソルを合わせて\n設定されたホットキーを押している間、\nインベントリプレビューを有効にします。\n§61.21+ MiniHUDバージョンを優先して非推奨", "tweakeroo.config.feature_toggle.comment.tweakItemUnstackingProtection": "有効にすると、Lists -> unstackingItemsで設定されたアイテムが\n使用時にこぼれ出ることを防ぎます。\nこれは例えば、バケツを溶岩に投げ込むことを\n防ぐためのものです。", "tweakeroo.config.feature_toggle.comment.tweakLavaVisibility": "有効にすると、水中呼吸と水中採掘のエンチャントレベル、\nおよび火炎耐性効果がアクティブであることで、\n溶岩の下での視認性が大幅に向上します。", "tweakeroo.config.feature_toggle.comment.tweakMapPreview": "有効にすると、インベントリ内の地図にシフトを押しながら\nカーソルを合わせると地図のプレビューがレンダリングされます", "tweakeroo.config.feature_toggle.comment.tweakMovementKeysLast": "有効にすると、反対の移動キーが互いにキャンセルされず、\n代わりに最後に押されたキーがアクティブな入力になります。", "tweakeroo.config.feature_toggle.comment.tweakPeriodicAttack": "定期的な攻撃（左クリック）を有効にします\nGeneric -> periodicAttackIntervalで間隔を設定してください", "tweakeroo.config.feature_toggle.comment.tweakPeriodicHoldAttack": "設定可能な時間だけ定期的に攻撃を保持することを有効にします。\nGeneric -> periodicHoldAttackIntervalで間隔を設定し、\nperiodicHoldAttackDurationで持続時間を設定してください\n§6注意：通常のホールド攻撃や定期攻撃を\n§6同時に使用しないでください", "tweakeroo.config.feature_toggle.comment.tweakPeriodicHoldUse": "設定可能な時間だけ定期的に使用を保持することを有効にします。\nGeneric -> periodicHoldUseIntervalで間隔を設定し、\nperiodicHoldUseDurationで持続時間を設定してください\n§6注意：通常のホールド使用や定期使用を\n§6同時に使用しないでください", "tweakeroo.config.feature_toggle.comment.tweakPeriodicUse": "定期的な使用（右クリック）を有効にします\nGeneric -> periodicUseIntervalで間隔を設定してください", "tweakeroo.config.feature_toggle.comment.tweakPermanentSneak": "有効にすると、プレイヤーは常にスニークします", "tweakeroo.config.feature_toggle.comment.tweakPermanentSprint": "有効にすると、プレイヤーは前進時に常にスプリントします", "tweakeroo.config.feature_toggle.comment.tweakPickBeforePlace": "有効にすると、各ブロック配置前に、配置しようとしている\n同じブロックが手に切り替わります", "tweakeroo.config.feature_toggle.comment.tweakPlacementGrid": "有効にすると、設定可能な間隔で\nグリッドパターンでのみブロックを配置できます。\n値を素早く調整するには、調整トグルキーバインドを\n押しながらスクロールしてください。", "tweakeroo.config.feature_toggle.comment.tweakPlacementLimit": "有効にすると、使用/右クリックあたり\n設定された数のブロックのみ配置できます。\n値を素早く調整するには、調整トグルキーバインドを\n押しながらスクロールしてください。", "tweakeroo.config.feature_toggle.comment.tweakPlacementRestriction": "配置制限モードを有効にします\n（平面、レイヤー、面、列、線、対角線）", "tweakeroo.config.feature_toggle.comment.tweakPlacementRestrictionFirst": "ブロック配置を制限し、最初にクリックした\n同じブロックタイプに対してのみ\nブロックを配置できるようにします", "tweakeroo.config.feature_toggle.comment.tweakPlacementRestrictionHand": "ブロック配置を制限し、手に持っている\n同じブロックタイプに対してのみ\nブロックを配置できるようにします", "tweakeroo.config.feature_toggle.comment.tweakPlayerInventoryPeek": "設定されたホットキーを押している間、\nプレイヤーインベントリのピーク/プレビューを有効にします。", "tweakeroo.config.feature_toggle.comment.tweakPlayerListAlwaysVisible": "有効にすると、キー（デフォルトではタブ）を\n押し続けることなく、プレイヤーリストが常にレンダリングされます", "tweakeroo.config.feature_toggle.comment.tweakPotionWarning": "非環境ポーション効果が切れそうになった時に\nホットバーに警告メッセージを表示します", "tweakeroo.config.feature_toggle.comment.tweakPrintDeathCoordinates": "死亡時にプレイヤーの座標をチャットに表示することを有効にします。\nこの機能は元々nessieのusefulmodからのものです。", "tweakeroo.config.feature_toggle.comment.tweakRenderEdgeChunks": "最端のクライアント読み込みチャンクのレンダリングを許可します。\nバニラでは隣接するすべてのチャンクが読み込まれていない\nチャンクのレンダリングを許可しないため、\nクライアントの読み込み済みの最端チャンクはバニラではレンダリングされません。\n§lこれはフリーカメラモードでも非常に役立ちます！§r", "tweakeroo.config.feature_toggle.comment.tweakRenderInvisibleEntities": "有効にすると、透明なエンティティが\nスペクテイターモードのようにレンダリングされます。", "tweakeroo.config.feature_toggle.comment.tweakRenderLimitEntities": "フレームごとにレンダリングする特定タイプのエンティティ数の\n制限を有効にします。現在XPオーブとアイテムエンティティが\nサポートされています。制限についてはGeneric設定を参照してください。", "tweakeroo.config.feature_toggle.comment.tweakRepairMode": "有効にすると、手に持った完全に修理されたアイテムが\n修繕エンチャントが付いた損傷したアイテムと交換されます。", "tweakeroo.config.feature_toggle.comment.tweakSculkPulseLength": "スカルクセンサーのパルス長を変更できます。Generic -> sculkSensorPulseLengthでパルス長を設定してください", "tweakeroo.config.feature_toggle.comment.tweakSelectiveBlocksRenderOutline": "リストされたブロックにアウトラインをレンダリングします\nTweakForkのAndrew54757によって作成", "tweakeroo.config.feature_toggle.comment.tweakSelectiveBlocksRendering": "選択的に表示されるブロックレンダリングを有効にします\nTweakForkのAndrew54757によって作成", "tweakeroo.config.feature_toggle.comment.tweakServerDataSync": "シュルカーボックスなどのエンティティにサーバーデータシンカーを使用し、\nServuxなどでサーバー上でinventoryPreviewが動作するようにします。\n§6このオプションがtrueに設定されていても、シンカーが動作するには\n§6サーバーのオペレーターであるか、サーバーサイドmodを\n§6インストールする必要があります。", "tweakeroo.config.feature_toggle.comment.tweakServerDataSyncBackup": "サーバーデータシンカーは、Servuxが利用できない時に\nバニラNbtQueryRequestパケットを使用するよう設定できます", "tweakeroo.config.feature_toggle.comment.tweakShulkerBoxDisplay": "インベントリ内でシュルカーボックスにカーソルを合わせて\nシフトを押している時のシュルカーボックス内容表示を有効にします", "tweakeroo.config.feature_toggle.comment.tweakSignCopy": "有効にすると、配置された看板は\n前に配置された看板のテキストを使用します。\ntweakNoSignGuiと組み合わせて、最初の看板を作成後に\nその調整を有効にすることで、看板のコピーを素早く配置できます。", "tweakeroo.config.feature_toggle.comment.tweakSnapAim": "スナップエイム調整を有効にし、プレイヤーを事前設定された正確なヨー回転に向かせます", "tweakeroo.config.feature_toggle.comment.tweakSnapAimLock": "スナップエイムロックを有効にし、ヨーおよび/またはピッチ回転を\n現在スナップされた値にロックします", "tweakeroo.config.feature_toggle.comment.tweakSneak_1_15_2": "1.15.2のスニーク動作を復元します", "tweakeroo.config.feature_toggle.comment.tweakSpectatorTeleport": "スペクテイターが他のスペクテイターにテレポートできるようにします。\nこれは元々nessieのusefulmodからのものです。", "tweakeroo.config.feature_toggle.comment.tweakStructureBlockLimit": "ストラクチャーブロック制限の上書きを許可します。\n新しい制限はGeneric -> structureBlockMaxSizeで設定されます", "tweakeroo.config.feature_toggle.comment.tweakSwapAlmostBrokenTools": "有効にすると、手に持った壊れそうな耐久性のあるアイテムが\n新しいものと交換されます", "tweakeroo.config.feature_toggle.comment.tweakTabCompleteCoordinate": "有効にすると、ブロックを見ていない時の座標のタブ補完で\n~文字を追加する代わりにプレイヤーの位置を使用します。", "tweakeroo.config.feature_toggle.comment.tweakToolSwitch": "ターゲットブロックに対する効果的なツールへの自動切り替えを有効にします", "tweakeroo.config.feature_toggle.comment.tweakWaterVisibility": "有効にすると、水中呼吸と水中採掘のエンチャントレベルにより、\n水中での視認性が大幅に向上します。", "tweakeroo.config.feature_toggle.comment.tweakWeaponSwitch": "ターゲットエンティティに対する武器への自動切り替えを有効にします", "tweakeroo.config.feature_toggle.comment.tweakYMirror": "ブロック境界内でターゲットのy位置をミラーリングします。\nこれは基本的に、例えば別のハーフブロックに対して配置する必要がある場合に、\nハーフブロックや階段を通常とは逆の上/下状態で配置するためのものです。", "tweakeroo.config.feature_toggle.comment.tweakZoom": "ズームホットキーを使用してズームインすることを有効にします", "tweakeroo.config.feature_toggle.name.tweakAccurateBlockPlacement": "正確ブロック配置", "tweakeroo.config.feature_toggle.name.tweakAfterClicker": "アフタークリッカー", "tweakeroo.config.feature_toggle.name.tweakAimLock": "エイムロック", "tweakeroo.config.feature_toggle.name.tweakAngelBlock": "エンジェルブロック", "tweakeroo.config.feature_toggle.name.tweakAreaSelector": "エリアセレクター", "tweakeroo.config.feature_toggle.name.tweakAutoSwitchElytra": "エリトラ自動切替", "tweakeroo.config.feature_toggle.name.tweakBlockReachOverride": "§6ブロックリーチ上書き§r", "tweakeroo.config.feature_toggle.name.tweakBlockTypeBreakRestriction": "ブロックタイプ破壊制限", "tweakeroo.config.feature_toggle.name.tweakBreakingGrid": "破壊グリッド", "tweakeroo.config.feature_toggle.name.tweakBreakingRestriction": "破壊制限", "tweakeroo.config.feature_toggle.name.tweakBundleDisplay": "バンドル表示", "tweakeroo.config.feature_toggle.name.tweakChatBackgroundColor": "チャット背景色", "tweakeroo.config.feature_toggle.name.tweakChatPersistentText": "チャット永続テキスト", "tweakeroo.config.feature_toggle.name.tweakChatTimestamp": "チャットタイムスタンプ", "tweakeroo.config.feature_toggle.name.tweakCommandBlockExtraFields": "コマンドブロック追加フィールド", "tweakeroo.config.feature_toggle.name.tweakCreativeExtraItems": "クリエイティブ追加アイテム", "tweakeroo.config.feature_toggle.name.tweakCustomFlatPresets": "カスタムフラットプリセット", "tweakeroo.config.feature_toggle.name.tweakCustomFlyDeceleration": "カスタム飛行減速", "tweakeroo.config.feature_toggle.name.tweakCustomInventoryScreenScale": "カスタムインベントリ画面スケール", "tweakeroo.config.feature_toggle.name.tweakDarknessVisibility": "暗闇視認性", "tweakeroo.config.feature_toggle.name.tweakElytraCamera": "エリトラカメラ", "tweakeroo.config.feature_toggle.name.tweakEmptyShulkerBoxesStack": "§6空シュルカーボックススタック§r", "tweakeroo.config.feature_toggle.name.tweakEntityReachOverride": "§6エンティティリーチ上書き§r", "tweakeroo.config.feature_toggle.name.tweakEntityTypeAttackRestriction": "エンティティタイプ攻撃制限", "tweakeroo.config.feature_toggle.name.tweakExplosionReducedParticles": "爆発パーティクル削減", "tweakeroo.config.feature_toggle.name.tweakF3Cursor": "F3カーソル", "tweakeroo.config.feature_toggle.name.tweakFakeSneakPlacement": "偽スニーク配置", "tweakeroo.config.feature_toggle.name.tweakFakeSneaking": "偽スニーク", "tweakeroo.config.feature_toggle.name.tweakFastBlockPlacement": "高速ブロック配置", "tweakeroo.config.feature_toggle.name.tweakFastLeftClick": "高速左クリック", "tweakeroo.config.feature_toggle.name.tweakFastRightClick": "高速右クリック", "tweakeroo.config.feature_toggle.name.tweakFillCloneLimit": "§6Fill Clone制限§r", "tweakeroo.config.feature_toggle.name.tweakFlexibleBlockPlacement": "フレキシブルブロック配置", "tweakeroo.config.feature_toggle.name.tweakFlySpeed": "飛行速度", "tweakeroo.config.feature_toggle.name.tweakFreeCamera": "フリーカメラ", "tweakeroo.config.feature_toggle.name.tweakGammaOverride": "ガンマ上書き", "tweakeroo.config.feature_toggle.name.tweakHandRestock": "ハンド補充", "tweakeroo.config.feature_toggle.name.tweakHangableEntityBypass": "掛けられるエンティティバイパス", "tweakeroo.config.feature_toggle.name.tweakHoldAttack": "攻撃ホールド", "tweakeroo.config.feature_toggle.name.tweakHoldUse": "使用ホールド", "tweakeroo.config.feature_toggle.name.tweakHotbarScroll": "ホットバースクロール", "tweakeroo.config.feature_toggle.name.tweakHotbarSlotCycle": "ホットバースロット循環", "tweakeroo.config.feature_toggle.name.tweakHotbarSlotRandomizer": "ホットバースロットランダム化", "tweakeroo.config.feature_toggle.name.tweakHotbarSwap": "ホットバー交換", "tweakeroo.config.feature_toggle.name.tweakInventoryPreview": "§6インベントリプレビュー§r", "tweakeroo.config.feature_toggle.name.tweakItemUnstackingProtection": "アイテムアンスタック保護", "tweakeroo.config.feature_toggle.name.tweakLavaVisibility": "溶岩視認性", "tweakeroo.config.feature_toggle.name.tweakMapPreview": "地図プレビュー", "tweakeroo.config.feature_toggle.name.tweakMovementKeysLast": "移動キー最後", "tweakeroo.config.feature_toggle.name.tweakPeriodicAttack": "定期攻撃", "tweakeroo.config.feature_toggle.name.tweakPeriodicHoldAttack": "定期攻撃ホールド", "tweakeroo.config.feature_toggle.name.tweakPeriodicHoldUse": "定期使用ホールド", "tweakeroo.config.feature_toggle.name.tweakPeriodicUse": "定期使用", "tweakeroo.config.feature_toggle.name.tweakPermanentSneak": "永続スニーク", "tweakeroo.config.feature_toggle.name.tweakPermanentSprint": "永続スプリント", "tweakeroo.config.feature_toggle.name.tweakPickBeforePlace": "配置前ピック", "tweakeroo.config.feature_toggle.name.tweakPlacementGrid": "配置グリッド", "tweakeroo.config.feature_toggle.name.tweakPlacementLimit": "配置制限", "tweakeroo.config.feature_toggle.name.tweakPlacementRestriction": "配置制限", "tweakeroo.config.feature_toggle.name.tweakPlacementRestrictionFirst": "配置制限最初", "tweakeroo.config.feature_toggle.name.tweakPlacementRestrictionHand": "配置制限ハンド", "tweakeroo.config.feature_toggle.name.tweakPlayerInventoryPeek": "プレイヤーインベントリピーク", "tweakeroo.config.feature_toggle.name.tweakPlayerListAlwaysVisible": "プレイヤーリスト常時表示", "tweakeroo.config.feature_toggle.name.tweakPotionWarning": "ポーション警告", "tweakeroo.config.feature_toggle.name.tweakPrintDeathCoordinates": "死亡座標表示", "tweakeroo.config.feature_toggle.name.tweakRenderEdgeChunks": "エッジチャンクレンダリング", "tweakeroo.config.feature_toggle.name.tweakRenderInvisibleEntities": "透明エンティティレンダリング", "tweakeroo.config.feature_toggle.name.tweakRenderLimitEntities": "エンティティレンダリング制限", "tweakeroo.config.feature_toggle.name.tweakRepairMode": "修理モード", "tweakeroo.config.feature_toggle.name.tweakSculkPulseLength": "§6スカルクパルス長§r", "tweakeroo.config.feature_toggle.name.tweakSelectiveBlocksRenderOutline": "選択ブロックレンダリングアウトライン", "tweakeroo.config.feature_toggle.name.tweakSelectiveBlocksRendering": "選択ブロックレンダリング", "tweakeroo.config.feature_toggle.name.tweakServerDataSync": "サーバーデータ同期", "tweakeroo.config.feature_toggle.name.tweakServerDataSyncBackup": "サーバーデータ同期バックアップ", "tweakeroo.config.feature_toggle.name.tweakShulkerBoxDisplay": "シュルカーボックス表示", "tweakeroo.config.feature_toggle.name.tweakSignCopy": "看板コピー", "tweakeroo.config.feature_toggle.name.tweakSnapAim": "スナップエイム", "tweakeroo.config.feature_toggle.name.tweakSnapAimLock": "スナップエイムロック", "tweakeroo.config.feature_toggle.name.tweakSneak_1_15_2": "スニーク1.15.2", "tweakeroo.config.feature_toggle.name.tweakSpectatorTeleport": "スペクテイターテレポート", "tweakeroo.config.feature_toggle.name.tweakStructureBlockLimit": "§6ストラクチャーブロック制限§r", "tweakeroo.config.feature_toggle.name.tweakSwapAlmostBrokenTools": "破損寸前ツール交換", "tweakeroo.config.feature_toggle.name.tweakTabCompleteCoordinate": "タブ補完座標", "tweakeroo.config.feature_toggle.name.tweakToolSwitch": "ツール切替", "tweakeroo.config.feature_toggle.name.tweakWaterVisibility": "水中視認性", "tweakeroo.config.feature_toggle.name.tweakWeaponSwitch": "武器切替", "tweakeroo.config.feature_toggle.name.tweakYMirror": "Yミラー", "tweakeroo.config.feature_toggle.name.tweakZoom": "ズーム", "tweakeroo.config.feature_toggle.prettyName.tweakAccurateBlockPlacement": "正確ブロック配置", "tweakeroo.config.feature_toggle.prettyName.tweakAfterClicker": "アフタークリッカー", "tweakeroo.config.feature_toggle.prettyName.tweakAimLock": "エイムロック", "tweakeroo.config.feature_toggle.prettyName.tweakAngelBlock": "エンジェルブロック", "tweakeroo.config.feature_toggle.prettyName.tweakAreaSelector": "エリアセレクター", "tweakeroo.config.feature_toggle.prettyName.tweakAutoSwitchElytra": "エリトラ自動切替", "tweakeroo.config.feature_toggle.prettyName.tweakBlockReachOverride": "ブロックリーチ上書き", "tweakeroo.config.feature_toggle.prettyName.tweakBlockTypeBreakRestriction": "ブロックタイプ破壊制限", "tweakeroo.config.feature_toggle.prettyName.tweakBreakingGrid": "破壊グリッド", "tweakeroo.config.feature_toggle.prettyName.tweakBreakingRestriction": "破壊制限", "tweakeroo.config.feature_toggle.prettyName.tweakBundleDisplay": "バンドル表示", "tweakeroo.config.feature_toggle.prettyName.tweakChatBackgroundColor": "チャット背景色", "tweakeroo.config.feature_toggle.prettyName.tweakChatPersistentText": "チャット永続テキスト", "tweakeroo.config.feature_toggle.prettyName.tweakChatTimestamp": "チャットタイムスタンプ", "tweakeroo.config.feature_toggle.prettyName.tweakCommandBlockExtraFields": "コマンドブロック追加フィールド", "tweakeroo.config.feature_toggle.prettyName.tweakCreativeExtraItems": "クリエイティブ追加アイテム", "tweakeroo.config.feature_toggle.prettyName.tweakCustomFlatPresets": "カスタムフラットプリセット", "tweakeroo.config.feature_toggle.prettyName.tweakCustomFlyDeceleration": "カスタム飛行減速", "tweakeroo.config.feature_toggle.prettyName.tweakCustomInventoryScreenScale": "カスタムインベントリ画面スケール", "tweakeroo.config.feature_toggle.prettyName.tweakDarknessVisibility": "暗闇視認性", "tweakeroo.config.feature_toggle.prettyName.tweakElytraCamera": "エリトラカメラ", "tweakeroo.config.feature_toggle.prettyName.tweakEmptyShulkerBoxesStack": "空シュルカーボックススタック", "tweakeroo.config.feature_toggle.prettyName.tweakEntityReachOverride": "エンティティリーチ上書き", "tweakeroo.config.feature_toggle.prettyName.tweakEntityTypeAttackRestriction": "エンティティタイプ攻撃制限", "tweakeroo.config.feature_toggle.prettyName.tweakExplosionReducedParticles": "爆発パーティクル削減", "tweakeroo.config.feature_toggle.prettyName.tweakF3Cursor": "F3カーソル", "tweakeroo.config.feature_toggle.prettyName.tweakFakeSneakPlacement": "偽スニーク配置", "tweakeroo.config.feature_toggle.prettyName.tweakFakeSneaking": "偽スニーク", "tweakeroo.config.feature_toggle.prettyName.tweakFastBlockPlacement": "高速ブロック配置", "tweakeroo.config.feature_toggle.prettyName.tweakFastLeftClick": "高速左クリック", "tweakeroo.config.feature_toggle.prettyName.tweakFastRightClick": "高速右クリック", "tweakeroo.config.feature_toggle.prettyName.tweakFillCloneLimit": "Fill Clone制限", "tweakeroo.config.feature_toggle.prettyName.tweakFlexibleBlockPlacement": "フレキシブルブロック配置", "tweakeroo.config.feature_toggle.prettyName.tweakFlySpeed": "飛行速度", "tweakeroo.config.feature_toggle.prettyName.tweakFreeCamera": "フリーカメラ", "tweakeroo.config.feature_toggle.prettyName.tweakGammaOverride": "ガンマ上書き", "tweakeroo.config.feature_toggle.prettyName.tweakHandRestock": "ハンド補充", "tweakeroo.config.feature_toggle.prettyName.tweakHangableEntityBypass": "掛けられるエンティティバイパス", "tweakeroo.config.feature_toggle.prettyName.tweakHoldAttack": "攻撃ホールド", "tweakeroo.config.feature_toggle.prettyName.tweakHoldUse": "使用ホールド", "tweakeroo.config.feature_toggle.prettyName.tweakHotbarScroll": "ホットバースクロール", "tweakeroo.config.feature_toggle.prettyName.tweakHotbarSlotCycle": "ホットバースロット循環", "tweakeroo.config.feature_toggle.prettyName.tweakHotbarSlotRandomizer": "ホットバースロットランダム化", "tweakeroo.config.feature_toggle.prettyName.tweakHotbarSwap": "ホットバー交換", "tweakeroo.config.feature_toggle.prettyName.tweakInventoryPreview": "インベントリプレビュー", "tweakeroo.config.feature_toggle.prettyName.tweakItemUnstackingProtection": "アイテムアンスタック保護", "tweakeroo.config.feature_toggle.prettyName.tweakLavaVisibility": "溶岩視認性", "tweakeroo.config.feature_toggle.prettyName.tweakMapPreview": "地図プレビュー", "tweakeroo.config.feature_toggle.prettyName.tweakMovementKeysLast": "移動キー最後", "tweakeroo.config.feature_toggle.prettyName.tweakPeriodicAttack": "定期攻撃", "tweakeroo.config.feature_toggle.prettyName.tweakPeriodicHoldAttack": "定期攻撃ホールド", "tweakeroo.config.feature_toggle.prettyName.tweakPeriodicHoldUse": "定期使用ホールド", "tweakeroo.config.feature_toggle.prettyName.tweakPeriodicUse": "定期使用", "tweakeroo.config.feature_toggle.prettyName.tweakPermanentSneak": "永続スニーク", "tweakeroo.config.feature_toggle.prettyName.tweakPermanentSprint": "永続スプリント", "tweakeroo.config.feature_toggle.prettyName.tweakPickBeforePlace": "配置前ピック", "tweakeroo.config.feature_toggle.prettyName.tweakPlacementGrid": "配置グリッド", "tweakeroo.config.feature_toggle.prettyName.tweakPlacementLimit": "配置制限", "tweakeroo.config.feature_toggle.prettyName.tweakPlacementRestriction": "配置制限", "tweakeroo.config.feature_toggle.prettyName.tweakPlacementRestrictionFirst": "配置制限最初", "tweakeroo.config.feature_toggle.prettyName.tweakPlacementRestrictionHand": "配置制限ハンド", "tweakeroo.config.feature_toggle.prettyName.tweakPlayerInventoryPeek": "プレイヤーインベントリピーク", "tweakeroo.config.feature_toggle.prettyName.tweakPlayerListAlwaysVisible": "プレイヤーリスト常時表示", "tweakeroo.config.feature_toggle.prettyName.tweakPotionWarning": "ポーション警告", "tweakeroo.config.feature_toggle.prettyName.tweakPrintDeathCoordinates": "死亡座標表示", "tweakeroo.config.feature_toggle.prettyName.tweakRenderEdgeChunks": "エッジチャンクレンダリング", "tweakeroo.config.feature_toggle.prettyName.tweakRenderInvisibleEntities": "透明エンティティレンダリング", "tweakeroo.config.feature_toggle.prettyName.tweakRenderLimitEntities": "エンティティレンダリング制限", "tweakeroo.config.feature_toggle.prettyName.tweakRepairMode": "修理モード", "tweakeroo.config.feature_toggle.prettyName.tweakSculkPulseLength": "スカルクパルス長", "tweakeroo.config.feature_toggle.prettyName.tweakSelectiveBlocksRenderOutline": "選択ブロックレンダリングアウトライン", "tweakeroo.config.feature_toggle.prettyName.tweakSelectiveBlocksRendering": "選択ブロックレンダリング", "tweakeroo.config.feature_toggle.prettyName.tweakServerDataSync": "サーバーデータ同期", "tweakeroo.config.feature_toggle.prettyName.tweakServerDataSyncBackup": "サーバーデータ同期バックアップ", "tweakeroo.config.feature_toggle.prettyName.tweakShulkerBoxDisplay": "シュルカーボックス表示", "tweakeroo.config.feature_toggle.prettyName.tweakSignCopy": "看板コピー", "tweakeroo.config.feature_toggle.prettyName.tweakSnapAim": "スナップエイム", "tweakeroo.config.feature_toggle.prettyName.tweakSnapAimLock": "スナップエイムロック", "tweakeroo.config.feature_toggle.prettyName.tweakSneak_1_15_2": "1.15.2スニーク", "tweakeroo.config.feature_toggle.prettyName.tweakSpectatorTeleport": "スペクテイターテレポート", "tweakeroo.config.feature_toggle.prettyName.tweakStructureBlockLimit": "ストラクチャーブロック制限", "tweakeroo.config.feature_toggle.prettyName.tweakSwapAlmostBrokenTools": "破損寸前ツール交換", "tweakeroo.config.feature_toggle.prettyName.tweakTabCompleteCoordinate": "タブ補完座標", "tweakeroo.config.feature_toggle.prettyName.tweakToolSwitch": "ツール切替", "tweakeroo.config.feature_toggle.prettyName.tweakWaterVisibility": "水中視認性", "tweakeroo.config.feature_toggle.prettyName.tweakWeaponSwitch": "武器切替", "tweakeroo.config.feature_toggle.prettyName.tweakYMirror": "Yミラー", "tweakeroo.config.feature_toggle.prettyName.tweakZoom": "カメラズーム", "tweakeroo.config.fixes.comment.elytraFix": "EarthcomputerとN<PERSON>ieによるエリトラ着地修正。\n展開修正は現在バニラにあるため、これは着地にのみ影響します。", "tweakeroo.config.fixes.comment.elytraSprintCancel": "エリトラ使用中にスプリントから外れるバニラのバグ（MC-279688）を修正します。\nこの設定は1.21.4でのみ必要で、1.21.5では修正されています。", "tweakeroo.config.fixes.comment.macHorizontalScroll": "Mac/OSXを使用している場合、hscrollmodと同じ修正/変更を適用しますが、\nmalilib系modのすべてのスクロール処理を壊すことはありません。", "tweakeroo.config.fixes.comment.ravagerClientBlockBreakFix": "ラヴェジャーがクライアント側でブロックを破壊することを修正し、\n迷惑なゴーストブロック/ブロック同期ずれを防ぎます", "tweakeroo.config.fixes.name.elytraFix": "エリトラ修正", "tweakeroo.config.fixes.name.elytraSprintCancel": "エリトラスプリントキャンセル", "tweakeroo.config.fixes.name.macHorizontalScroll": "Mac水平スクロール", "tweakeroo.config.fixes.name.ravagerClientBlockBreakFix": "ラヴェジャークライアントブロック破壊修正", "tweakeroo.config.generic.comment.accuratePlacementProtocol": "有効にすると、フレキシブルブロック配置と\n正確ブロック配置がCarpet modで実装されたプロトコルを使用します。\n§6注意：ホッパーや原木など、クリックしたブロック面のみを\n§6考慮するブロック以外のブロック回転が動作するために必要です。", "tweakeroo.config.generic.comment.accuratePlacementProtocolMode": "使用する「正確配置プロトコル」のタイプ。\n- Auto：シングルプレイヤーではv3を使用し、マルチプレイヤーでは\n  デフォルトでSlabs-onlyを使用しますが、サーバーに'carpet:hello'\n  パケットを送信するCarpet modがある場合はv2を使用します。\n- Version 3：Tweakeroo自体（シングルプレイヤー）またはServuxでサポート。\n- Version 2：Carpet modがあるサーバーと互換性があります\n  （skyrisingとDeadlyMCのQuickCarpet、\n  またはFabricCarpetに加えてCarpetExtra。\n  どちらの場合も'accurateBlockPlacement' Carpetルールを\n  サーバーで有効にする必要があります）。\n- Slabs only：上ハーフブロックのみ修正。Paperサーバーと互換性があります。\n- None：座標を変更しません。", "tweakeroo.config.generic.comment.afterClickerClickCount": "tweakAfterClickerが有効な時に\n配置されたブロックごとに行う右クリック数", "tweakeroo.config.generic.comment.angelBlockPlacementDistance": "tweakAngelBlockが有効な時に\nプレイヤーから空中にブロックを配置できる距離。\n5がサーバーが許可する最大値です。", "tweakeroo.config.generic.comment.areaSelectionUseAll": "選択に空気を含めるかどうか\nTweakForkのAndrew54757によって作成", "tweakeroo.config.generic.comment.blockReachDistance": "上書き調整が有効な場合に使用するブロックリーチ距離。\nゲームが許可する最大値は64です。\n§6サーバーで定義されたルールより\n§6[0.5 - 1.0]以上高い値で使用しないでください。", "tweakeroo.config.generic.comment.blockTypeBreakRestrictionWarn": "ブロックタイプ破壊制限機能がブロックの破壊を防ぐ時に\n表示する警告メッセージのタイプを選択します（ある場合）", "tweakeroo.config.generic.comment.breakingGridSize": "グリッド破壊モードのグリッド間隔サイズ。\n値を素早く調整するには、調整トグルキーバインドを\n押しながらスクロールしてください。", "tweakeroo.config.generic.comment.breakingRestrictionMode": "使用する破壊制限モード（ホットキー選択可能）", "tweakeroo.config.generic.comment.bundleDisplayBgColor": "バンドル表示の背景テクスチャを\nバンドルの染料色で着色/色付けを有効にします", "tweakeroo.config.generic.comment.bundleDisplayRequireShift": "バンドルプレビューにシフトを押すことが必要かどうか\n§6注意：これを無効にするとバニラバンドルツールチップが完全に無効になります。", "tweakeroo.config.generic.comment.bundleDisplayRowWidth": "バンドルプレビュー表示の行幅サイズを調整します。\nより小さいまたは大きい値はテクスチャ表示の問題を引き起こします。", "tweakeroo.config.generic.comment.chatBackgroundColor": "'tweakChatBackgroundColor'が有効な場合の\nチャットメッセージの背景色", "tweakeroo.config.generic.comment.chatTimeFormat": "tweakChatTimestampが有効な場合のチャットメッセージの時刻形式\nJava SimpleDateFormat形式指定子を使用します。", "tweakeroo.config.generic.comment.clientPlacementRotation": "シングルプレイヤーとクライアント側の配置回転を有効にします。\nCarpet modなしでシングルプレイヤーで正確配置が動作するなど", "tweakeroo.config.generic.comment.customInventoryGuiScale": "§etweakCustomInventoryScreenScale§rが有効な場合に\nインベントリ画面で使用するGUIスケール値。", "tweakeroo.config.generic.comment.debugLogging": "特定の問題やクラッシュをデバッグするために\nゲームコンソールでいくつかのデバッグログメッセージを有効にします。", "tweakeroo.config.generic.comment.darknessScaleOverrideValue": "'tweakDarknessVisibility'で使用される上書き値で、\n「暗化」画面効果を自動的に弱めます。\nこれは各効果パルスで画面が暗くなる量の\nパーセンテージ値です。", "tweakeroo.config.generic.comment.elytraCameraIndicator": "エリトラカメラモードがアクティブな時に\n実際のピッチ角インジケーターをレンダリングするかどうか", "tweakeroo.config.generic.comment.entityReachDistance": "上書き調整が有効な場合に使用するエンティティリーチ距離。\nゲームが許可する最大値は64です。\n§6サーバーで定義されたルールより\n§6[0.5 - 1.0]以上高い値で使用しないでください。", "tweakeroo.config.generic.comment.entityTypeAttackRestrictionWarn": "エンティティタイプ攻撃制限機能がエンティティの攻撃を防ぐ時に\n表示する警告メッセージのタイプを選択します（ある場合）", "tweakeroo.config.generic.comment.fastBlockPlacementCount": "高速ブロック配置調整で\nゲームティックごとに配置するブロックの最大数", "tweakeroo.config.generic.comment.fastLeftClickAllowTools": "高速左クリックがサバイバルでも\nツールアイテムを持っている間に動作することを許可します", "tweakeroo.config.generic.comment.fastLeftClickCount": "tweakFastLeftClickが有効で攻撃ボタンが押されている時に\nゲームティックごとに行う左クリック数", "tweakeroo.config.generic.comment.fastPlacementRememberOrientation": "有効にすると、高速ブロック配置機能は\n配置した最初のブロックの向きを常に記憶します。\nこれがないと、フレキシブルブロック配置が有効で\nアクティブな場合にのみ向きが記憶されます。", "tweakeroo.config.generic.comment.fastRightClickCount": "tweakFastRightClickが有効で使用ボタンが押されている時に\nゲームティックごとに行う右クリック数", "tweakeroo.config.generic.comment.fillCloneLimit": "それらを上書きする調整が有効な場合の\nシングルプレイヤーでの新しい/fillと/cloneブロック制限", "tweakeroo.config.generic.comment.flexibleBlockPlacementOverlayColor": "ブロック配置オーバーレイで現在指している\n領域の色", "tweakeroo.config.generic.comment.flyDecelerationFactor": "'customFlyDeceleration'調整が有効な場合に\nプレイヤーがどれだけ速く停止するかを調整します", "tweakeroo.config.generic.comment.flySpeedIncrement1": "増分1での飛行速度の変化量", "tweakeroo.config.generic.comment.flySpeedIncrement2": "増分2での飛行速度の変化量", "tweakeroo.config.generic.comment.flySpeedPreset1": "プリセット1の飛行速度", "tweakeroo.config.generic.comment.flySpeedPreset2": "プリセット2の飛行速度", "tweakeroo.config.generic.comment.flySpeedPreset3": "プリセット3の飛行速度", "tweakeroo.config.generic.comment.flySpeedPreset4": "プリセット4の飛行速度", "tweakeroo.config.generic.comment.freeCameraPlayerInputs": "有効にすると、フリーカメラモードでの攻撃と使用アクション\n（つまり左右クリック）が実際のプレイヤーに通されます。", "tweakeroo.config.generic.comment.freeCameraPlayerMovement": "有効にすると、フリーカメラモードでの移動入力が\nカメラの代わりに実際のクライアントプレイヤーを移動させます", "tweakeroo.config.generic.comment.gammaOverrideValue": "上書きオプションが有効な時に使用するガンマ値", "tweakeroo.config.generic.comment.handRestockPre": "有効にすると、ハンド補充が\nスタックがなくなる前に発生します", "tweakeroo.config.generic.comment.handRestockPreThreshold": "事前補充モードでハンド補充が発生する\nスタックサイズの閾値", "tweakeroo.config.generic.comment.hangableEntityBypassInverse": "hangableEntityTargetingBypass調整が有効な場合、\nこれは掛けられるエンティティ（アイテムフレームまたは絵画）を\nターゲットできるようにするために、プレイヤーがスニークする必要があるか\nスニークしない必要があるかを制御します。\n > true - スニーク = エンティティを無視/バイパス\n > false - スニーク = エンティティをターゲット", "tweakeroo.config.generic.comment.hotbarSlotCycleMax": "ホットバースロット循環調整が有効な場合に\n使用/循環する最後のホットバースロットです。\n基本的に、ここで設定された最大スロット番号を超えると\n循環は最初のスロットに戻ります。", "tweakeroo.config.generic.comment.hotbarSlotRandomizerMax": "ホットバースロットランダム化調整が有効な場合に\n使用する最後のホットバースロットです。基本的に、\nアイテム使用後に選択されたホットバースロットが\n1からこの最大スロットまでランダムに選ばれます。", "tweakeroo.config.generic.comment.hotbarSwapOverlayAlignment": "ホットバー交換オーバーレイの位置", "tweakeroo.config.generic.comment.hotbarSwapOverlayOffsetX": "ホットバー交換オーバーレイの水平オフセット", "tweakeroo.config.generic.comment.hotbarSwapOverlayOffsetY": "ホットバー交換オーバーレイの垂直オフセット", "tweakeroo.config.generic.comment.inventoryPreviewVillagerBGColor": "職業に基づく村人取引の背景色表示を\n有効/無効にします。", "tweakeroo.config.generic.comment.itemSwapDurabilityThreshold": "これは低耐久性アイテム交換機能の\n耐久性閾値（残り使用回数）です。\n総耐久性が低いアイテムはより低くなり、\n5%%残りで交換されることに注意してください。", "tweakeroo.config.generic.comment.itemUsePacketCheckBypass": "1.18.2で追加された新しい距離/座標チェックをバイパスします。\n\nそのチェックは「正確配置プロトコル」を破り、\n回転（または他のプロパティ）リクエストで配置されたブロックを\nゴーストブロックにしてしまいます。\n\n基本的にこれを無効にする必要はありません。\nそのチェックは1.18.2以前には存在しませんでした。", "tweakeroo.config.generic.comment.mapPreviewRequireShift": "地図プレビューにシフトを押すことが必要かどうか", "tweakeroo.config.generic.comment.mapPreviewSize": "レンダリングされる地図プレビューのサイズ", "tweakeroo.config.generic.comment.periodicAttackInterval": "自動攻撃（左クリック）間のゲームティック数", "tweakeroo.config.generic.comment.periodicAttackResetIntervalOnActivate": "マウススクロールホイールで調整された場合、\n無効化時に定期攻撃間隔をリセットします", "tweakeroo.config.generic.comment.periodicHoldAttackDuration": "攻撃を押し続けるゲームティック数", "tweakeroo.config.generic.comment.periodicHoldAttackInterval": "攻撃を押し始める（左クリック）間のゲームティック数", "tweakeroo.config.generic.comment.periodicHoldAttackResetIntervalOnActivate": "マウススクロールホイールで調整された場合、\n無効化時に定期ホールド攻撃間隔をリセットします", "tweakeroo.config.generic.comment.periodicHoldUseDuration": "使用を押し続けるゲームティック数", "tweakeroo.config.generic.comment.periodicHoldUseInterval": "使用を押し始める（右クリック）間のゲームティック数", "tweakeroo.config.generic.comment.periodicHoldUseResetIntervalOnActivate": "マウススクロールホイールで調整された場合、\n無効化時に定期ホールド使用間隔をリセットします", "tweakeroo.config.generic.comment.periodicUseInterval": "自動使用（右クリック）間のゲームティック数", "tweakeroo.config.generic.comment.periodicUseResetIntervalOnActivate": "マウススクロールホイールで調整された場合、\n無効化時に定期使用間隔をリセットします", "tweakeroo.config.generic.comment.permanentSneakAllowInGUIs": "trueの場合、永続スニーク調整は\nGUIが開いている間も動作します", "tweakeroo.config.generic.comment.placementGridSize": "グリッド配置モードのグリッド間隔サイズ。\n値を素早く調整するには、調整トグルキーバインドを\n押しながらスクロールしてください。", "tweakeroo.config.generic.comment.placementLimit": "tweakPlacementLimitが有効な場合に\n右クリックあたり最大で配置できるブロック数。\n値を素早く調整するには、調整トグルキーバインドを\n押しながらスクロールしてください。", "tweakeroo.config.generic.comment.placementRestrictionMode": "使用する配置制限モード（ホットキー選択可能）", "tweakeroo.config.generic.comment.placementRestrictionTiedToFast": "有効にすると、高速配置モードを切り替える時に\n配置制限モードがオン/オフ状態を切り替えます。", "tweakeroo.config.generic.comment.potionWarningBeneficialOnly": "「有益」とマークされたポーション効果がなくなることについてのみ警告します", "tweakeroo.config.generic.comment.potionWarningThreshold": "警告が表示され始めるポーション効果の\n残り持続時間（ティック単位）", "tweakeroo.config.generic.comment.rememberFlexibleFromClick": "有効にすると、使用キー（右クリック）が押されている限り、\nフレキシブルブロック配置ステータスが\n最初に配置されたブロックから記憶されます。\n基本的に、同じ向きですべてのブロックを高速配置するために\nすべてのフレキシブルアクティベーションキーを\n押し続ける必要がありません。", "tweakeroo.config.generic.comment.renderLimitItem": "フレームごとにレンダリングされるアイテムエンティティの最大数。\n通常の動作、つまりこの制限を無効にするには-1を使用してください。", "tweakeroo.config.generic.comment.renderLimitXPOrb": "フレームごとにレンダリングされるXPオーブエンティティの最大数。\n通常の動作、つまりこの制限を無効にするには-1を使用してください。", "tweakeroo.config.generic.comment.sculkSensorPulseLength": "'tweakSculkPulseLength'調整が有効な場合のスカルクセンサーのパルス長。", "tweakeroo.config.generic.comment.selectiveBlocksHideEntities": "選択ブロックレンダリングでエンティティを隠すかどうか\nTweakForkのAndrew54757によって作成", "tweakeroo.config.generic.comment.selectiveBlocksHideParticles": "選択ブロックレンダリングでパーティクルを隠すかどうか\nTweakForkのAndrew54757によって作成", "tweakeroo.config.generic.comment.selectiveBlocksNoHit": "隠されたブロックのターゲットを無効にするかどうか\nTweakForkのAndrew54757によって作成", "tweakeroo.config.generic.comment.selectiveBlocksTrackPistons": "選択ブロックレンダリングでピストンの動きを追跡するかどうか\nTweakForkのAndrew54757によって作成", "tweakeroo.config.generic.comment.serverDataSyncCacheRefresh": "秒の分数としてのキャッシュ更新値。\nこの値は、キャッシュに存在していても\nサーバーからエンティティデータの要求が\n更新されるタイミングのトリガーです。\nこれは「entityDataSyncCacheTimeout」値の\n25%以下に設定する必要があります。\n'0.05f'の値は50msまたはゲームティックごとに1回を意味します。", "tweakeroo.config.generic.comment.serverDataSyncCacheTimeout": "エンティティキャッシュがレコードを\n保持する秒単位のキャッシュタイムアウト値。\n低い値はより頻繁な更新を意味します。", "tweakeroo.config.generic.comment.serverNbtRequestRate": "サーバーエンティティデータシンカーのリクエスト率を制限します", "tweakeroo.config.generic.comment.shulkerDisplayBgColor": "シュルカーボックス表示の背景テクスチャを\nボックスの染料色で着色/色付けを有効にします", "tweakeroo.config.generic.comment.shulkerDisplayEnderChest": "シュルカーボックス表示と同様のエンダーチェスト表示を有効にします。", "tweakeroo.config.generic.comment.shulkerDisplayRequireShift": "シュルカーボックスプレビューにシフトを押すことが必要かどうか", "tweakeroo.config.generic.comment.slotSyncWorkaround": "これは、例えば高速右クリック調整で\n素早く使用されているアイテムの耐久性や\nスタックサイズをサーバーが上書きすることを防ぎます。", "tweakeroo.config.generic.comment.slotSyncWorkaroundAlways": "高速右クリックや高速ブロック配置を使用している時だけでなく、\n使用キーが押されている間は常にスロット同期回避策を有効にします。\nこれは主に、Litematicaのイージープレイスモードなど、\n使用を押し続けている時にアイテムを素早く使用する\n他のmodのためのものです。", "tweakeroo.config.generic.comment.snapAimIndicator": "スナップエイム角度インジケーターをレンダリングするかどうか", "tweakeroo.config.generic.comment.snapAimIndicatorColor": "スナップエイムインジケーター背景の色", "tweakeroo.config.generic.comment.snapAimMode": "スナップエイムモード：ヨー、ピッチ、または両方", "tweakeroo.config.generic.comment.snapAimOnlyCloseToAngle": "有効にすると、内部角度が特定の距離内にある時のみ\nスナップエイムが角度にスナップします。\n閾値はsnapAimThresholdで設定できます", "tweakeroo.config.generic.comment.snapAimPitchOvershoot": "通常の+/- 90度から+/- 180度まで\nピッチ角度のオーバーシュートを許可するかどうか", "tweakeroo.config.generic.comment.snapAimPitchStep": "スナップエイム調整のピッチ角度ステップ", "tweakeroo.config.generic.comment.snapAimThresholdPitch": "プレイヤーの回転がスナップ角度に\nスナップされる角度閾値。", "tweakeroo.config.generic.comment.snapAimThresholdYaw": "プレイヤーの回転がスナップ角度に\nスナップされる角度閾値。", "tweakeroo.config.generic.comment.snapAimYawStep": "スナップエイム調整のヨー角度ステップ", "tweakeroo.config.generic.comment.structureBlockMaxSize": "ストラクチャーブロックの保存エリアの最大寸法", "tweakeroo.config.generic.comment.toolSwapBetterEnchants": "ターゲットに使用する正しいツールかどうかを\n比較した後でのみ、ツール交換のために\nツールのエンチャントとレア度を考慮します。", "tweakeroo.config.generic.comment.toolSwapPreferSilkTouch": "他のすべての結果が等しい場合\n（ブロック破壊速度、エンチャント、材料など）、\nツールでの最終的なシルクタッチ設定を考慮します。\nこれは、採掘しているブロックに\nSilkTouchFirstが適用されない場合に\n選択するデフォルトのツルハシを決定するのに役立ちます。\n§6注意：これが無効な場合、modは比較中に\n§6すべての基準に一致するシルクタッチでないツールが\n§6見つかった場合、常にそれを使用することを優先します。", "tweakeroo.config.generic.comment.toolSwapBambooUsesSwordFirst": "ターゲットブロックが竹かどうかをチェックし、\nそうであれば斧の代わりに剣を使用することを優先します。", "tweakeroo.config.generic.comment.toolSwapNeedsShearsFirst": "羊毛、つる、クモの巣など、\nブロックがハサミを最初に必要とするかどうかをチェックします。\n選択されるブロックは、バニラには存在しないため\n新しい'§b#malilib:needs_shears§r'ブロックタグに基づきます。\n§6注意：この機能は、ブロックがバニラで\n§6適切にマッチしない場合にのみアクティブになります。", "tweakeroo.config.generic.comment.toolSwapSilkTouchFirst": "ガラスやエンダーチェストなど、\nブロックがシルクタッチを最初に必要とするかどうかをチェックします。\n選択されるブロックは、バニラには存在しないため\n新しい'§b#malilib:needs_silk_touch§r'ブロックタグに基づきます。", "tweakeroo.config.generic.comment.toolSwapSilkTouchOres": "'§b#malilib:ore_blocks§r'タグを介して\nブロックが鉱石ブロックかどうかをチェックし、\nそれにシルクタッチファーストを使用します。\n§6注意：幸運を使用するにはこれを無効にしてください。§r", "tweakeroo.config.generic.comment.toolSwapSilkTouchOverride": "SilkTouchFirstを適用するブロックの\nシルクタッチオーバーライドリストをチェックします。\n§6注意：これは'toolSwapSilkTouchFirst'が\n§6有効でなくても動作しますが、同じように動作するよう\n§6設計されており、それと共存できます。", "tweakeroo.config.generic.comment.toolSwitchIgnoredSlots": "ツール切替調整がアクティブな時に動作しないスロット。", "tweakeroo.config.generic.comment.toolSwitchableSlots": "ツール切替調整がツールを置くことを許可されているスロット。\nツール切替は、すでに優先ツールがある場合は\nホットバーの他のスロットにも切り替えることができますが、\n新しいツールはこれらのスロットにのみ交換します", "tweakeroo.config.generic.comment.weaponSwapBetterEnchants": "ターゲットに使用する正しい武器かどうかを\n比較した後でのみ、武器交換のために\n武器のエンチャントとレア度を考慮します。", "tweakeroo.config.generic.comment.zoomAdjustMouseSensitivity": "有効にすると、ズーム機能が有効でズームキーがアクティブな間\nマウス感度が減少します", "tweakeroo.config.generic.comment.zoomFov": "ズーム機能で使用されるFOV値", "tweakeroo.config.generic.comment.zoomResetFovOnActivate": "マウススクロールホイールで調整された場合、\n無効化時にカメラズームFOVをリセットします", "tweakeroo.config.generic.name.accuratePlacementProtocol": "正確配置プロトコル", "tweakeroo.config.generic.name.accuratePlacementProtocolMode": "正確配置プロトコルモード", "tweakeroo.config.generic.name.afterClickerClickCount": "アフタークリッカークリック数", "tweakeroo.config.generic.name.angelBlockPlacementDistance": "エンジェルブロック配置距離", "tweakeroo.config.generic.name.areaSelectionUseAll": "エリア選択全使用", "tweakeroo.config.generic.name.blockReachDistance": "ブロックリーチ距離", "tweakeroo.config.generic.name.blockTypeBreakRestrictionWarn": "ブロックタイプ破壊制限警告", "tweakeroo.config.generic.name.breakingGridSize": "破壊グリッドサイズ", "tweakeroo.config.generic.name.breakingRestrictionMode": "破壊制限モード", "tweakeroo.config.generic.name.bundleDisplayBgColor": "バンドル表示背景色", "tweakeroo.config.generic.name.bundleDisplayRequireShift": "バンドル表示シフト必須", "tweakeroo.config.generic.name.bundleDisplayRowWidth": "バンドル表示行幅", "tweakeroo.config.generic.name.chatBackgroundColor": "チャット背景色", "tweakeroo.config.generic.name.chatTimeFormat": "チャット時刻形式", "tweakeroo.config.generic.name.clientPlacementRotation": "クライアント配置回転", "tweakeroo.config.generic.name.customInventoryGuiScale": "カスタムインベントリGUIスケール", "tweakeroo.config.generic.name.debugLogging": "デバッグログ", "tweakeroo.config.generic.name.darknessScaleOverrideValue": "暗闇スケール上書き値", "tweakeroo.config.generic.name.elytraCameraIndicator": "エリトラカメラインジケーター", "tweakeroo.config.generic.name.entityReachDistance": "エンティティリーチ距離", "tweakeroo.config.generic.name.entityTypeAttackRestrictionWarn": "エンティティタイプ攻撃制限警告", "tweakeroo.config.generic.name.fastBlockPlacementCount": "高速ブロック配置数", "tweakeroo.config.generic.name.fastLeftClickAllowTools": "高速左クリックツール許可", "tweakeroo.config.generic.name.fastLeftClickCount": "高速左クリック数", "tweakeroo.config.generic.name.fastPlacementRememberOrientation": "高速配置向き記憶", "tweakeroo.config.generic.name.fastRightClickCount": "高速右クリック数", "tweakeroo.config.generic.name.fillCloneLimit": "Fill Clone制限", "tweakeroo.config.generic.name.flexibleBlockPlacementOverlayColor": "フレキシブルブロック配置オーバーレイ色", "tweakeroo.config.generic.name.flyDecelerationFactor": "飛行減速係数", "tweakeroo.config.generic.name.flySpeedIncrement1": "飛行速度増分1", "tweakeroo.config.generic.name.flySpeedIncrement2": "飛行速度増分2", "tweakeroo.config.generic.name.flySpeedPreset1": "飛行速度プリセット1", "tweakeroo.config.generic.name.flySpeedPreset2": "飛行速度プリセット2", "tweakeroo.config.generic.name.flySpeedPreset3": "飛行速度プリセット3", "tweakeroo.config.generic.name.flySpeedPreset4": "飛行速度プリセット4", "tweakeroo.config.generic.name.freeCameraPlayerInputs": "フリーカメラプレイヤー入力", "tweakeroo.config.generic.name.freeCameraPlayerMovement": "フリーカメラプレイヤー移動", "tweakeroo.config.generic.name.gammaOverrideValue": "ガンマ上書き値", "tweakeroo.config.generic.name.handRestockPre": "ハンド補充事前", "tweakeroo.config.generic.name.handRestockPreThreshold": "ハンド補充事前閾値", "tweakeroo.config.generic.name.hangableEntityBypassInverse": "掛けられるエンティティバイパス逆", "tweakeroo.config.generic.name.hotbarSlotCycleMax": "ホットバースロット循環最大", "tweakeroo.config.generic.name.hotbarSlotRandomizerMax": "ホットバースロットランダム化最大", "tweakeroo.config.generic.name.hotbarSwapOverlayAlignment": "ホットバー交換オーバーレイ配置", "tweakeroo.config.generic.name.hotbarSwapOverlayOffsetX": "ホットバー交換オーバーレイX座標", "tweakeroo.config.generic.name.hotbarSwapOverlayOffsetY": "ホットバー交換オーバーレイY座標", "tweakeroo.config.generic.name.inventoryPreviewVillagerBGColor": "インベントリプレビュー村人背景色", "tweakeroo.config.generic.name.itemSwapDurabilityThreshold": "アイテム交換耐久性閾値", "tweakeroo.config.generic.name.itemUsePacketCheckBypass": "アイテム使用パケットチェックバイパス", "tweakeroo.config.generic.name.mapPreviewRequireShift": "地図プレビューシフト必須", "tweakeroo.config.generic.name.mapPreviewSize": "地図プレビューサイズ", "tweakeroo.config.generic.name.periodicAttackInterval": "定期攻撃間隔", "tweakeroo.config.generic.name.periodicAttackResetIntervalOnActivate": "定期攻撃間隔アクティベート時リセット", "tweakeroo.config.generic.name.periodicHoldAttackDuration": "定期ホールド攻撃持続時間", "tweakeroo.config.generic.name.periodicHoldAttackInterval": "定期ホールド攻撃間隔", "tweakeroo.config.generic.name.periodicHoldAttackResetIntervalOnActivate": "定期ホールド攻撃間隔アクティベート時リセット", "tweakeroo.config.generic.name.periodicHoldUseDuration": "定期ホールド使用持続時間", "tweakeroo.config.generic.name.periodicHoldUseInterval": "定期ホールド使用間隔", "tweakeroo.config.generic.name.periodicHoldUseResetIntervalOnActivate": "定期ホールド使用間隔アクティベート時リセット", "tweakeroo.config.generic.name.periodicUseInterval": "定期使用間隔", "tweakeroo.config.generic.name.periodicUseResetIntervalOnActivate": "定期使用間隔アクティベート時リセット", "tweakeroo.config.generic.name.permanentSneakAllowInGUIs": "永続スニークGUI許可", "tweakeroo.config.generic.name.placementGridSize": "配置グリッドサイズ", "tweakeroo.config.generic.name.placementLimit": "配置制限", "tweakeroo.config.generic.name.placementRestrictionMode": "配置制限モード", "tweakeroo.config.generic.name.placementRestrictionTiedToFast": "配置制限高速連動", "tweakeroo.config.generic.name.potionWarningBeneficialOnly": "ポーション警告有益のみ", "tweakeroo.config.generic.name.potionWarningThreshold": "ポーション警告閾値", "tweakeroo.config.generic.name.rememberFlexibleFromClick": "クリックからフレキシブル記憶", "tweakeroo.config.generic.name.renderLimitItem": "レンダリング制限アイテム", "tweakeroo.config.generic.name.renderLimitXPOrb": "レンダリング制限XPオーブ", "tweakeroo.config.generic.name.sculkSensorPulseLength": "スカルクセンサーパルス長", "tweakeroo.config.generic.name.selectiveBlocksHideEntities": "選択ブロックエンティティ非表示", "tweakeroo.config.generic.name.selectiveBlocksHideParticles": "選択ブロックパーティクル非表示", "tweakeroo.config.generic.name.selectiveBlocksNoHit": "選択ブロックヒット無効", "tweakeroo.config.generic.name.selectiveBlocksTrackPistons": "選択ブロックピストン追跡", "tweakeroo.config.generic.name.serverDataSyncCacheRefresh": "サーバーデータ同期キャッシュ更新", "tweakeroo.config.generic.name.serverDataSyncCacheTimeout": "サーバーデータ同期キャッシュタイムアウト", "tweakeroo.config.generic.name.serverNbtRequestRate": "サーバーNBTリクエスト率", "tweakeroo.config.generic.name.shulkerDisplayBgColor": "シュルカー表示背景色", "tweakeroo.config.generic.name.shulkerDisplayEnderChest": "シュルカー表示エンダーチェスト", "tweakeroo.config.generic.name.shulkerDisplayRequireShift": "シュルカー表示シフト必須", "tweakeroo.config.generic.name.slotSyncWorkaround": "スロット同期回避策", "tweakeroo.config.generic.name.slotSyncWorkaroundAlways": "スロット同期回避策常時", "tweakeroo.config.generic.name.snapAimIndicator": "スナップエイムインジケーター", "tweakeroo.config.generic.name.snapAimIndicatorColor": "スナップエイムインジケーター色", "tweakeroo.config.generic.name.snapAimMode": "スナップエイムモード", "tweakeroo.config.generic.name.snapAimOnlyCloseToAngle": "スナップエイム角度近接のみ", "tweakeroo.config.generic.name.snapAimPitchOvershoot": "スナップエイムピッチオーバーシュート", "tweakeroo.config.generic.name.snapAimPitchStep": "スナップエイムピッチステップ", "tweakeroo.config.generic.name.snapAimThresholdPitch": "スナップエイム閾値ピッチ", "tweakeroo.config.generic.name.snapAimThresholdYaw": "スナップエイム閾値ヨー", "tweakeroo.config.generic.name.snapAimYawStep": "スナップエイムヨーステップ", "tweakeroo.config.generic.name.structureBlockMaxSize": "ストラクチャーブロック最大サイズ", "tweakeroo.config.generic.name.toolSwapBetterEnchants": "ツール交換より良いエンチャント", "tweakeroo.config.generic.name.toolSwapPreferSilkTouch": "ツール交換シルクタッチ優先", "tweakeroo.config.generic.name.toolSwapBambooUsesSwordFirst": "ツール交換竹剣優先", "tweakeroo.config.generic.name.toolSwapNeedsShearsFirst": "ツール交換ハサミ必要優先", "tweakeroo.config.generic.name.toolSwapSilkTouchFirst": "ツール交換シルクタッチ優先", "tweakeroo.config.generic.name.toolSwapSilkTouchOres": "ツール交換シルクタッチ鉱石", "tweakeroo.config.generic.name.toolSwapSilkTouchOverride": "ツール交換シルクタッチ上書き", "tweakeroo.config.generic.name.toolSwitchIgnoredSlots": "ツール切替無視スロット", "tweakeroo.config.generic.name.toolSwitchableSlots": "ツール切替可能スロット", "tweakeroo.config.generic.name.weaponSwapBetterEnchants": "武器交換より良いエンチャント", "tweakeroo.config.generic.name.zoomAdjustMouseSensitivity": "ズームマウス感度調整", "tweakeroo.config.generic.name.zoomFov": "ズームFOV", "tweakeroo.config.generic.name.zoomResetFovOnActivate": "ズームFOVアクティベート時リセット", "tweakeroo.config.generic.prettyName.freeCameraPlayerInputs": "フリーカメラプレイヤー入力", "tweakeroo.config.generic.prettyName.freeCameraPlayerMovement": "フリーカメラプレイヤー移動", "tweakeroo.config.generic.prettyName.toolSwapBetterEnchants": "ツール交換より良いエンチャント", "tweakeroo.config.generic.prettyName.toolSwapBambooUsesSwordFirst": "ツール交換竹剣優先", "tweakeroo.config.generic.prettyName.toolSwapNeedsShearsFirst": "ツール交換ハサミ必要優先", "tweakeroo.config.generic.prettyName.toolSwapSilkTouchFirst": "ツール交換シルクタッチ優先", "tweakeroo.config.generic.prettyName.toolSwapPreferSilkTouch": "ツール交換シルクタッチ選好", "tweakeroo.config.generic.prettyName.toolSwapSilkTouchOres": "ツール交換シルクタッチ鉱石", "tweakeroo.config.generic.prettyName.toolSwapSilkTouchOverride": "ツール交換シルクタッチ上書き", "tweakeroo.config.generic.prettyName.weaponSwapBetterEnchants": "武器交換より良いエンチャント", "tweakeroo.config.hotkey.comment.accurateBlockPlacementInto": "クリックしたブロック面に向かって\nブロックを配置するための正確ブロック配置\nモード/オーバーレイを有効にするキー", "tweakeroo.config.hotkey.comment.accurateBlockPlacementReverse": "通常とは逆の方向にブロックを\n配置するための正確ブロック配置\nモード/オーバーレイを有効にするキー", "tweakeroo.config.hotkey.comment.areaSelectionAddToList": "選択したブロックをリストに追加\nTweakForkのAndrew54757によって作成", "tweakeroo.config.hotkey.comment.areaSelectionOffset": "選択位置をオフセットするキー\nTweakForkのAndrew54757によって作成", "tweakeroo.config.hotkey.comment.areaSelectionRemoveFromList": "選択したブロックをリストから削除\nTweakForkのAndrew54757によって作成", "tweakeroo.config.hotkey.comment.breakingRestrictionModeColumn": "破壊制限モードを列モードに切り替えます", "tweakeroo.config.hotkey.comment.breakingRestrictionModeDiagonal": "破壊制限モードを対角線モードに切り替えます", "tweakeroo.config.hotkey.comment.breakingRestrictionModeFace": "破壊制限モードを面モードに切り替えます", "tweakeroo.config.hotkey.comment.breakingRestrictionModeLayer": "破壊制限モードをレイヤーモードに切り替えます", "tweakeroo.config.hotkey.comment.breakingRestrictionModeLine": "破壊制限モードを線モードに切り替えます", "tweakeroo.config.hotkey.comment.breakingRestrictionModePlane": "破壊制限モードを平面モードに切り替えます", "tweakeroo.config.hotkey.comment.copySignText": "既に配置された看板からテキストをコピーします。\nそのテキストはtweakSignCopy調整で使用できます。", "tweakeroo.config.hotkey.comment.elytraCamera": "現在の実際のプレイヤー回転をロックし、このキーがアクティブな間は\n入力（マウス）がレンダリングにのみ使用される別の「カメラ回転」に\n影響するようにするキー。\nエリトラで真っ直ぐ飛行しながら自由に下や周りを見回すためのものです。", "tweakeroo.config.hotkey.comment.flexibleBlockPlacementAdjacent": "隣接する位置にブロックを配置するための\nフレキシブルブロック配置モード/オーバーレイを\n有効にするキー", "tweakeroo.config.hotkey.comment.flexibleBlockPlacementOffset": "オフセットまたは対角線位置に\nブロックを配置するための\nフレキシブルブロック配置モード/オーバーレイを\n有効にするキー", "tweakeroo.config.hotkey.comment.flexibleBlockPlacementRotation": "回転/向きでブロックを配置するための\nフレキシブルブロック配置モード/オーバーレイを\n有効にするキー", "tweakeroo.config.hotkey.comment.flyIncrement1": "飛行速度を増分1で変更", "tweakeroo.config.hotkey.comment.flyIncrement2": "飛行速度を増分2で変更", "tweakeroo.config.hotkey.comment.flyPreset1": "飛行プリセット1に切り替え", "tweakeroo.config.hotkey.comment.flyPreset2": "飛行プリセット2に切り替え", "tweakeroo.config.hotkey.comment.flyPreset3": "飛行プリセット3に切り替え", "tweakeroo.config.hotkey.comment.flyPreset4": "飛行プリセット4に切り替え", "tweakeroo.config.hotkey.comment.freeCameraPlayerInputs": "Generic -> freeCameraPlayerInputsオプションを切り替え", "tweakeroo.config.hotkey.comment.freeCameraPlayerMovement": "Generic -> freeCameraPlayerMovementオプションを切り替え", "tweakeroo.config.hotkey.comment.hotbarScroll": "プレイヤーインベントリ行を通して\nホットバーをスクロールできるようにするために\n押し続けるキー", "tweakeroo.config.hotkey.comment.hotbarSwap1": "ホットバーを最上段のインベントリ行と交換", "tweakeroo.config.hotkey.comment.hotbarSwap2": "ホットバーを中段のインベントリ行と交換", "tweakeroo.config.hotkey.comment.hotbarSwap3": "ホットバーを最下段のインベントリ行と交換", "tweakeroo.config.hotkey.comment.hotbarSwapBase": "ホットバー/インベントリオーバーレイを表示するベースキー", "tweakeroo.config.hotkey.comment.inventoryPreview": "インベントリプレビュー機能を有効にするキー", "tweakeroo.config.hotkey.comment.inventoryPreviewToggleScreen": "インベントリプレビュー用の画面を開く\nマウスを使ってツールチップを見ることができます", "tweakeroo.config.hotkey.comment.openConfigGui": "ゲーム内設定GUIを開くキー", "tweakeroo.config.hotkey.comment.placementRestrictionModeColumn": "配置制限モードを列モードに切り替えます", "tweakeroo.config.hotkey.comment.placementRestrictionModeDiagonal": "配置制限モードを対角線モードに切り替えます", "tweakeroo.config.hotkey.comment.placementRestrictionModeFace": "配置制限モードを面モードに切り替えます", "tweakeroo.config.hotkey.comment.placementRestrictionModeLayer": "配置制限モードをレイヤーモードに切り替えます", "tweakeroo.config.hotkey.comment.placementRestrictionModeLine": "配置制限モードを線モードに切り替えます", "tweakeroo.config.hotkey.comment.placementRestrictionModePlane": "配置制限モードを平面モードに切り替えます", "tweakeroo.config.hotkey.comment.placementYMirror": "ブロック内でターゲットのy位置をミラーリングするキー", "tweakeroo.config.hotkey.comment.playerInventoryPeek": "プレイヤーインベントリピーク/プレビュー機能を有効にするキー", "tweakeroo.config.hotkey.comment.sitDownNearbyPets": "近くのすべてのペットを座らせます", "tweakeroo.config.hotkey.comment.skipAllRendering": "すべてのレンダリングのスキップを切り替えます", "tweakeroo.config.hotkey.comment.skipWorldRendering": "ワールドレンダリングのスキップを切り替えます", "tweakeroo.config.hotkey.comment.standUpNearbyPets": "近くのすべてのペットを立たせます", "tweakeroo.config.hotkey.comment.swapElytraChestplate": "胸スロットに装備されているアイテムをエリトラとチェストプレートの間で交換します", "tweakeroo.config.hotkey.comment.toggleAccuratePlacementProtocol": "Generic -> 'accuratePlacementProtocol'オプションの値を切り替えます", "tweakeroo.config.hotkey.comment.toggleGrabCursor": "現在の状態に応じて、マウスカーソルを取得または解放します", "tweakeroo.config.hotkey.comment.toolPick": "ターゲットブロックに対する効果的なツールに切り替えます", "tweakeroo.config.hotkey.comment.writeMapsAsImages": "現在利用可能なすべての地図を画像として\n'config/tweakeroo/map_images/<worldname>/'ディレクトリに書き出します", "tweakeroo.config.hotkey.comment.zoomActivate": "ズーム有効化ホットキー\nTweakForkのAndrew54757によって作成", "tweakeroo.config.hotkey.name.accurateBlockPlacementInto": "正確ブロック配置内向き", "tweakeroo.config.hotkey.name.accurateBlockPlacementReverse": "正確ブロック配置逆向き", "tweakeroo.config.hotkey.name.areaSelectionAddToList": "エリア選択リスト追加", "tweakeroo.config.hotkey.name.areaSelectionOffset": "エリア選択オフセット", "tweakeroo.config.hotkey.name.areaSelectionRemoveFromList": "エリア選択リスト削除", "tweakeroo.config.hotkey.name.breakingRestrictionModeColumn": "破壊制限モード列", "tweakeroo.config.hotkey.name.breakingRestrictionModeDiagonal": "破壊制限モード対角線", "tweakeroo.config.hotkey.name.breakingRestrictionModeFace": "破壊制限モード面", "tweakeroo.config.hotkey.name.breakingRestrictionModeLayer": "破壊制限モードレイヤー", "tweakeroo.config.hotkey.name.breakingRestrictionModeLine": "破壊制限モード線", "tweakeroo.config.hotkey.name.breakingRestrictionModePlane": "破壊制限モード平面", "tweakeroo.config.hotkey.name.copySignText": "看板テキストコピー", "tweakeroo.config.hotkey.name.elytraCamera": "エリトラカメラ", "tweakeroo.config.hotkey.name.flexibleBlockPlacementAdjacent": "フレキシブルブロック配置隣接", "tweakeroo.config.hotkey.name.flexibleBlockPlacementOffset": "フレキシブルブロック配置オフセット", "tweakeroo.config.hotkey.name.flexibleBlockPlacementRotation": "フレキシブルブロック配置回転", "tweakeroo.config.hotkey.name.flyIncrement1": "飛行増分1", "tweakeroo.config.hotkey.name.flyIncrement2": "飛行増分2", "tweakeroo.config.hotkey.name.flyPreset1": "飛行プリセット1", "tweakeroo.config.hotkey.name.flyPreset2": "飛行プリセット2", "tweakeroo.config.hotkey.name.flyPreset3": "飛行プリセット3", "tweakeroo.config.hotkey.name.flyPreset4": "飛行プリセット4", "tweakeroo.config.hotkey.name.freeCameraPlayerInputs": "フリーカメラプレイヤー入力", "tweakeroo.config.hotkey.name.freeCameraPlayerMovement": "フリーカメラプレイヤー移動", "tweakeroo.config.hotkey.name.hotbarScroll": "ホットバースクロール", "tweakeroo.config.hotkey.name.hotbarSwap1": "ホットバー交換1", "tweakeroo.config.hotkey.name.hotbarSwap2": "ホットバー交換2", "tweakeroo.config.hotkey.name.hotbarSwap3": "ホットバー交換3", "tweakeroo.config.hotkey.name.hotbarSwapBase": "ホットバー交換ベース", "tweakeroo.config.hotkey.name.inventoryPreview": "インベントリプレビュー", "tweakeroo.config.hotkey.name.inventoryPreviewToggleScreen": "インベントリプレビュー画面切替", "tweakeroo.config.hotkey.name.openConfigGui": "設定GUI開く", "tweakeroo.config.hotkey.name.placementRestrictionModeColumn": "配置制限モード列", "tweakeroo.config.hotkey.name.placementRestrictionModeDiagonal": "配置制限モード対角線", "tweakeroo.config.hotkey.name.placementRestrictionModeFace": "配置制限モード面", "tweakeroo.config.hotkey.name.placementRestrictionModeLayer": "配置制限モードレイヤー", "tweakeroo.config.hotkey.name.placementRestrictionModeLine": "配置制限モード線", "tweakeroo.config.hotkey.name.placementRestrictionModePlane": "配置制限モード平面", "tweakeroo.config.hotkey.name.placementYMirror": "配置Yミラー", "tweakeroo.config.hotkey.name.playerInventoryPeek": "プレイヤーインベントリピーク", "tweakeroo.config.hotkey.name.sitDownNearbyPets": "近くのペット座らせ", "tweakeroo.config.hotkey.name.skipAllRendering": "全レンダリングスキップ", "tweakeroo.config.hotkey.name.skipWorldRendering": "ワールドレンダリングスキップ", "tweakeroo.config.hotkey.name.standUpNearbyPets": "近くのペット立たせ", "tweakeroo.config.hotkey.name.swapElytraChestplate": "エリトラチェストプレート交換", "tweakeroo.config.hotkey.name.toggleAccuratePlacementProtocol": "正確配置プロトコル切替", "tweakeroo.config.hotkey.name.toggleGrabCursor": "カーソル取得切替", "tweakeroo.config.hotkey.name.toolPick": "ツールピック", "tweakeroo.config.hotkey.name.writeMapsAsImages": "地図を画像として書き出し", "tweakeroo.config.hotkey.name.zoomActivate": "ズーム有効化", "tweakeroo.config.internal.comment.darknessScaleValueOriginal": "'暗闇視認性調整'が有効になる前の元の暗闇スケール値", "tweakeroo.config.internal.comment.flySpeedPreset": "これは現在選択されている飛行速度プリセットを\nmod内部で追跡するためのものです", "tweakeroo.config.internal.comment.gammaValueOriginal": "ガンマ上書きが有効になる前の元のガンマ値", "tweakeroo.config.internal.comment.hotbarScrollCurrentRow": "これはホットバースクロール機能の\n「現在のホットバー行」をmod内部で追跡するためのものです", "tweakeroo.config.internal.comment.slimeBlockSlipperinessOriginal": "スライムブロックの元の滑りやすさ値", "tweakeroo.config.internal.comment.snapAimLastPitch": "最後にスナップしたピッチ値", "tweakeroo.config.internal.comment.snapAimLastYaw": "最後にスナップしたヨー値", "tweakeroo.config.internal.name.darknessScaleValueOriginal": "暗闇スケール値元", "tweakeroo.config.internal.name.flySpeedPreset": "飛行速度プリセット", "tweakeroo.config.internal.name.gammaValueOriginal": "ガンマ値元", "tweakeroo.config.internal.name.hotbarScrollCurrentRow": "ホットバースクロール現在行", "tweakeroo.config.internal.name.slimeBlockSlipperinessOriginal": "スライムブロック滑りやすさ元", "tweakeroo.config.internal.name.snapAimLastPitch": "スナップエイム最後ピッチ", "tweakeroo.config.internal.name.snapAimLastYaw": "スナップエイム最後ヨー", "tweakeroo.config.lists.comment.blockTypeBreakRestrictionBlackList": "ブロック破壊制限調整が有効な間に破壊が許可されないブロック。\nblockBreakRestrictionListTypeがブラックリストに設定されている場合", "tweakeroo.config.lists.comment.blockTypeBreakRestrictionListType": "ブロックタイプ破壊制限調整の制限リストタイプ", "tweakeroo.config.lists.comment.blockTypeBreakRestrictionWhiteList": "ブロック破壊制限調整が有効な間に破壊できる唯一のブロック。\nblockBreakRestrictionListTypeがホワイトリストに設定されている場合", "tweakeroo.config.lists.comment.creativeExtraItems": "クリエイティブインベントリに追加される追加アイテム。\n現在これらは交通カテゴリに表示されます。\n将来的には追加アイテムごとのグループがカスタマイズ可能になります。", "tweakeroo.config.lists.comment.entityTypeAttackRestrictionBlackList": "エンティティ攻撃制限調整が有効な間に攻撃が許可されないエンティティ。\nentityAttackRestrictionListTypeがブラックリストに設定されている場合", "tweakeroo.config.lists.comment.entityTypeAttackRestrictionListType": "エンティティタイプ攻撃制限調整の制限リストタイプ", "tweakeroo.config.lists.comment.entityTypeAttackRestrictionWhiteList": "エンティティ攻撃制限調整が有効な間に攻撃できる唯一のエンティティ。\nentityAttackRestrictionListTypeがホワイトリストに設定されている場合", "tweakeroo.config.lists.comment.entityWeaponMapping": "'tweakWeaponSwitch'調整で使用する武器のマッピング。\n他のマッピングが定義されていない場合は'<default>'が使用されます。\n'<ignore>'は武器切り替えをトリガーしません。", "tweakeroo.config.lists.comment.fastPlacementItemBlackList": "高速ブロック配置調整で使用が許可されないアイテム。\nfastPlacementItemListTypeがブラックリストに設定されている場合", "tweakeroo.config.lists.comment.fastPlacementItemListType": "高速ブロック配置調整のアイテム制限タイプ", "tweakeroo.config.lists.comment.fastPlacementItemWhiteList": "高速ブロック配置調整で使用が許可されるアイテム。\nfastPLacementItemListTypeがホワイトリストに設定されている場合", "tweakeroo.config.lists.comment.fastRightClickBlackList": "高速右クリック調整で使用が許可されないアイテム。\nfastRightClickListTypeがブラックリストに設定されている場合", "tweakeroo.config.lists.comment.fastRightClickBlockBlackList": "高速右クリック調整で右クリックが許可されないブロック。\nfastRightClickBlockListTypeがブラックリストに設定されている場合", "tweakeroo.config.lists.comment.fastRightClickBlockListType": "高速右クリック調整のターゲットブロック制限タイプ", "tweakeroo.config.lists.comment.fastRightClickBlockWhiteList": "高速右クリック調整で右クリックが許可されるブロック。\nfastRightClickBlockListTypeがホワイトリストに設定されている場合", "tweakeroo.config.lists.comment.fastRightClickListType": "高速右クリック調整のアイテム制限タイプ", "tweakeroo.config.lists.comment.fastRightClickWhiteList": "高速右クリック調整で使用が許可されるアイテム。\nfastRightClickListTypeがホワイトリストに設定されている場合", "tweakeroo.config.lists.comment.flatWorldPresets": "カスタムフラットワールドプリセット文字列。\nこれらは次の形式です：name;blocks_string;biome;generation_features;icon_item\nブロック文字列形式はバニラ形式です。例：62*minecraft:dirt,minecraft:grass\nバイオームはレジストリ名またはint IDです\nアイコンアイテム名形式はminecraft:iron_nuggetです", "tweakeroo.config.lists.comment.handRestockBlackList": "ハンド補充調整で補充が許可されないアイテム。\nhandRestockListTypeがブラックリストに設定されている場合", "tweakeroo.config.lists.comment.handRestockListType": "ハンド補充調整の制限リストタイプ", "tweakeroo.config.lists.comment.handRestockWhiteList": "ハンド補充調整で補充が許可される唯一のアイテム。\nhandRestockListTypeがホワイトリストに設定されている場合", "tweakeroo.config.lists.comment.potionWarningBlackList": "警告されないポーション効果", "tweakeroo.config.lists.comment.potionWarningListType": "ポーション警告効果のリストタイプ", "tweakeroo.config.lists.comment.potionWarningWhiteList": "警告される唯一のポーション効果", "tweakeroo.config.lists.comment.silkTouchOverride": "When using tweak tool switch, add blocks or\nblock tags to this list to apply SilkTouchFirst to,\nin addition to using the '§b#malilib:needs_silk_touch§r' Block Tag\nbased 'toolSwapSilkTouchFirst' method when\n'toolSwapSilkTouchOverride' is enabled.\n\nA useful example, would be adding\n'§bminecraft:stone§r' to this list.\n\nThe Block Tag exists, so that you don't need\nto add dozens of blocks to a list like this.\nBut; You can configure this list to work this way\nby disabling 'toolSwapSilkTouchFirst' and only enabling\n`toolSwapSilkTouchOverride` instead.\nYou may then want to make use of some of\nthe existing `§b#malilib:§r` or '§b#minecraft:§r' block tags here;\nsuch as adding '§b#malilib:glass_blocks§r', for example\ninstead of typing in all 18 glass blocks one by one.", "tweakeroo.config.lists.comment.repairModeSlots": "The slots the repair mode should use\nValid values: mainhand, offhand, head, chest, legs, feet", "tweakeroo.config.lists.comment.selectiveBlocksBlacklist": "The block positions you want to blacklist\nWritten by Andrew54757 under TweakFork", "tweakeroo.config.lists.comment.selectiveBlocksListType": "The list type for selective blocks tweak\nWritten by Andrew54757 under TweakFork", "tweakeroo.config.lists.comment.selectiveBlocksWhitelist": "The block positions you want to whitelist\nWritten by Andrew54757 under TweakFork", "tweakeroo.config.lists.comment.unstackingItems": "The items that should be considered for the\n'tweakItemUnstackingProtection' tweak", "tweakeroo.config.lists.name.blockTypeBreakRestrictionBlackList": "blockTypeBreakRestrictionBlackList", "tweakeroo.config.lists.name.blockTypeBreakRestrictionListType": "blockTypeBreakRestrictionListType", "tweakeroo.config.lists.name.blockTypeBreakRestrictionWhiteList": "blockTypeBreakRestrictionWhiteList", "tweakeroo.config.lists.name.creativeExtraItems": "creativeExtraItems", "tweakeroo.config.lists.name.entityTypeAttackRestrictionBlackList": "entityTypeAttackRestrictionBlackList", "tweakeroo.config.lists.name.entityTypeAttackRestrictionListType": "entityTypeAttackRestrictionListType", "tweakeroo.config.lists.name.entityTypeAttackRestrictionWhiteList": "entityTypeAttackRestrictionWhiteList", "tweakeroo.config.lists.name.entityWeaponMapping": "entityWeaponMapping", "tweakeroo.config.lists.name.fastPlacementItemBlackList": "fastPlacementItemBlackList", "tweakeroo.config.lists.name.fastPlacementItemListType": "fastPlacementItemListType", "tweakeroo.config.lists.name.fastPlacementItemWhiteList": "fastPlacementItemWhiteList", "tweakeroo.config.lists.name.fastRightClickBlackList": "fastRightClickBlackList", "tweakeroo.config.lists.name.fastRightClickBlockBlackList": "fastRightClickBlockBlackList", "tweakeroo.config.lists.name.fastRightClickBlockListType": "fastRightClickBlockListType", "tweakeroo.config.lists.name.fastRightClickBlockWhiteList": "fastRightClickBlockWhiteList", "tweakeroo.config.lists.name.fastRightClickListType": "fastRightClickListType", "tweakeroo.config.lists.name.fastRightClickWhiteList": "fastRightClickWhiteList", "tweakeroo.config.lists.name.flatWorldPresets": "flatWorldPresets", "tweakeroo.config.lists.name.handRestockBlackList": "handRestockBlackList", "tweakeroo.config.lists.name.handRestockListType": "handRestockListType", "tweakeroo.config.lists.name.handRestockWhiteList": "handRestockWhiteList", "tweakeroo.config.lists.name.potionWarningBlackList": "potionWarningBlackList", "tweakeroo.config.lists.name.potionWarningListType": "potionWarningListType", "tweakeroo.config.lists.name.potionWarningWhiteList": "potionWarningWhiteList", "tweakeroo.config.lists.name.silkTouchOverride": "silkTouchOverride", "tweakeroo.config.lists.name.repairModeSlots": "repairModeSlots", "tweakeroo.config.lists.name.selectiveBlocksBlacklist": "selectiveBlocksBlacklist", "tweakeroo.config.lists.name.selectiveBlocksListType": "selectiveBlocksListType", "tweakeroo.config.lists.name.selectiveBlocksWhitelist": "selective<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tweakeroo.config.lists.name.unstackingItems": "unstackingItems", "tweakeroo.gui.button.config_gui.disables": "無効化", "tweakeroo.gui.button.config_gui.fixes": "修正", "tweakeroo.gui.button.config_gui.generic": "汎用", "tweakeroo.gui.button.config_gui.generic_hotkeys": "ホットキー", "tweakeroo.gui.button.config_gui.generic_config_hotkeys": "汎用ホットキー", "tweakeroo.gui.button.config_gui.lists": "リスト", "tweakeroo.gui.button.config_gui.placement": "配置関連", "tweakeroo.gui.button.config_gui.tweaks": "調整", "tweakeroo.gui.button.misc.command_block.hover.update_execution": "ゲームティックごとに複数のトリガーを許可するかどうか", "tweakeroo.gui.button.misc.command_block.set_name": "名前を設定", "tweakeroo.gui.button.misc.command_block.update_execution.looping": "ループ", "tweakeroo.gui.button.misc.command_block.update_execution.off": "ループ: §cオフ", "tweakeroo.gui.button.misc.command_block.update_execution.on": "ループ: §aオン", "tweakeroo.gui.label.easy_place_protocol.auto": "自動", "tweakeroo.gui.label.easy_place_protocol.none": "なし", "tweakeroo.gui.label.easy_place_protocol.slabs_only": "ハーフブロックのみ", "tweakeroo.gui.label.easy_place_protocol.v2": "バージョン2", "tweakeroo.gui.label.easy_place_protocol.v3": "バージョン3", "tweakeroo.gui.title.configs": "Tweakeroo設定 - %s", "tweakeroo.hotkeys.category.disable_toggle_hotkeys": "無効化トグルホットキー", "tweakeroo.hotkeys.category.generic_hotkeys": "汎用ホットキー", "tweakeroo.hotkeys.category.tweak_toggle_hotkeys": "調整トグルホットキー", "tweakeroo.label.config_comment.single_player_only": "§6注意：この機能は完全にまたは§r\n§6少なくとも完全にシングルプレイヤーでのみ動作します§r", "tweakeroo.label.placement_restriction_mode.column": "列", "tweakeroo.label.placement_restriction_mode.diagonal": "対角線", "tweakeroo.label.placement_restriction_mode.face": "面", "tweakeroo.label.placement_restriction_mode.layer": "レイヤー", "tweakeroo.label.placement_restriction_mode.line": "線", "tweakeroo.label.placement_restriction_mode.plane": "平面", "tweakeroo.label.snap_aim_mode.both": "ヨー&ピッチ", "tweakeroo.label.snap_aim_mode.pitch": "ピッチ", "tweakeroo.label.snap_aim_mode.yaw": "ヨー", "tweakeroo.message.death_coordinates": "§6死亡しました§r @ [§b%d, %d, %d§r] in §a%s", "tweakeroo.message.focusing_game": "§6ゲームにフォーカス§f（カーソルの取得）", "tweakeroo.message.potion_effects_running_out": "§6!! 警告 - %s のポーション効果があと %s 秒で切れます !!§r", "tweakeroo.message.repair_mode.swapped_repairable_item_to_slot": "修理可能なアイテムを %s スロットに交換しました", "tweakeroo.message.set_after_clicker_count_to": "アフタークリッカー数を %s に設定しました", "tweakeroo.message.set_breaking_grid_size_to": "破壊グリッドサイズを %s に設定しました", "tweakeroo.message.set_breaking_restriction_mode_to": "破壊制限モードを %s に設定しました", "tweakeroo.message.set_fly_speed_preset_to": "飛行速度プリセット %s に切り替えました（速度: %s）", "tweakeroo.message.set_fly_speed_to": "プリセット %s の飛行速度を %s に設定しました", "tweakeroo.message.set_hotbar_slot_cycle_max_to": "ホットバースロット循環最大スロットを %s に設定しました", "tweakeroo.message.set_hotbar_slot_randomizer_max_to": "ホットバースロットランダム化最大スロットを %s に設定しました", "tweakeroo.message.set_periodic_attack_interval_to": "定期攻撃間隔を %s に設定しました", "tweakeroo.message.set_periodic_hold_attack_interval_to": "定期ホールド攻撃間隔を %s に設定しました", "tweakeroo.message.set_periodic_hold_use_interval_to": "定期ホールド使用間隔を %s に設定しました", "tweakeroo.message.set_periodic_use_interval_to": "定期使用間隔を %s に設定しました", "tweakeroo.message.set_placement_grid_size_to": "配置グリッドサイズを %s に設定しました", "tweakeroo.message.set_placement_limit_to": "配置制限を %s に設定しました", "tweakeroo.message.set_placement_restriction_mode_to": "配置制限モードを %s に設定しました", "tweakeroo.message.set_snap_aim_pitch_step_to": "スナップエイムピッチステップを %s に設定しました", "tweakeroo.message.set_snap_aim_yaw_step_to": "スナップエイムヨーステップを %s に設定しました", "tweakeroo.message.set_zoom_fov_to": "カメラズームFOVを %s に設定しました", "tweakeroo.message.sign_text_copied": "看板テキストをコピーしました", "tweakeroo.message.snapped_to_pitch": "ピッチ %s にスナップしました", "tweakeroo.message.snapped_to_yaw": "ヨー %s にスナップしました", "tweakeroo.message.swapped_low_durability_item_for_better_durability": "低耐久性アイテムをより高い耐久性のものと交換しました", "tweakeroo.message.swapped_low_durability_item_for_dummy_item": "低耐久性アイテムをダミーアイテムと交換しました", "tweakeroo.message.swapped_low_durability_item_off_players_hand": "プレイヤーの手から低耐久性アイテムを交換しました", "tweakeroo.message.toggled": "%s を %s に切り替えました", "tweakeroo.message.toggled_after_clicker_on": "アフタークリッカー調整を %s に切り替えました、クリック数: %s", "tweakeroo.message.toggled_breaking_grid_on": "破壊グリッド調整を %s に切り替えました、グリッド間隔: %s", "tweakeroo.message.toggled_fast_placement_mode_on": "高速配置モードを %s に切り替えました、モード: %s", "tweakeroo.message.toggled_fly_speed_on": "飛行速度調整を %s に切り替えました、プリセット: %s、速度: %s", "tweakeroo.message.toggled_periodic": "%s を %s に切り替えました、速度: %s", "tweakeroo.message.toggled_placement_grid_on": "配置グリッド調整を %s に切り替えました、グリッド間隔: %s", "tweakeroo.message.toggled_placement_limit_on": "配置制限調整を %s に切り替えました、制限: %s", "tweakeroo.message.toggled_slot_cycle_on": "ホットバースロット循環調整を %s に切り替えました、最大スロット: %s", "tweakeroo.message.toggled_slot_randomizer_on": "ホットバースロットランダム化調整を %s に切り替えました、最大スロット: %s", "tweakeroo.message.toggled_snap_aim_on_both": "ヨー&ピッチスナップエイムを %s に切り替えました、ヨーステップ %s、ピッチステップ %s", "tweakeroo.message.toggled_snap_aim_on_pitch": "ピッチスナップエイムを %s に切り替えました、ピッチステップサイズ %s 度", "tweakeroo.message.toggled_snap_aim_on_yaw": "ヨースナップエイムを %s に切り替えました、ヨーステップサイズ %s 度", "tweakeroo.message.toggled_zoom_activate_off": "カメラズーム無効化、FOV: %s", "tweakeroo.message.toggled_zoom_activate_on": "カメラズーム有効化、FOV: %s", "tweakeroo.message.toggled_zoom_on": "カメラズームを %s に切り替えました、FOV: %s", "tweakeroo.message.unfocusing_game": "§6ゲームのフォーカス解除§f（カーソルの解放）", "tweakeroo.message.value.off": "オフ", "tweakeroo.message.value.on": "オン", "tweakeroo.message.warning.block_type_break_restriction": "§6ブロックタイプ破壊制限調整によりブロック破壊が防がれました", "tweakeroo.message.warning.entity_type_attack_restriction": "§6エンティティタイプ攻撃制限調整によりエンティティ攻撃が防がれました"}