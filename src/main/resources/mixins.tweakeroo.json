{"required": true, "package": "fi.dy.masa.tweakeroo.mixin", "minVersion": "0.8", "compatibilityLevel": "JAVA_21", "mixins": [], "client": ["IMixinSimpleOption", "MixinMinecraftClient", "block.IMixinAbstractBlock", "block.IMixinCommandBlockExecutor", "block.IMixinPistonBlock", "block.MixinHopperBlockEntity", "block.MixinMobSpawnerLogic", "block.MixinNetherPortalBlock", "block.MixinObserverBlock", "block.MixinPistonBlock", "block.MixinScaffoldingBlock", "block.MixinSculkSensor", "block.MixinShulkerBoxBlock", "block.MixinSignBlockEntity", "block.MixinSlimeBlock", "block.MixinStructureBlockBlockEntity", "entity.MixinAbstractClientPlayerEntity", "entity.MixinBatEntity", "entity.MixinBlockAttachedEntity", "entity.MixinClientPlayerEntity", "entity.MixinEntity", "entity.MixinLivingEntity", "entity.MixinPlayerAbilities", "entity.MixinPlayerEntity", "entity.MixinRavagerEntity", "entity.MixinTradeOffer", "hud.<PERSON><PERSON><PERSON><PERSON>", "hud.<PERSON><PERSON><PERSON><PERSON>", "hud.MixinClientBossBar", "hud.MixinInGameHud", "hud.MixinStatusEffectsDisplay", "hud.MixinTeleportSpectatorMenu", "input.MixinClientCommandSource", "input.MixinCloneCommand", "input.MixinFillCommand", "input.MixinKeyboardInput", "input.MixinMouse", "item.IMixinAxeItem", "item.IMixinShovelItem", "item.MixinBlockItem", "item.MixinBundleItem", "item.MixinItem", "item.MixinItemGroup", "item.MixinItemStack", "network.MixinClientCommonNetworkHandler", "network.MixinClientPlayerInteractionManager", "network.MixinClientPlayNetworkHandler", "network.MixinDataQueryHandler", "network.MixinServerPlayNetworkHandler", "render.MixinBackgroundRenderer", "render.MixinBeaconBlockEntityRenderer", "render.MixinBlockEntityRenderDispatcher", "render.MixinCamera", "render.MixinEntityRenderDispatcher", "render.MixinExplosionEmitterParticle", "render.MixinGame<PERSON><PERSON>er", "render.MixinGameRenderer_ViewBob", "render.MixinHeldItemRenderer", "render.MixinMobSpawnerBlockEntityRenderer", "render.MixinParticleManager", "render.MixinWeatherRendering", "<PERSON>.MixinWindow", "render.MixinWorld<PERSON><PERSON>er", "screen.IMixinCustomizeFlatLevelScreen", "screen.MixinAbstractSignEditScreen", "screen.MixinChatScreen", "screen.MixinCommandBlockScreen", "screen.MixinCreativeInventoryScreen", "world.IMixinChunkLightProvider", "world.IMixinClientWorld", "world.MixinBuiltChunk", "world.MixinChunkBuilder_BuiltChunk", "world.MixinClientWorld", "world.MixinClientWorld_Properties", "world.MixinDimensionEffects_Nether", "world.MixinLightingProvider", "world.MixinServerChunkLoadingManager", "world.MixinUpdateStructureBlockC2SPacket", "world.MixinWorld"], "mixinPriority": 990, "injectors": {"defaultRequire": 0}}