package fi.dy.masa.tweakeroo.mixin.screen;

import java.util.Collections;

import net.minecraft.block.entity.CommandBlockBlockEntity;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.ingame.AbstractCommandBlockScreen;
import net.minecraft.client.gui.screen.ingame.CommandBlockScreen;
import net.minecraft.client.gui.widget.ButtonWidget;
import net.minecraft.client.gui.widget.CyclingButtonWidget;
import net.minecraft.client.gui.widget.TextFieldWidget;
import net.minecraft.text.Text;
import net.minecraft.util.math.BlockPos;
import org.spongepowered.asm.mixin.Final;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.Shadow;
import org.spongepowered.asm.mixin.Unique;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

import fi.dy.masa.malilib.render.RenderUtils;
import fi.dy.masa.malilib.util.StringUtils;
import fi.dy.masa.tweakeroo.config.FeatureToggle;
import fi.dy.masa.tweakeroo.util.MiscUtils;

@Mixin(CommandBlockScreen.class)
public abstract class MixinCommandBlockScreen extends AbstractCommandBlockScreen
{
    @Shadow @Final private CommandBlockBlockEntity blockEntity;
    @Shadow private CyclingButtonWidget<CommandBlockBlockEntity.Type> modeButton;
    @Shadow private CyclingButtonWidget<Boolean> conditionalModeButton;
    @Shadow private CyclingButtonWidget<Boolean> redstoneTriggerButton;

    @Unique private TextFieldWidget textFieldName;
    @Unique private CyclingButtonWidget<Boolean> buttonUpdateExec;
    @Unique private boolean updateExecValue;
    @Unique private String lastName = "";

    @Inject(method = "init", at = @At("RETURN"))
    private void addExtraFields(CallbackInfo ci)
    {
        if (FeatureToggle.TWEAK_COMMAND_BLOCK_EXTRA_FIELDS.getBooleanValue())
        {
            int x1 = this.width / 2 - 152;
            int x2 = x1 + 204;
            int y = 158;
            int width = 200;

            if (this.client == null || this.client.player == null)
            {
                return;
            }

            // Move the vanilla buttons a little bit tighter, otherwise the large GUI scale is a mess
            this.modeButton.setY(y);
            this.conditionalModeButton.setY(y);
            this.redstoneTriggerButton.setY(y);

            y += 46;
            this.doneButton.setY(y);
            this.cancelButton.setY(y);

            Text str = Text.translatable("tweakeroo.gui.button.misc.command_block.set_name");
            int widthBtn = this.textRenderer.getWidth(str) + 10;

            y = 181;
            this.textFieldName = new TextFieldWidget(this.textRenderer, x1, y, width, 20, Text.of(""));
            this.textFieldName.setText(this.blockEntity.getCommandExecutor().getName().getString());
            this.addSelectableChild(this.textFieldName);
            final TextFieldWidget tf = this.textFieldName;
            final BlockPos pos = this.blockEntity.getPos();

            ButtonWidget.Builder builder = ButtonWidget.builder(str, (button) ->
            {
                String name = tf.getText();
                name = String.format("{\"CustomName\":\"{\\\"text\\\":\\\"%s\\\"}\"}", name);
                this.client.player.networkHandler.sendCommand(String.format("data merge block %d %d %d %s", pos.getX(), pos.getY(), pos.getZ(), name));
            });

            builder.position(x2, y).size(widthBtn, 20);
            this.addDrawableChild(builder.build());

            this.updateExecValue = MiscUtils.getUpdateExec(this.blockEntity);

            Text strOn = Text.translatable("tweakeroo.gui.button.misc.command_block.update_execution.on");
            Text strOff = Text.translatable("tweakeroo.gui.button.misc.command_block.update_execution.off");
            Text strLooping = Text.translatable("tweakeroo.gui.button.misc.command_block.update_execution.looping");
            width = this.textRenderer.getWidth(strOff) + 10;

            this.buttonUpdateExec = CyclingButtonWidget.onOffBuilder(strOn, strOff)
                                    .omitKeyText().initially(this.updateExecValue)
                                    .build(x2 + widthBtn + 4, y, width, 20, strLooping, (button, val) ->
            {
                this.updateExecValue = val;
                MiscUtils.setUpdateExec(this.blockEntity, this.updateExecValue);

                String cmd = String.format("data merge block %d %d %d {\"UpdateLastExecution\":%s}",
                        pos.getX(), pos.getY(), pos.getZ(), this.updateExecValue ? "1b" : "0b");
                this.client.player.networkHandler.sendCommand(cmd);
            });

            this.addDrawableChild(this.buttonUpdateExec);
        }
    }

    // This is needed because otherwise the name updating is delayed by "one GUI opening" >_>
    @Override
    public void tick()
    {
        super.tick();

        if (this.textFieldName != null)
        {
            String currentName = this.blockEntity.getCommandExecutor().getName().getString();

            if (currentName.equals(this.lastName) == false)
            {
                this.textFieldName.setText(currentName);
                this.lastName = currentName;
            }
        }

        if (this.buttonUpdateExec != null)
        {
            boolean updateExec = MiscUtils.getUpdateExec(this.blockEntity);

            if (this.updateExecValue != updateExec)
            {
                this.updateExecValue = updateExec;
                Text str = getDisplayStringForCurrentStatus(this.updateExecValue);
                this.buttonUpdateExec.setMessage(str);
                this.buttonUpdateExec.setWidth(this.textRenderer.getWidth(str) + 10);
            }
        }
    }

    @Override
    public void render(DrawContext drawContext, int mouseX, int mouseY, float partialTicks)
    {
        super.render(drawContext, mouseX, mouseY, partialTicks);

        if (this.textFieldName != null)
        {
            this.textFieldName.render(drawContext, mouseX, mouseY, partialTicks);
        }

        if (this.buttonUpdateExec != null && this.buttonUpdateExec.isHovered())
        {
            String hover = "tweakeroo.gui.button.misc.command_block.hover.update_execution";
            RenderUtils.drawHoverText(mouseX, mouseY, Collections.singletonList(StringUtils.translate(hover)), drawContext);
        }
    }

    @Unique
    private static Text getDisplayStringForCurrentStatus(boolean updateExecValue)
    {
        String translationKey = "tweakeroo.gui.button.misc.command_block.update_execution";
        boolean isCurrentlyOn = ! updateExecValue;
        String strStatus = "malilib.gui.label_colored." + (isCurrentlyOn ? "on" : "off");
        return Text.translatable(translationKey, StringUtils.translate(strStatus));
    }
}
